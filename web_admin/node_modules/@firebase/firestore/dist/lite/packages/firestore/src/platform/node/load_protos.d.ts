/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import * as grpc from '@grpc/grpc-js';
import { IConversionOptions, Root } from 'protobufjs';
/** Used by tests so we can match @grpc/proto-loader behavior. */
export declare const protoLoaderOptions: IConversionOptions;
/**
 * Loads the protocol buffer definitions for Firestore.
 *
 * @returns The GrpcObject representing our protos.
 */
export declare function loadProtos(): grpc.GrpcObject;
/** Used by tests so we can directly create ProtobufJS proto message objects from JSON protos. */
export declare function loadRawProtos(): Root;
