{"version": 3, "file": "index.node.cjs", "sources": ["../app/index.ts", "../index.node.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport firebase from '@firebase/app-compat';\nimport { name, version } from '../../package.json';\n\nfirebase.registerVersion(name, version, 'app-compat');\n\nexport default firebase;\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport firebase from './app';\nimport { name, version } from '../package.json';\n\nimport './auth';\nimport './database';\nimport './firestore';\nimport './functions';\nimport './storage';\n\nfirebase.registerVersion(name, version, 'compat-node');\n\nexport default firebase;\n"], "names": ["firebase", "name", "version"], "mappings": ";;;;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;AAKHA,4BAAQ,CAAC,eAAe,CAACC,MAAI,EAAEC,SAAO,EAAE,YAAY,CAAC;;;;;ACpBrD;;;;;;;;;;;;;;;AAeG;AAWHF,4BAAQ,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,aAAa,CAAC;;;;"}