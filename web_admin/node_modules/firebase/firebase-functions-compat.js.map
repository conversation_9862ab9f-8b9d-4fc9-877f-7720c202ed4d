{"version": 3, "file": "firebase-functions-compat.js", "sources": ["../functions/src/config.ts", "../util/src/errors.ts", "../util/src/compat.ts", "../component/src/component.ts", "../functions/src/serializer.ts", "../functions/src/constants.ts", "../functions/src/error.ts", "../functions/src/context.ts", "../functions/src/service.ts", "../functions/src/api.ts", "../functions/src/index.ts", "../functions-compat/src/register.ts", "../functions-compat/src/service.ts", "../functions-compat/src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _registerComponent, registerVersion } from '@firebase/app';\nimport { FunctionsService } from './service';\nimport {\n  Component,\n  ComponentType,\n  ComponentContainer,\n  InstanceFactory\n} from '@firebase/component';\nimport { FUNCTIONS_TYPE } from './constants';\nimport { FirebaseAuthInternalName } from '@firebase/auth-interop-types';\nimport { AppCheckInternalComponentName } from '@firebase/app-check-interop-types';\nimport { MessagingInternalComponentName } from '@firebase/messaging-interop-types';\nimport { name, version } from '../package.json';\n\nconst AUTH_INTERNAL_NAME: FirebaseAuthInternalName = 'auth-internal';\nconst APP_CHECK_INTERNAL_NAME: AppCheckInternalComponentName =\n  'app-check-internal';\nconst MESSAGING_INTERNAL_NAME: MessagingInternalComponentName =\n  'messaging-internal';\n\nexport function registerFunctions(\n  fetchImpl: typeof fetch,\n  variant?: string\n): void {\n  const factory: InstanceFactory<'functions'> = (\n    container: ComponentContainer,\n    { instanceIdentifier: regionOrCustomDomain }\n  ) => {\n    // Dependencies\n    const app = container.getProvider('app').getImmediate();\n    const authProvider = container.getProvider(AUTH_INTERNAL_NAME);\n    const messagingProvider = container.getProvider(MESSAGING_INTERNAL_NAME);\n    const appCheckProvider = container.getProvider(APP_CHECK_INTERNAL_NAME);\n\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return new FunctionsService(\n      app,\n      authProvider,\n      messagingProvider,\n      appCheckProvider,\n      regionOrCustomDomain,\n      fetchImpl\n    );\n  };\n\n  _registerComponent(\n    new Component(\n      FUNCTIONS_TYPE,\n      factory,\n      ComponentType.PUBLIC\n    ).setMultipleInstances(true)\n  );\n\n  registerVersion(name, version, variant);\n  // BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Standardized Firebase Error.\n *\n * Usage:\n *\n *   // TypeScript string literals for type-safe codes\n *   type Err =\n *     'unknown' |\n *     'object-not-found'\n *     ;\n *\n *   // Closure enum for type-safe error codes\n *   // at-enum {string}\n *   var Err = {\n *     UNKNOWN: 'unknown',\n *     OBJECT_NOT_FOUND: 'object-not-found',\n *   }\n *\n *   let errors: Map<Err, string> = {\n *     'generic-error': \"Unknown error\",\n *     'file-not-found': \"Could not find file: {$file}\",\n *   };\n *\n *   // Type-safe function - must pass a valid error code as param.\n *   let error = new ErrorFactory<Err>('service', 'Service', errors);\n *\n *   ...\n *   throw error.create(Err.GENERIC);\n *   ...\n *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});\n *   ...\n *   // Service: Could not file file: foo.txt (service/file-not-found).\n *\n *   catch (e) {\n *     assert(e.message === \"Could not find file: foo.txt.\");\n *     if ((e as FirebaseError)?.code === 'service/file-not-found') {\n *       console.log(\"Could not read file: \" + e['file']);\n *     }\n *   }\n */\n\nexport type ErrorMap<ErrorCode extends string> = {\n  readonly [K in ErrorCode]: string;\n};\n\nconst ERROR_NAME = 'FirebaseError';\n\nexport interface StringLike {\n  toString(): string;\n}\n\nexport interface ErrorData {\n  [key: string]: unknown;\n}\n\n// Based on code from:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types\nexport class FirebaseError extends Error {\n  /** The custom name for all FirebaseErrors. */\n  readonly name: string = ERROR_NAME;\n\n  constructor(\n    /** The error code for this error. */\n    readonly code: string,\n    message: string,\n    /** Custom data for this error. */\n    public customData?: Record<string, unknown>\n  ) {\n    super(message);\n\n    // Fix For ES5\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, FirebaseError.prototype);\n\n    // Maintains proper stack trace for where our error was thrown.\n    // Only available on V8.\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ErrorFactory.prototype.create);\n    }\n  }\n}\n\nexport class ErrorFactory<\n  ErrorCode extends string,\n  ErrorParams extends { readonly [K in ErrorCode]?: ErrorData } = {}\n> {\n  constructor(\n    private readonly service: string,\n    private readonly serviceName: string,\n    private readonly errors: ErrorMap<ErrorCode>\n  ) {}\n\n  create<K extends ErrorCode>(\n    code: K,\n    ...data: K extends keyof ErrorParams ? [ErrorParams[K]] : []\n  ): FirebaseError {\n    const customData = (data[0] as ErrorData) || {};\n    const fullCode = `${this.service}/${code}`;\n    const template = this.errors[code];\n\n    const message = template ? replaceTemplate(template, customData) : 'Error';\n    // Service Name: Error message (service/code).\n    const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;\n\n    const error = new FirebaseError(fullCode, fullMessage, customData);\n\n    return error;\n  }\n}\n\nfunction replaceTemplate(template: string, data: ErrorData): string {\n  return template.replace(PATTERN, (_, key) => {\n    const value = data[key];\n    return value != null ? String(value) : `<${key}?>`;\n  });\n}\n\nconst PATTERN = /\\{\\$([^}]+)}/g;\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface Compat<T> {\n  _delegate: T;\n}\n\nexport function getModularInstance<ExpService>(\n  service: Compat<ExpService> | ExpService\n): ExpService {\n  if (service && (service as Compat<ExpService>)._delegate) {\n    return (service as Compat<ExpService>)._delegate;\n  } else {\n    return service as ExpService;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  InstantiationMode,\n  InstanceFactory,\n  ComponentType,\n  Dictionary,\n  Name,\n  onInstanceCreatedCallback\n} from './types';\n\n/**\n * Component for service name T, e.g. `auth`, `auth-internal`\n */\nexport class Component<T extends Name = Name> {\n  multipleInstances = false;\n  /**\n   * Properties to be added to the service namespace\n   */\n  serviceProps: Dictionary = {};\n\n  instantiationMode = InstantiationMode.LAZY;\n\n  onInstanceCreated: onInstanceCreatedCallback<T> | null = null;\n\n  /**\n   *\n   * @param name The public service name, e.g. app, auth, firestore, database\n   * @param instanceFactory Service factory responsible for creating the public interface\n   * @param type whether the service provided by the component is public or private\n   */\n  constructor(\n    readonly name: T,\n    readonly instanceFactory: InstanceFactory<T>,\n    readonly type: ComponentType\n  ) {}\n\n  setInstantiationMode(mode: InstantiationMode): this {\n    this.instantiationMode = mode;\n    return this;\n  }\n\n  setMultipleInstances(multipleInstances: boolean): this {\n    this.multipleInstances = multipleInstances;\n    return this;\n  }\n\n  setServiceProps(props: Dictionary): this {\n    this.serviceProps = props;\n    return this;\n  }\n\n  setInstanceCreatedCallback(callback: onInstanceCreatedCallback<T>): this {\n    this.onInstanceCreated = callback;\n    return this;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst LONG_TYPE = 'type.googleapis.com/google.protobuf.Int64Value';\nconst UNSIGNED_LONG_TYPE = 'type.googleapis.com/google.protobuf.UInt64Value';\n\nfunction mapValues(\n  // { [k: string]: unknown } is no longer a wildcard assignment target after typescript 3.5\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  o: { [key: string]: any },\n  f: (arg0: unknown) => unknown\n): object {\n  const result: { [key: string]: unknown } = {};\n  for (const key in o) {\n    if (o.hasOwnProperty(key)) {\n      result[key] = f(o[key]);\n    }\n  }\n  return result;\n}\n\n/**\n * Takes data and encodes it in a JSON-friendly way, such that types such as\n * Date are preserved.\n * @internal\n * @param data - Data to encode.\n */\nexport function encode(data: unknown): unknown {\n  if (data == null) {\n    return null;\n  }\n  if (data instanceof Number) {\n    data = data.valueOf();\n  }\n  if (typeof data === 'number' && isFinite(data)) {\n    // Any number in JS is safe to put directly in JSON and parse as a double\n    // without any loss of precision.\n    return data;\n  }\n  if (data === true || data === false) {\n    return data;\n  }\n  if (Object.prototype.toString.call(data) === '[object String]') {\n    return data;\n  }\n  if (data instanceof Date) {\n    return data.toISOString();\n  }\n  if (Array.isArray(data)) {\n    return data.map(x => encode(x));\n  }\n  if (typeof data === 'function' || typeof data === 'object') {\n    return mapValues(data!, x => encode(x));\n  }\n  // If we got this far, the data is not encodable.\n  throw new Error('Data cannot be encoded in JSON: ' + data);\n}\n\n/**\n * Takes data that's been encoded in a JSON-friendly form and returns a form\n * with richer datatypes, such as Dates, etc.\n * @internal\n * @param json - JSON to convert.\n */\nexport function decode(json: unknown): unknown {\n  if (json == null) {\n    return json;\n  }\n  if ((json as { [key: string]: unknown })['@type']) {\n    switch ((json as { [key: string]: unknown })['@type']) {\n      case LONG_TYPE:\n      // Fall through and handle this the same as unsigned.\n      case UNSIGNED_LONG_TYPE: {\n        // Technically, this could work return a valid number for malformed\n        // data if there was a number followed by garbage. But it's just not\n        // worth all the extra code to detect that case.\n        const value = Number((json as { [key: string]: unknown })['value']);\n        if (isNaN(value)) {\n          throw new Error('Data cannot be decoded from JSON: ' + json);\n        }\n        return value;\n      }\n      default: {\n        throw new Error('Data cannot be decoded from JSON: ' + json);\n      }\n    }\n  }\n  if (Array.isArray(json)) {\n    return json.map(x => decode(x));\n  }\n  if (typeof json === 'function' || typeof json === 'object') {\n    return mapValues(json!, x => decode(x));\n  }\n  // Anything else is safe to return.\n  return json;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Type constant for Firebase Functions.\n */\nexport const FUNCTIONS_TYPE = 'functions';\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FunctionsErrorCodeCore as FunctionsErrorCode } from './public-types';\nimport { decode } from './serializer';\nimport { HttpResponseBody } from './service';\nimport { FirebaseError } from '@firebase/util';\nimport { FUNCTIONS_TYPE } from './constants';\n\n/**\n * Standard error codes for different ways a request can fail, as defined by:\n * https://github.com/googleapis/googleapis/blob/master/google/rpc/code.proto\n *\n * This map is used primarily to convert from a backend error code string to\n * a client SDK error code string, and make sure it's in the supported set.\n */\nconst errorCodeMap: { [name: string]: FunctionsErrorCode } = {\n  OK: 'ok',\n  CANCELLED: 'cancelled',\n  UNKNOWN: 'unknown',\n  INVALID_ARGUMENT: 'invalid-argument',\n  DEADLINE_EXCEEDED: 'deadline-exceeded',\n  NOT_FOUND: 'not-found',\n  ALREADY_EXISTS: 'already-exists',\n  PERMISSION_DENIED: 'permission-denied',\n  UNAUTHENTICATED: 'unauthenticated',\n  RESOURCE_EXHAUSTED: 'resource-exhausted',\n  FAILED_PRECONDITION: 'failed-precondition',\n  ABORTED: 'aborted',\n  OUT_OF_RANGE: 'out-of-range',\n  UNIMPLEMENTED: 'unimplemented',\n  INTERNAL: 'internal',\n  UNAVAILABLE: 'unavailable',\n  DATA_LOSS: 'data-loss'\n};\n\n/**\n * An explicit error that can be thrown from a handler to send an error to the\n * client that called the function.\n */\nexport class FunctionsError extends FirebaseError {\n  constructor(\n    /**\n     * A standard error code that will be returned to the client. This also\n     * determines the HTTP status code of the response, as defined in code.proto.\n     */\n    code: FunctionsErrorCode,\n    message?: string,\n    /**\n     * Extra data to be converted to JSON and included in the error response.\n     */\n    readonly details?: unknown\n  ) {\n    super(`${FUNCTIONS_TYPE}/${code}`, message || '');\n  }\n}\n\n/**\n * Takes an HTTP status code and returns the corresponding ErrorCode.\n * This is the standard HTTP status code -> error mapping defined in:\n * https://github.com/googleapis/googleapis/blob/master/google/rpc/code.proto\n *\n * @param status An HTTP status code.\n * @return The corresponding ErrorCode, or ErrorCode.UNKNOWN if none.\n */\nfunction codeForHTTPStatus(status: number): FunctionsErrorCode {\n  // Make sure any successful status is OK.\n  if (status >= 200 && status < 300) {\n    return 'ok';\n  }\n  switch (status) {\n    case 0:\n      // This can happen if the server returns 500.\n      return 'internal';\n    case 400:\n      return 'invalid-argument';\n    case 401:\n      return 'unauthenticated';\n    case 403:\n      return 'permission-denied';\n    case 404:\n      return 'not-found';\n    case 409:\n      return 'aborted';\n    case 429:\n      return 'resource-exhausted';\n    case 499:\n      return 'cancelled';\n    case 500:\n      return 'internal';\n    case 501:\n      return 'unimplemented';\n    case 503:\n      return 'unavailable';\n    case 504:\n      return 'deadline-exceeded';\n    default: // ignore\n  }\n  return 'unknown';\n}\n\n/**\n * Takes an HTTP response and returns the corresponding Error, if any.\n */\nexport function _errorForResponse(\n  status: number,\n  bodyJSON: HttpResponseBody | null\n): Error | null {\n  let code = codeForHTTPStatus(status);\n\n  // Start with reasonable defaults from the status code.\n  let description: string = code;\n\n  let details: unknown = undefined;\n\n  // Then look through the body for explicit details.\n  try {\n    const errorJSON = bodyJSON && bodyJSON.error;\n    if (errorJSON) {\n      const status = errorJSON.status;\n      if (typeof status === 'string') {\n        if (!errorCodeMap[status]) {\n          // They must've included an unknown error code in the body.\n          return new FunctionsError('internal', 'internal');\n        }\n        code = errorCodeMap[status];\n\n        // TODO(klimt): Add better default descriptions for error enums.\n        // The default description needs to be updated for the new code.\n        description = status;\n      }\n\n      const message = errorJSON.message;\n      if (typeof message === 'string') {\n        description = message;\n      }\n\n      details = errorJSON.details;\n      if (details !== undefined) {\n        details = decode(details);\n      }\n    }\n  } catch (e) {\n    // If we couldn't parse explicit error data, that's fine.\n  }\n\n  if (code === 'ok') {\n    // Technically, there's an edge case where a developer could explicitly\n    // return an error code of OK, and we will treat it as success, but that\n    // seems reasonable.\n    return null;\n  }\n\n  return new FunctionsError(code, description, details);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Provider } from '@firebase/component';\nimport {\n  AppCheckInternalComponentName,\n  FirebaseAppCheckInternal\n} from '@firebase/app-check-interop-types';\nimport {\n  MessagingInternal,\n  MessagingInternalComponentName\n} from '@firebase/messaging-interop-types';\nimport {\n  FirebaseAuthInternal,\n  FirebaseAuthInternalName\n} from '@firebase/auth-interop-types';\n\n/**\n * The metadata that should be supplied with function calls.\n * @internal\n */\nexport interface Context {\n  authToken?: string;\n  messagingToken?: string;\n  appCheckToken: string | null;\n}\n\n/**\n * Helper class to get metadata that should be included with a function call.\n * @internal\n */\nexport class ContextProvider {\n  private auth: FirebaseAuthInternal | null = null;\n  private messaging: MessagingInternal | null = null;\n  private appCheck: FirebaseAppCheckInternal | null = null;\n  constructor(\n    authProvider: Provider<FirebaseAuthInternalName>,\n    messagingProvider: Provider<MessagingInternalComponentName>,\n    appCheckProvider: Provider<AppCheckInternalComponentName>\n  ) {\n    this.auth = authProvider.getImmediate({ optional: true });\n    this.messaging = messagingProvider.getImmediate({\n      optional: true\n    });\n\n    if (!this.auth) {\n      authProvider.get().then(\n        auth => (this.auth = auth),\n        () => {\n          /* get() never rejects */\n        }\n      );\n    }\n\n    if (!this.messaging) {\n      messagingProvider.get().then(\n        messaging => (this.messaging = messaging),\n        () => {\n          /* get() never rejects */\n        }\n      );\n    }\n\n    if (!this.appCheck) {\n      appCheckProvider.get().then(\n        appCheck => (this.appCheck = appCheck),\n        () => {\n          /* get() never rejects */\n        }\n      );\n    }\n  }\n\n  async getAuthToken(): Promise<string | undefined> {\n    if (!this.auth) {\n      return undefined;\n    }\n\n    try {\n      const token = await this.auth.getToken();\n      return token?.accessToken;\n    } catch (e) {\n      // If there's any error when trying to get the auth token, leave it off.\n      return undefined;\n    }\n  }\n\n  async getMessagingToken(): Promise<string | undefined> {\n    if (\n      !this.messaging ||\n      !('Notification' in self) ||\n      Notification.permission !== 'granted'\n    ) {\n      return undefined;\n    }\n\n    try {\n      return await this.messaging.getToken();\n    } catch (e) {\n      // We don't warn on this, because it usually means messaging isn't set up.\n      // console.warn('Failed to retrieve instance id token.', e);\n\n      // If there's any error when trying to get the token, leave it off.\n      return undefined;\n    }\n  }\n\n  async getAppCheckToken(\n    limitedUseAppCheckTokens?: boolean\n  ): Promise<string | null> {\n    if (this.appCheck) {\n      const result = limitedUseAppCheckTokens\n        ? await this.appCheck.getLimitedUseToken()\n        : await this.appCheck.getToken();\n      if (result.error) {\n        // Do not send the App Check header to the functions endpoint if\n        // there was an error from the App Check exchange endpoint. The App\n        // Check SDK will already have logged the error to console.\n        return null;\n      }\n      return result.token;\n    }\n    return null;\n  }\n\n  async getContext(limitedUseAppCheckTokens?: boolean): Promise<Context> {\n    const authToken = await this.getAuthToken();\n    const messagingToken = await this.getMessagingToken();\n    const appCheckToken = await this.getAppCheckToken(limitedUseAppCheckTokens);\n    return { authToken, messagingToken, appCheckToken };\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, _FirebaseService } from '@firebase/app';\nimport {\n  HttpsCallable,\n  HttpsCallableResult,\n  HttpsCallableOptions\n} from './public-types';\nimport { _errorForResponse, FunctionsError } from './error';\nimport { ContextProvider } from './context';\nimport { encode, decode } from './serializer';\nimport { Provider } from '@firebase/component';\nimport { FirebaseAuthInternalName } from '@firebase/auth-interop-types';\nimport { MessagingInternalComponentName } from '@firebase/messaging-interop-types';\nimport { AppCheckInternalComponentName } from '@firebase/app-check-interop-types';\n\nexport const DEFAULT_REGION = 'us-central1';\n\n/**\n * The response to an http request.\n */\ninterface HttpResponse {\n  status: number;\n  json: HttpResponseBody | null;\n}\n/**\n * Describes the shape of the HttpResponse body.\n * It makes functions that would otherwise take {} able to access the\n * possible elements in the body more easily\n */\nexport interface HttpResponseBody {\n  data?: unknown;\n  result?: unknown;\n  error?: {\n    message?: unknown;\n    status?: unknown;\n    details?: unknown;\n  };\n}\n\ninterface CancellablePromise<T> {\n  promise: Promise<T>;\n  cancel: () => void;\n}\n\n/**\n * Returns a Promise that will be rejected after the given duration.\n * The error will be of type FunctionsError.\n *\n * @param millis Number of milliseconds to wait before rejecting.\n */\nfunction failAfter(millis: number): CancellablePromise<never> {\n  // Node timers and browser timers are fundamentally incompatible, but we\n  // don't care about the value here\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let timer: any | null = null;\n  return {\n    promise: new Promise((_, reject) => {\n      timer = setTimeout(() => {\n        reject(new FunctionsError('deadline-exceeded', 'deadline-exceeded'));\n      }, millis);\n    }),\n    cancel: () => {\n      if (timer) {\n        clearTimeout(timer);\n      }\n    }\n  };\n}\n\n/**\n * The main class for the Firebase Functions SDK.\n * @internal\n */\nexport class FunctionsService implements _FirebaseService {\n  readonly contextProvider: ContextProvider;\n  emulatorOrigin: string | null = null;\n  cancelAllRequests: Promise<void>;\n  deleteService!: () => Promise<void>;\n  region: string;\n  customDomain: string | null;\n\n  /**\n   * Creates a new Functions service for the given app.\n   * @param app - The FirebaseApp to use.\n   */\n  constructor(\n    readonly app: FirebaseApp,\n    authProvider: Provider<FirebaseAuthInternalName>,\n    messagingProvider: Provider<MessagingInternalComponentName>,\n    appCheckProvider: Provider<AppCheckInternalComponentName>,\n    regionOrCustomDomain: string = DEFAULT_REGION,\n    readonly fetchImpl: typeof fetch\n  ) {\n    this.contextProvider = new ContextProvider(\n      authProvider,\n      messagingProvider,\n      appCheckProvider\n    );\n    // Cancels all ongoing requests when resolved.\n    this.cancelAllRequests = new Promise(resolve => {\n      this.deleteService = () => {\n        return Promise.resolve(resolve());\n      };\n    });\n\n    // Resolve the region or custom domain overload by attempting to parse it.\n    try {\n      const url = new URL(regionOrCustomDomain);\n      this.customDomain =\n        url.origin + (url.pathname === '/' ? '' : url.pathname);\n      this.region = DEFAULT_REGION;\n    } catch (e) {\n      this.customDomain = null;\n      this.region = regionOrCustomDomain;\n    }\n  }\n\n  _delete(): Promise<void> {\n    return this.deleteService();\n  }\n\n  /**\n   * Returns the URL for a callable with the given name.\n   * @param name - The name of the callable.\n   * @internal\n   */\n  _url(name: string): string {\n    const projectId = this.app.options.projectId;\n    if (this.emulatorOrigin !== null) {\n      const origin = this.emulatorOrigin;\n      return `${origin}/${projectId}/${this.region}/${name}`;\n    }\n\n    if (this.customDomain !== null) {\n      return `${this.customDomain}/${name}`;\n    }\n\n    return `https://${this.region}-${projectId}.cloudfunctions.net/${name}`;\n  }\n}\n\n/**\n * Modify this instance to communicate with the Cloud Functions emulator.\n *\n * Note: this must be called before this instance has been used to do any operations.\n *\n * @param host The emulator host (ex: localhost)\n * @param port The emulator port (ex: 5001)\n * @public\n */\nexport function connectFunctionsEmulator(\n  functionsInstance: FunctionsService,\n  host: string,\n  port: number\n): void {\n  functionsInstance.emulatorOrigin = `http://${host}:${port}`;\n}\n\n/**\n * Returns a reference to the callable https trigger with the given name.\n * @param name - The name of the trigger.\n * @public\n */\nexport function httpsCallable<RequestData, ResponseData>(\n  functionsInstance: FunctionsService,\n  name: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData> {\n  return (data => {\n    return call(functionsInstance, name, data, options || {});\n  }) as HttpsCallable<RequestData, ResponseData>;\n}\n\n/**\n * Returns a reference to the callable https trigger with the given url.\n * @param url - The url of the trigger.\n * @public\n */\nexport function httpsCallableFromURL<RequestData, ResponseData>(\n  functionsInstance: FunctionsService,\n  url: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData> {\n  return (data => {\n    return callAtURL(functionsInstance, url, data, options || {});\n  }) as HttpsCallable<RequestData, ResponseData>;\n}\n\n/**\n * Does an HTTP POST and returns the completed response.\n * @param url The url to post to.\n * @param body The JSON body of the post.\n * @param headers The HTTP headers to include in the request.\n * @return A Promise that will succeed when the request finishes.\n */\nasync function postJSON(\n  url: string,\n  body: unknown,\n  headers: { [key: string]: string },\n  fetchImpl: typeof fetch\n): Promise<HttpResponse> {\n  headers['Content-Type'] = 'application/json';\n\n  let response: Response;\n  try {\n    response = await fetchImpl(url, {\n      method: 'POST',\n      body: JSON.stringify(body),\n      headers\n    });\n  } catch (e) {\n    // This could be an unhandled error on the backend, or it could be a\n    // network error. There's no way to know, since an unhandled error on the\n    // backend will fail to set the proper CORS header, and thus will be\n    // treated as a network error by fetch.\n    return {\n      status: 0,\n      json: null\n    };\n  }\n  let json: HttpResponseBody | null = null;\n  try {\n    json = await response.json();\n  } catch (e) {\n    // If we fail to parse JSON, it will fail the same as an empty body.\n  }\n  return {\n    status: response.status,\n    json\n  };\n}\n\n/**\n * Calls a callable function asynchronously and returns the result.\n * @param name The name of the callable trigger.\n * @param data The data to pass as params to the function.s\n */\nfunction call(\n  functionsInstance: FunctionsService,\n  name: string,\n  data: unknown,\n  options: HttpsCallableOptions\n): Promise<HttpsCallableResult> {\n  const url = functionsInstance._url(name);\n  return callAtURL(functionsInstance, url, data, options);\n}\n\n/**\n * Calls a callable function asynchronously and returns the result.\n * @param url The url of the callable trigger.\n * @param data The data to pass as params to the function.s\n */\nasync function callAtURL(\n  functionsInstance: FunctionsService,\n  url: string,\n  data: unknown,\n  options: HttpsCallableOptions\n): Promise<HttpsCallableResult> {\n  // Encode any special types, such as dates, in the input data.\n  data = encode(data);\n  const body = { data };\n\n  // Add a header for the authToken.\n  const headers: { [key: string]: string } = {};\n  const context = await functionsInstance.contextProvider.getContext(\n    options.limitedUseAppCheckTokens\n  );\n  if (context.authToken) {\n    headers['Authorization'] = 'Bearer ' + context.authToken;\n  }\n  if (context.messagingToken) {\n    headers['Firebase-Instance-ID-Token'] = context.messagingToken;\n  }\n  if (context.appCheckToken !== null) {\n    headers['X-Firebase-AppCheck'] = context.appCheckToken;\n  }\n\n  // Default timeout to 70s, but let the options override it.\n  const timeout = options.timeout || 70000;\n\n  const failAfterHandle = failAfter(timeout);\n  const response = await Promise.race([\n    postJSON(url, body, headers, functionsInstance.fetchImpl),\n    failAfterHandle.promise,\n    functionsInstance.cancelAllRequests\n  ]);\n\n  // Always clear the failAfter timeout\n  failAfterHandle.cancel();\n\n  // If service was deleted, interrupted response throws an error.\n  if (!response) {\n    throw new FunctionsError(\n      'cancelled',\n      'Firebase Functions instance was deleted.'\n    );\n  }\n\n  // Check for an error status, regardless of http status.\n  const error = _errorForResponse(response.status, response.json);\n  if (error) {\n    throw error;\n  }\n\n  if (!response.json) {\n    throw new FunctionsError('internal', 'Response is not valid JSON object.');\n  }\n\n  let responseData = response.json.data;\n  // TODO(klimt): For right now, allow \"result\" instead of \"data\", for\n  // backwards compatibility.\n  if (typeof responseData === 'undefined') {\n    responseData = response.json.result;\n  }\n  if (typeof responseData === 'undefined') {\n    // Consider the response malformed.\n    throw new FunctionsError('internal', 'Response is missing data field.');\n  }\n\n  // Decode any special types, such as dates, in the returned data.\n  const decodedData = decode(responseData);\n\n  return { data: decodedData };\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _getProvider, FirebaseApp, getApp } from '@firebase/app';\nimport { FUNCTIONS_TYPE } from './constants';\n\nimport { Provider } from '@firebase/component';\nimport { Functions, HttpsCallableOptions, HttpsCallable } from './public-types';\nimport {\n  FunctionsService,\n  DEFAULT_REGION,\n  connectFunctionsEmulator as _connectFunctionsEmulator,\n  httpsCallable as _httpsCallable,\n  httpsCallableFromURL as _httpsCallableFromURL\n} from './service';\nimport {\n  getModularInstance,\n  getDefaultEmulatorHostnameAndPort\n} from '@firebase/util';\n\nexport * from './public-types';\n\n/**\n * Returns a {@link Functions} instance for the given app.\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n * @param regionOrCustomDomain - one of:\n *   a) The region the callable functions are located in (ex: us-central1)\n *   b) A custom domain hosting the callable functions (ex: https://mydomain.com)\n * @public\n */\nexport function getFunctions(\n  app: FirebaseApp = getApp(),\n  regionOrCustomDomain: string = DEFAULT_REGION\n): Functions {\n  // Dependencies\n  const functionsProvider: Provider<'functions'> = _getProvider(\n    getModularInstance(app),\n    FUNCTIONS_TYPE\n  );\n  const functionsInstance = functionsProvider.getImmediate({\n    identifier: regionOrCustomDomain\n  });\n  const emulator = getDefaultEmulatorHostnameAndPort('functions');\n  if (emulator) {\n    connectFunctionsEmulator(functionsInstance, ...emulator);\n  }\n  return functionsInstance;\n}\n\n/**\n * Modify this instance to communicate with the Cloud Functions emulator.\n *\n * Note: this must be called before this instance has been used to do any operations.\n *\n * @param host - The emulator host (ex: localhost)\n * @param port - The emulator port (ex: 5001)\n * @public\n */\nexport function connectFunctionsEmulator(\n  functionsInstance: Functions,\n  host: string,\n  port: number\n): void {\n  _connectFunctionsEmulator(\n    getModularInstance<FunctionsService>(functionsInstance as FunctionsService),\n    host,\n    port\n  );\n}\n\n/**\n * Returns a reference to the callable HTTPS trigger with the given name.\n * @param name - The name of the trigger.\n * @public\n */\nexport function httpsCallable<RequestData = unknown, ResponseData = unknown>(\n  functionsInstance: Functions,\n  name: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData> {\n  return _httpsCallable<RequestData, ResponseData>(\n    getModularInstance<FunctionsService>(functionsInstance as FunctionsService),\n    name,\n    options\n  );\n}\n\n/**\n * Returns a reference to the callable HTTPS trigger with the specified url.\n * @param url - The url of the trigger.\n * @public\n */\nexport function httpsCallableFromURL<\n  RequestData = unknown,\n  ResponseData = unknown\n>(\n  functionsInstance: Functions,\n  url: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData> {\n  return _httpsCallableFromURL<RequestData, ResponseData>(\n    getModularInstance<FunctionsService>(functionsInstance as FunctionsService),\n    url,\n    options\n  );\n}\n", "/**\n * Cloud Functions for Firebase\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { registerFunctions } from './config';\n\nexport * from './api';\nexport * from './public-types';\n\nregisterFunctions(fetch.bind(self));\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport firebase, { _FirebaseNamespace } from '@firebase/app-compat';\nimport { FunctionsService } from './service';\nimport {\n  Component,\n  ComponentType,\n  InstanceFactory,\n  ComponentContainer,\n  InstanceFactoryOptions\n} from '@firebase/component';\n\nconst DEFAULT_REGION = 'us-central1';\n\nconst factory: InstanceFactory<'functions-compat'> = (\n  container: ComponentContainer,\n  { instanceIdentifier: regionOrCustomDomain }: InstanceFactoryOptions\n) => {\n  // Dependencies\n  const app = container.getProvider('app-compat').getImmediate();\n  const functionsServiceExp = container.getProvider('functions').getImmediate({\n    identifier: regionOrCustomDomain ?? DEFAULT_REGION\n  });\n\n  return new FunctionsService(app, functionsServiceExp);\n};\n\nexport function registerFunctions(): void {\n  const namespaceExports = {\n    Functions: FunctionsService\n  };\n  (firebase as _FirebaseNamespace).INTERNAL.registerComponent(\n    new Component('functions-compat', factory, ComponentType.PUBLIC)\n      .setServiceProps(namespaceExports)\n      .setMultipleInstances(true)\n  );\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseFunctions, HttpsCallable } from '@firebase/functions-types';\nimport {\n  httpsCallable as httpsCallableExp,\n  httpsCallableFromURL as httpsCallableFromURLExp,\n  connectFunctionsEmulator as useFunctionsEmulatorExp,\n  HttpsCallableOptions,\n  Functions as FunctionsServiceExp\n} from '@firebase/functions';\nimport { FirebaseApp, _FirebaseService } from '@firebase/app-compat';\nimport { FirebaseError } from '@firebase/util';\n\nexport class FunctionsService implements FirebaseFunctions, _FirebaseService {\n  /**\n   * For testing.\n   * @internal\n   */\n  _region: string;\n  /**\n   * For testing.\n   * @internal\n   */\n  _customDomain: string | null;\n\n  constructor(\n    public app: FirebaseApp,\n    readonly _delegate: FunctionsServiceExp\n  ) {\n    this._region = this._delegate.region;\n    this._customDomain = this._delegate.customDomain;\n  }\n  httpsCallable(name: string, options?: HttpsCallableOptions): HttpsCallable {\n    return httpsCallableExp(this._delegate, name, options);\n  }\n  httpsCallableFromURL(\n    url: string,\n    options?: HttpsCallableOptions\n  ): HttpsCallable {\n    return httpsCallableFromURLExp(this._delegate, url, options);\n  }\n  /**\n   * Deprecated in pre-modularized repo, does not exist in modularized\n   * functions package, need to convert to \"host\" and \"port\" args that\n   * `useFunctionsEmulatorExp` takes.\n   * @deprecated\n   */\n  useFunctionsEmulator(origin: string): void {\n    const match = origin.match('[a-zA-Z]+://([a-zA-Z0-9.-]+)(?::([0-9]+))?');\n    if (match == null) {\n      throw new FirebaseError(\n        'functions',\n        'No origin provided to useFunctionsEmulator()'\n      );\n    }\n    if (match[2] == null) {\n      throw new FirebaseError(\n        'functions',\n        'Port missing in origin provided to useFunctionsEmulator()'\n      );\n    }\n    return useFunctionsEmulatorExp(this._delegate, match[1], Number(match[2]));\n  }\n  useEmulator(host: string, port: number): void {\n    return useFunctionsEmulatorExp(this._delegate, host, port);\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport firebase from '@firebase/app-compat';\nimport { name, version } from '../package.json';\nimport { registerFunctions } from './register';\nimport * as types from '@firebase/functions-types';\n\nregisterFunctions();\nfirebase.registerVersion(name, version);\n\ndeclare module '@firebase/app-compat' {\n  interface FirebaseNamespace {\n    functions: {\n      (app?: FirebaseApp): types.FirebaseFunctions;\n      Functions: typeof types.FirebaseFunctions;\n    };\n  }\n  interface FirebaseApp {\n    functions(regionOrCustomDomain?: string): types.FirebaseFunctions;\n  }\n}\n"], "names": ["fetchImpl", "variant", "FirebaseError", "Error", "constructor", "code", "message", "customData", "super", "this", "name", "Object", "setPrototypeOf", "prototype", "captureStackTrace", "ErrorFactory", "create", "service", "serviceName", "errors", "data", "fullCode", "template", "replace", "PATTERN", "_", "key", "value", "String", "fullMessage", "getModularInstance", "_delegate", "Component", "instanceFactory", "type", "multipleInstances", "serviceProps", "instantiationMode", "onInstanceCreated", "setInstantiationMode", "mode", "setMultipleInstances", "setServiceProps", "props", "setInstanceCreatedCallback", "callback", "LONG_TYPE", "UNSIGNED_LONG_TYPE", "mapValues", "o", "f", "result", "hasOwnProperty", "decode", "json", "Number", "isNaN", "Array", "isArray", "map", "x", "FUNCTIONS_TYPE", "errorCodeMap", "OK", "CANCELLED", "UNKNOWN", "INVALID_ARGUMENT", "DEADLINE_EXCEEDED", "NOT_FOUND", "ALREADY_EXISTS", "PERMISSION_DENIED", "UNAUTHENTICATED", "RESOURCE_EXHAUSTED", "FAILED_PRECONDITION", "ABORTED", "OUT_OF_RANGE", "UNIMPLEMENTED", "INTERNAL", "UNAVAILABLE", "DATA_LOSS", "FunctionsError", "details", "_errorForResponse", "status", "bodyJSON", "codeForHTTPStatus", "description", "undefined", "errorJSON", "error", "e", "ContextProvider", "authProvider", "messagingProvider", "appCheckProvider", "auth", "messaging", "appCheck", "getImmediate", "optional", "get", "then", "getAuthToken", "token", "getToken", "accessToken", "getMessagingToken", "self", "Notification", "permission", "getAppCheckToken", "limitedUseAppCheckTokens", "getLimitedUseToken", "getContext", "authToken", "messagingToken", "appCheckToken", "DEFAULT_REGION", "FunctionsService", "app", "regionOrCustomDomain", "emulator<PERSON><PERSON><PERSON>", "contextProvider", "cancelAllRequests", "Promise", "resolve", "deleteService", "url", "URL", "customDomain", "origin", "pathname", "region", "_delete", "_url", "projectId", "options", "httpsCallable", "functionsInstance", "callAtURL", "async", "body", "encode", "valueOf", "isFinite", "toString", "call", "Date", "toISOString", "headers", "context", "failAfterHandle", "millis", "timer", "promise", "reject", "setTimeout", "cancel", "clearTimeout", "failAfter", "timeout", "response", "race", "method", "JSON", "stringify", "postJSON", "responseData", "connectFunctionsEmulator", "host", "port", "httpsCallableFromURL", "fetch", "bind", "_registerComponent", "container", "instanceIdentifier", "get<PERSON><PERSON><PERSON>", "registerVersion", "version", "namespaceExports", "_region", "_customDomain", "_httpsCallable", "httpsCallableFromURLExp", "useFunctionsEmulator", "match", "useFunctionsEmulatorExp", "useEmulator", "factory", "functionsServiceExp", "identifier", "Functions", "firebase", "registerComponent"], "mappings": "ybAsCEA,EACAC,eCkCWC,UAAsBC,MAIjCC,YAEWC,EACTC,EAEOC,GAEPC,MAAMF,GALGG,KAAIJ,KAAJA,EAGFI,KAAUF,WAAVA,EAPAE,KAAIC,KAdI,gBA2BfC,OAAOC,eAAeH,KAAMP,EAAcW,WAItCV,MAAMW,mBACRX,MAAMW,kBAAkBL,KAAMM,EAAaF,UAAUG,eAK9CD,EAIXX,YACmBa,EACAC,EACAC,GAFAV,KAAOQ,QAAPA,EACAR,KAAWS,YAAXA,EACAT,KAAMU,OAANA,EAGnBH,OACEX,KACGe,GAEH,IAcuCA,EAdjCb,EAAca,EAAK,IAAoB,GACvCC,KAAcZ,KAAKQ,WAAWZ,IAC9BiB,EAAWb,KAAKU,OAAOd,GAEvBC,EAAUgB,GAUuBF,EAVcb,EAAVe,EAW7BC,QAAQC,EAAS,CAACC,EAAGC,KACnC,IAAMC,EAAQP,EAAKM,GACnB,OAAgB,MAATC,EAAgBC,OAAOD,OAAaD,SAbwB,QAE7DG,KAAiBpB,KAAKS,gBAAgBZ,MAAYe,MAIxD,OAFc,IAAInB,EAAcmB,EAAUQ,EAAatB,IAa3D,MAAMiB,EAAU,gBChHV,SAAUM,EACdb,GAEA,OAAIA,GAAYA,EAA+Bc,UACrCd,EAA+Bc,UAEhCd,QCCEe,EAiBX5B,YACWM,EACAuB,EACAC,GAFAzB,KAAIC,KAAJA,EACAD,KAAewB,gBAAfA,EACAxB,KAAIyB,KAAJA,EAnBXzB,KAAiB0B,mBAAG,EAIpB1B,KAAY2B,aAAe,GAE3B3B,KAAA4B,kBAA2C,OAE3C5B,KAAiB6B,kBAAwC,KAczDC,qBAAqBC,GAEnB,OADA/B,KAAK4B,kBAAoBG,EAClB/B,KAGTgC,qBAAqBN,GAEnB,OADA1B,KAAK0B,kBAAoBA,EAClB1B,KAGTiC,gBAAgBC,GAEd,OADAlC,KAAK2B,aAAeO,EACblC,KAGTmC,2BAA2BC,GAEzB,OADApC,KAAK6B,kBAAoBO,EAClBpC,MCpDX,MAAMqC,EAAY,iDACZC,EAAqB,kDAE3B,SAASC,EAGPC,EACAC,GAEA,MAAMC,EAAqC,GAC3C,IAAK,MAAMzB,KAAOuB,EACZA,EAAEG,eAAe1B,KACnByB,EAAOzB,GAAOwB,EAAED,EAAEvB,KAGtB,OAAOyB,EA8CH,SAAUE,EAAOC,GACrB,GAAY,MAARA,EACF,OAAOA,EAET,GAAKA,EAAoC,SACvC,OAASA,EAAoC,UAC3C,KAAKR,EAEL,KAAKC,EAIH,IAAMpB,EAAQ4B,OAAQD,EAA2C,OACjE,GAAIE,MAAM7B,GACR,MAAM,IAAIxB,MAAM,qCAAuCmD,GAEzD,OAAO3B,EAET,QACE,MAAM,IAAIxB,MAAM,qCAAuCmD,GAI7D,OAAIG,MAAMC,QAAQJ,GACTA,EAAKK,IAAIC,GAAKP,EAAOO,IAEV,mBAATN,GAAuC,iBAATA,EAChCN,EAAUM,EAAOM,GAAKP,EAAOO,IAG/BN,ECvFF,MAAMO,EAAiB,YCUxBC,EAAuD,CAC3DC,GAAI,KACJC,UAAW,YACXC,QAAS,UACTC,iBAAkB,mBAClBC,kBAAmB,oBACnBC,UAAW,YACXC,eAAgB,iBAChBC,kBAAmB,oBACnBC,gBAAiB,kBACjBC,mBAAoB,qBACpBC,oBAAqB,sBACrBC,QAAS,UACTC,aAAc,eACdC,cAAe,gBACfC,SAAU,WACVC,YAAa,cACbC,UAAW,mBAOAC,UAAuB9E,EAClCE,YAKEC,EACAC,EAIS2E,GAETzE,SAASqD,KAAkBxD,IAAQC,GAAW,IAFrCG,KAAOwE,QAAPA,GAqDG,SAAAC,EACdC,EACAC,GAEA,IAAI/E,EA3CN,SAA2B8E,GAEzB,GAAc,KAAVA,GAAiBA,EAAS,IAC5B,MAAO,KAET,OAAQA,GACN,KAAK,EAEH,MAAO,WACT,KAAK,IACH,MAAO,mBACT,KAAK,IACH,MAAO,kBACT,KAAK,IACH,MAAO,oBACT,KAAK,IACH,MAAO,YACT,KAAK,IACH,MAAO,UACT,KAAK,IACH,MAAO,qBACT,KAAK,IACH,MAAO,YACT,KAAK,IACH,MAAO,WACT,KAAK,IACH,MAAO,gBACT,KAAK,IACH,MAAO,cACT,KAAK,IACH,MAAO,oBAGX,MAAO,UAUIE,CAAkBF,GAGzBG,EAAsBjF,EAEtB4E,OAAmBM,EAGvB,IACE,IAAMC,EAAYJ,GAAYA,EAASK,MACvC,GAAID,EAAW,CACb,MAAML,EAASK,EAAUL,OACzB,GAAsB,iBAAXA,EAAqB,CAC9B,IAAKrB,EAAaqB,GAEhB,OAAO,IAAIH,EAAe,WAAY,YAExC3E,EAAOyD,EAAaqB,GAIpBG,EAAcH,EAGhB,IAAM7E,EAAUkF,EAAUlF,QACH,iBAAZA,IACTgF,EAAchF,GAGhB2E,EAAUO,EAAUP,aACJM,IAAZN,IACFA,EAAU5B,EAAO4B,KAGrB,MAAOS,IAIT,MAAa,OAATrF,EAIK,KAGF,IAAI2E,EAAe3E,EAAMiF,EAAaL,SC1HlCU,EAIXvF,YACEwF,EACAC,EACAC,GANMrF,KAAIsF,KAAgC,KACpCtF,KAASuF,UAA6B,KACtCvF,KAAQwF,SAAoC,KAMlDxF,KAAKsF,KAAOH,EAAaM,aAAa,CAAEC,UAAU,IAClD1F,KAAKuF,UAAYH,EAAkBK,aAAa,CAC9CC,UAAU,IAGP1F,KAAKsF,MACRH,EAAaQ,MAAMC,KACjBN,GAAStF,KAAKsF,KAAOA,EACrB,QAMCtF,KAAKuF,WACRH,EAAkBO,MAAMC,KACtBL,GAAcvF,KAAKuF,UAAYA,EAC/B,QAMCvF,KAAKwF,UACRH,EAAiBM,MAAMC,KACrBJ,GAAaxF,KAAKwF,SAAWA,EAC7B,QAONK,qBACE,GAAK7F,KAAKsF,KAIV,IACE,IAAMQ,QAAc9F,KAAKsF,KAAKS,WAC9B,OAAOD,MAAAA,OAAA,EAAAA,EAAOE,YACd,MAAOf,GAEP,QAIJgB,0BACE,GACGjG,KAAKuF,WACJ,iBAAkBW,MACQ,YAA5BC,aAAaC,WAKf,IACE,OAAapG,KAAKuF,UAAUQ,WAC5B,MAAOd,GAKP,QAIJoB,uBACEC,GAEA,GAAItG,KAAKwF,SAAU,CACjB,IAAM9C,EAAS4D,QACLtG,KAAKwF,SAASe,2BACdvG,KAAKwF,SAASO,WACxB,OAAIrD,EAAOsC,MAIF,KAEFtC,EAAOoD,MAEhB,OAAO,KAGTU,iBAAiBF,GAIf,MAAO,CAAEG,gBAHezG,KAAK6F,eAGTa,qBAFS1G,KAAKiG,oBAEEU,oBADR3G,KAAKqG,iBAAiBC,KC/G/C,MAAMM,EAAiB,oBA0DjBC,EAYXlH,YACWmH,EACT3B,EACAC,EACAC,EACA0B,EAA+BH,EACtBrH,GALAS,KAAG8G,IAAHA,EAKA9G,KAAST,UAATA,EAhBXS,KAAcgH,eAAkB,KAkB9BhH,KAAKiH,gBAAkB,IAAI/B,EACzBC,EACAC,EACAC,GAGFrF,KAAKkH,kBAAoB,IAAIC,QAAQC,IACnCpH,KAAKqH,cAAgB,IACZF,QAAQC,QAAQA,OAK3B,IACE,IAAME,EAAM,IAAIC,IAAIR,GACpB/G,KAAKwH,aACHF,EAAIG,QAA2B,MAAjBH,EAAII,SAAmB,GAAKJ,EAAII,UAChD1H,KAAK2H,OAASf,EACd,MAAO3B,GACPjF,KAAKwH,aAAe,KACpBxH,KAAK2H,OAASZ,GAIlBa,UACE,OAAO5H,KAAKqH,gBAQdQ,KAAK5H,GACH,IAAM6H,EAAY9H,KAAK8G,IAAIiB,QAAQD,UACnC,OAA4B,OAAxB9H,KAAKgH,eAKiB,OAAtBhH,KAAKwH,gBACGxH,KAAKwH,gBAAgBvH,eAGfD,KAAK2H,UAAUG,wBAAgC7H,OARhDD,KAAKgH,kBACAc,KAAa9H,KAAK2H,UAAU1H,KAiCtC+H,SAAAA,EACdC,EACAhI,EACA8H,GAEA,OAAQpH,IACN,OAuEFA,EAvEuCA,EAwEvCoH,EAxE6CA,GAAW,GA0ElDT,GALNW,EArEcA,GA0EgBJ,KA1EG5H,GA2E1BiI,EAAUD,EAAmBX,EAAK3G,EAAMoH,GAPjD,IAGEpH,EACAoH,EAEMT,GASRa,eAAeD,EACbD,EACAX,EACA3G,EACAoH,GAIA,IAAMK,EAAO,CAAEzH,KADfA,EJ3OI,SAAU0H,EAAO1H,GACrB,GAAY,MAARA,EACF,OAAO,KAKT,GAAoB,iBAFlBA,EADEA,aAAgBmC,OACXnC,EAAK2H,UAEH3H,IAAqB4H,SAAS5H,GAGvC,OAAOA,EAET,IAAa,IAATA,IAA0B,IAATA,EACnB,OAAOA,EAET,GAA6C,oBAAzCT,OAAOE,UAAUoI,SAASC,KAAK9H,GACjC,OAAOA,EAET,GAAIA,aAAgB+H,KAClB,OAAO/H,EAAKgI,cAEd,GAAI3F,MAAMC,QAAQtC,GAChB,OAAOA,EAAKuC,IAAIC,GAAKkF,EAAOlF,IAE9B,GAAoB,mBAATxC,GAAuC,iBAATA,EACvC,OAAO4B,EAAU5B,EAAOwC,GAAKkF,EAAOlF,IAGtC,MAAM,IAAIzD,MAAM,mCAAqCiB,GI+M9C0H,CAAO1H,IAId,MAAMiI,EAAqC,GAC3C,IAAMC,QAAgBZ,EAAkBhB,gBAAgBT,WACtDuB,EAAQzB,0BAENuC,EAAQpC,YACVmC,EAAuB,cAAI,UAAYC,EAAQpC,WAE7CoC,EAAQnC,iBACVkC,EAAQ,8BAAgCC,EAAQnC,gBAEpB,OAA1BmC,EAAQlC,gBACViC,EAAQ,uBAAyBC,EAAQlC,eAM3C,MAAMmC,EAtOR,SAAmBC,GAIjB,IAAIC,EAAoB,KACxB,MAAO,CACLC,QAAS,IAAI9B,QAAQ,CAACnG,EAAGkI,KACvBF,EAAQG,WAAW,KACjBD,EAAO,IAAI3E,EAAe,oBAAqB,uBAC9CwE,KAELK,OAAQ,KACFJ,GACFK,aAAaL,KAyNKM,CAFRvB,EAAQwB,SAAW,KAG7BC,QAAiBrC,QAAQsC,KAAK,CAtFtCtB,eACEb,EACAc,EACAQ,EACArJ,GAEAqJ,EAAQ,gBAAkB,mBAE1B,IAAIY,EACJ,IACEA,QAAiBjK,EAAU+H,EAAK,CAC9BoC,OAAQ,OACRtB,KAAMuB,KAAKC,UAAUxB,GACrBQ,QAAAA,IAEF,MAAO3D,GAKP,MAAO,CACLP,OAAQ,EACR7B,KAAM,MAGV,IAAIA,EAAgC,KACpC,IACEA,QAAa2G,EAAS3G,OACtB,MAAOoC,IAGT,MAAO,CACLP,OAAQ8E,EAAS9E,OACjB7B,KAAAA,GAsDAgH,CAASvC,EAAKc,EAAMQ,EAASX,EAAkB1I,WAC/CuJ,EAAgBG,QAChBhB,EAAkBf,oBAOpB,GAHA4B,EAAgBM,UAGXI,EACH,MAAM,IAAIjF,EACR,YACA,4CAKES,EAAQP,EAAkB+E,EAAS9E,OAAQ8E,EAAS3G,MAC1D,GAAImC,EACF,MAAMA,EAGR,IAAKwE,EAAS3G,KACZ,MAAM,IAAI0B,EAAe,WAAY,sCAGvC,IAAIuF,EAAeN,EAAS3G,KAAKlC,KAMjC,QAH4B,IAAjBmJ,IACTA,EAAeN,EAAS3G,KAAKH,aAEH,IAAjBoH,EAET,MAAM,IAAIvF,EAAe,WAAY,mCAMvC,MAAO,CAAE5D,KAFWiC,EAAOkH,6CCxQb,SAAAC,EACd9B,EACA+B,EACAC,GAGE5I,EAAqC4G,GD6FrBjB,yBC5FhBgD,KACAC,IA0BY,SAAAC,EAIdjC,EACAX,EACAS,GAEA,ODiFAE,EChFE5G,EAAqC4G,GDiFvCX,EChFEA,EDiFFS,EChFEA,EDkFMpH,GACCuH,EAAUD,EAAmBX,EAAK3G,EAAMoH,GAAW,IAN9CmC,IACdjC,EACAX,EACAS,ER/JAxI,EUXgB4K,MAAMC,KAAKlE,MVmC3BmE,qBACE,IAAI9I,EACF6B,EAvB0C,CAC5CkH,EACA,CAAEC,mBAAoBxD,MAGtB,IAAMD,EAAMwD,EAAUE,YAAY,OAAO/E,eACnCN,EAAemF,EAAUE,YAhBkB,iBAiB3CpF,EAAoBkF,EAAUE,YAbtC,sBAcQnF,EAAmBiF,EAAUE,YAhBrC,sBAmBE,OAAO,IAAI3D,EACTC,EACA3B,EACAC,EACAC,EACA0B,EACAxH,IASD,UAACyC,sBAAqB,IAGzByI,EAAAA,gBAAgBxK,EAAMyK,EAASlL,GAE/BiL,EAAAA,gBAAgBxK,EAAMyK,EAAS,eW7BzBC,QCfK9D,EAYXlH,YACSmH,EACExF,GADFtB,KAAG8G,IAAHA,EACE9G,KAASsB,UAATA,EAETtB,KAAK4K,QAAU5K,KAAKsB,UAAUqG,OAC9B3H,KAAK6K,cAAgB7K,KAAKsB,UAAUkG,aAEtCQ,cAAc/H,EAAc8H,GAC1B,OH8CK+C,EACLzJ,EG/CwBrB,KAAKsB,WAAWrB,EAAM8H,GAEhDmC,qBACE5C,EACAS,GAEA,OAAOgD,EAAwB/K,KAAKsB,UAAWgG,EAAKS,GAQtDiD,qBAAqBvD,GACnB,IAAMwD,EAAQxD,EAAOwD,MAAM,8CAC3B,GAAa,MAATA,EACF,MAAM,IAAIxL,EACR,YACA,gDAGJ,GAAgB,MAAZwL,EAAM,GACR,MAAM,IAAIxL,EACR,YACA,6DAGJ,OAAOyL,EAAwBlL,KAAKsB,UAAW2J,EAAM,GAAInI,OAAOmI,EAAM,KAExEE,YAAYnB,EAAcC,GACxB,OAAOiB,EAAwBlL,KAAKsB,UAAW0I,EAAMC,IDpDzD,MAAMrD,EAAiB,cAEjBwE,EAA+C,CACnDd,EACA,CAAEC,mBAAoBxD,MAGtB,IAAMD,EAAMwD,EAAUE,YAAY,cAAc/E,eAC1C4F,EAAsBf,EAAUE,YAAY,aAAa/E,aAAa,CAC1E6F,WAAYvE,MAAAA,EAAAA,EAAwBH,IAGtC,OAAO,IAAIC,EAAiBC,EAAKuE,IAI3BV,EAAmB,CACvBY,UAAW1E,GAEZ2E,EAA+B,QAACpH,SAASqH,kBACxC,IAAIlK,EAAU,mBAAoB6J,EAA8B,UAC7DnJ,gBAAgB0I,GAChB3I,sBAAqB,IE1B5BwJ,EAAAA,QAASf"}