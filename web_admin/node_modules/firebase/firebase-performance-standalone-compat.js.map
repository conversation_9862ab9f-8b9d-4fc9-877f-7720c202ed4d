{"version": 3, "file": "firebase-performance-standalone-compat.js", "sources": ["../../node_modules/tslib/tslib.es6.js", "../util/src/crypt.ts", "../util/src/deepCopy.ts", "../util/src/global.ts", "../util/src/defaults.ts", "../util/src/deferred.ts", "../util/src/environment.ts", "../util/src/errors.ts", "../util/src/obj.ts", "../util/src/subscribe.ts", "../component/src/component.ts", "../component/src/constants.ts", "../component/src/provider.ts", "../component/src/component_container.ts", "../logger/src/logger.ts", "../../node_modules/idb/build/index.js", "../../node_modules/idb/build/wrap-idb-value.js", "../app/src/platformLoggerService.ts", "../app/src/logger.ts", "../app/src/constants.ts", "../app/src/internal.ts", "../app/src/errors.ts", "../app/src/firebaseApp.ts", "../app/src/firebaseServerApp.ts", "../app/src/api.ts", "../app/src/indexeddb.ts", "../app/src/heartbeatService.ts", "../app/src/registerCoreComponents.ts", "../app/src/index.ts", "../app-compat/src/firebaseApp.ts", "../app-compat/src/errors.ts", "../app-compat/src/firebaseNamespaceCore.ts", "../app-compat/src/firebaseNamespace.ts", "../app-compat/src/logger.ts", "../app-compat/src/index.ts", "../app-compat/src/registerCoreComponents.ts", "compat/app/index.ts", "../installations/src/util/constants.ts", "../installations/src/util/errors.ts", "../installations/src/functions/common.ts", "../installations/src/util/sleep.ts", "../installations/src/helpers/generate-fid.ts", "../installations/src/helpers/buffer-to-base64-url-safe.ts", "../installations/src/util/get-key.ts", "../installations/src/helpers/fid-changed.ts", "../installations/src/helpers/idb-manager.ts", "../installations/src/helpers/get-installation-entry.ts", "../installations/src/functions/create-installation-request.ts", "../installations/src/functions/generate-auth-token-request.ts", "../installations/src/helpers/refresh-auth-token.ts", "../installations/src/api/get-token.ts", "../installations/src/helpers/extract-app-config.ts", "../installations/src/functions/config.ts", "../installations/src/api/get-id.ts", "../installations/src/index.ts", "../performance/src/services/api_service.ts", "../performance/src/constants.ts", "../performance/src/utils/errors.ts", "../performance/src/utils/console_logger.ts", "../performance/src/services/iid_service.ts", "../performance/src/services/settings_service.ts", "../performance/src/utils/string_merger.ts", "../performance/src/utils/attributes_utils.ts", "../performance/src/utils/app_utils.ts", "../performance/src/services/remote_config_service.ts", "../performance/src/services/initialization_service.ts", "../performance/src/services/transport_service.ts", "../performance/src/services/perf_logger.ts", "../performance/src/utils/metric_utils.ts", "../performance/src/resources/trace.ts", "../performance/src/resources/network_request.ts", "../performance/src/services/oob_resources_service.ts", "../performance/src/controllers/perf.ts", "../performance/src/index.ts", "../performance-compat/src/performance.ts", "../util/src/compat.ts", "../performance-compat/src/index.ts", "compat/index.perf.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nconst stringToByteArray = function (str: string): number[] {\n  // TODO(user): Use native implementations if/when available\n  const out: number[] = [];\n  let p = 0;\n  for (let i = 0; i < str.length; i++) {\n    let c = str.charCodeAt(i);\n    if (c < 128) {\n      out[p++] = c;\n    } else if (c < 2048) {\n      out[p++] = (c >> 6) | 192;\n      out[p++] = (c & 63) | 128;\n    } else if (\n      (c & 0xfc00) === 0xd800 &&\n      i + 1 < str.length &&\n      (str.charCodeAt(i + 1) & 0xfc00) === 0xdc00\n    ) {\n      // Surrogate Pair\n      c = 0x10000 + ((c & 0x03ff) << 10) + (str.charCodeAt(++i) & 0x03ff);\n      out[p++] = (c >> 18) | 240;\n      out[p++] = ((c >> 12) & 63) | 128;\n      out[p++] = ((c >> 6) & 63) | 128;\n      out[p++] = (c & 63) | 128;\n    } else {\n      out[p++] = (c >> 12) | 224;\n      out[p++] = ((c >> 6) & 63) | 128;\n      out[p++] = (c & 63) | 128;\n    }\n  }\n  return out;\n};\n\n/**\n * Turns an array of numbers into the string given by the concatenation of the\n * characters to which the numbers correspond.\n * @param bytes Array of numbers representing characters.\n * @return Stringification of the array.\n */\nconst byteArrayToString = function (bytes: number[]): string {\n  // TODO(user): Use native implementations if/when available\n  const out: string[] = [];\n  let pos = 0,\n    c = 0;\n  while (pos < bytes.length) {\n    const c1 = bytes[pos++];\n    if (c1 < 128) {\n      out[c++] = String.fromCharCode(c1);\n    } else if (c1 > 191 && c1 < 224) {\n      const c2 = bytes[pos++];\n      out[c++] = String.fromCharCode(((c1 & 31) << 6) | (c2 & 63));\n    } else if (c1 > 239 && c1 < 365) {\n      // Surrogate Pair\n      const c2 = bytes[pos++];\n      const c3 = bytes[pos++];\n      const c4 = bytes[pos++];\n      const u =\n        (((c1 & 7) << 18) | ((c2 & 63) << 12) | ((c3 & 63) << 6) | (c4 & 63)) -\n        0x10000;\n      out[c++] = String.fromCharCode(0xd800 + (u >> 10));\n      out[c++] = String.fromCharCode(0xdc00 + (u & 1023));\n    } else {\n      const c2 = bytes[pos++];\n      const c3 = bytes[pos++];\n      out[c++] = String.fromCharCode(\n        ((c1 & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63)\n      );\n    }\n  }\n  return out.join('');\n};\n\ninterface Base64 {\n  byteToCharMap_: { [key: number]: string } | null;\n  charToByteMap_: { [key: string]: number } | null;\n  byteToCharMapWebSafe_: { [key: number]: string } | null;\n  charToByteMapWebSafe_: { [key: string]: number } | null;\n  ENCODED_VALS_BASE: string;\n  readonly ENCODED_VALS: string;\n  readonly ENCODED_VALS_WEBSAFE: string;\n  HAS_NATIVE_SUPPORT: boolean;\n  encodeByteArray(input: number[] | Uint8Array, webSafe?: boolean): string;\n  encodeString(input: string, webSafe?: boolean): string;\n  decodeString(input: string, webSafe: boolean): string;\n  decodeStringToByteArray(input: string, webSafe: boolean): number[];\n  init_(): void;\n}\n\n// We define it as an object literal instead of a class because a class compiled down to es5 can't\n// be treeshaked. https://github.com/rollup/rollup/issues/1691\n// Static lookup maps, lazily populated by init_()\nexport const base64: Base64 = {\n  /**\n   * Maps bytes to characters.\n   */\n  byteToCharMap_: null,\n\n  /**\n   * Maps characters to bytes.\n   */\n  charToByteMap_: null,\n\n  /**\n   * Maps bytes to websafe characters.\n   * @private\n   */\n  byteToCharMapWebSafe_: null,\n\n  /**\n   * Maps websafe characters to bytes.\n   * @private\n   */\n  charToByteMapWebSafe_: null,\n\n  /**\n   * Our default alphabet, shared between\n   * ENCODED_VALS and ENCODED_VALS_WEBSAFE\n   */\n  ENCODED_VALS_BASE:\n    'ABCDEFGHIJKLMNOPQRSTUVWXYZ' + 'abcdefghijklmnopqrstuvwxyz' + '0123456789',\n\n  /**\n   * Our default alphabet. Value 64 (=) is special; it means \"nothing.\"\n   */\n  get ENCODED_VALS() {\n    return this.ENCODED_VALS_BASE + '+/=';\n  },\n\n  /**\n   * Our websafe alphabet.\n   */\n  get ENCODED_VALS_WEBSAFE() {\n    return this.ENCODED_VALS_BASE + '-_.';\n  },\n\n  /**\n   * Whether this browser supports the atob and btoa functions. This extension\n   * started at Mozilla but is now implemented by many browsers. We use the\n   * ASSUME_* variables to avoid pulling in the full useragent detection library\n   * but still allowing the standard per-browser compilations.\n   *\n   */\n  HAS_NATIVE_SUPPORT: typeof atob === 'function',\n\n  /**\n   * Base64-encode an array of bytes.\n   *\n   * @param input An array of bytes (numbers with\n   *     value in [0, 255]) to encode.\n   * @param webSafe Boolean indicating we should use the\n   *     alternative alphabet.\n   * @return The base64 encoded string.\n   */\n  encodeByteArray(input: number[] | Uint8Array, webSafe?: boolean): string {\n    if (!Array.isArray(input)) {\n      throw Error('encodeByteArray takes an array as a parameter');\n    }\n\n    this.init_();\n\n    const byteToCharMap = webSafe\n      ? this.byteToCharMapWebSafe_!\n      : this.byteToCharMap_!;\n\n    const output = [];\n\n    for (let i = 0; i < input.length; i += 3) {\n      const byte1 = input[i];\n      const haveByte2 = i + 1 < input.length;\n      const byte2 = haveByte2 ? input[i + 1] : 0;\n      const haveByte3 = i + 2 < input.length;\n      const byte3 = haveByte3 ? input[i + 2] : 0;\n\n      const outByte1 = byte1 >> 2;\n      const outByte2 = ((byte1 & 0x03) << 4) | (byte2 >> 4);\n      let outByte3 = ((byte2 & 0x0f) << 2) | (byte3 >> 6);\n      let outByte4 = byte3 & 0x3f;\n\n      if (!haveByte3) {\n        outByte4 = 64;\n\n        if (!haveByte2) {\n          outByte3 = 64;\n        }\n      }\n\n      output.push(\n        byteToCharMap[outByte1],\n        byteToCharMap[outByte2],\n        byteToCharMap[outByte3],\n        byteToCharMap[outByte4]\n      );\n    }\n\n    return output.join('');\n  },\n\n  /**\n   * Base64-encode a string.\n   *\n   * @param input A string to encode.\n   * @param webSafe If true, we should use the\n   *     alternative alphabet.\n   * @return The base64 encoded string.\n   */\n  encodeString(input: string, webSafe?: boolean): string {\n    // Shortcut for Mozilla browsers that implement\n    // a native base64 encoder in the form of \"btoa/atob\"\n    if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n      return btoa(input);\n    }\n    return this.encodeByteArray(stringToByteArray(input), webSafe);\n  },\n\n  /**\n   * Base64-decode a string.\n   *\n   * @param input to decode.\n   * @param webSafe True if we should use the\n   *     alternative alphabet.\n   * @return string representing the decoded value.\n   */\n  decodeString(input: string, webSafe: boolean): string {\n    // Shortcut for Mozilla browsers that implement\n    // a native base64 encoder in the form of \"btoa/atob\"\n    if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n      return atob(input);\n    }\n    return byteArrayToString(this.decodeStringToByteArray(input, webSafe));\n  },\n\n  /**\n   * Base64-decode a string.\n   *\n   * In base-64 decoding, groups of four characters are converted into three\n   * bytes.  If the encoder did not apply padding, the input length may not\n   * be a multiple of 4.\n   *\n   * In this case, the last group will have fewer than 4 characters, and\n   * padding will be inferred.  If the group has one or two characters, it decodes\n   * to one byte.  If the group has three characters, it decodes to two bytes.\n   *\n   * @param input Input to decode.\n   * @param webSafe True if we should use the web-safe alphabet.\n   * @return bytes representing the decoded value.\n   */\n  decodeStringToByteArray(input: string, webSafe: boolean): number[] {\n    this.init_();\n\n    const charToByteMap = webSafe\n      ? this.charToByteMapWebSafe_!\n      : this.charToByteMap_!;\n\n    const output: number[] = [];\n\n    for (let i = 0; i < input.length; ) {\n      const byte1 = charToByteMap[input.charAt(i++)];\n\n      const haveByte2 = i < input.length;\n      const byte2 = haveByte2 ? charToByteMap[input.charAt(i)] : 0;\n      ++i;\n\n      const haveByte3 = i < input.length;\n      const byte3 = haveByte3 ? charToByteMap[input.charAt(i)] : 64;\n      ++i;\n\n      const haveByte4 = i < input.length;\n      const byte4 = haveByte4 ? charToByteMap[input.charAt(i)] : 64;\n      ++i;\n\n      if (byte1 == null || byte2 == null || byte3 == null || byte4 == null) {\n        throw new DecodeBase64StringError();\n      }\n\n      const outByte1 = (byte1 << 2) | (byte2 >> 4);\n      output.push(outByte1);\n\n      if (byte3 !== 64) {\n        const outByte2 = ((byte2 << 4) & 0xf0) | (byte3 >> 2);\n        output.push(outByte2);\n\n        if (byte4 !== 64) {\n          const outByte3 = ((byte3 << 6) & 0xc0) | byte4;\n          output.push(outByte3);\n        }\n      }\n    }\n\n    return output;\n  },\n\n  /**\n   * Lazy static initialization function. Called before\n   * accessing any of the static map variables.\n   * @private\n   */\n  init_() {\n    if (!this.byteToCharMap_) {\n      this.byteToCharMap_ = {};\n      this.charToByteMap_ = {};\n      this.byteToCharMapWebSafe_ = {};\n      this.charToByteMapWebSafe_ = {};\n\n      // We want quick mappings back and forth, so we precompute two maps.\n      for (let i = 0; i < this.ENCODED_VALS.length; i++) {\n        this.byteToCharMap_[i] = this.ENCODED_VALS.charAt(i);\n        this.charToByteMap_[this.byteToCharMap_[i]] = i;\n        this.byteToCharMapWebSafe_[i] = this.ENCODED_VALS_WEBSAFE.charAt(i);\n        this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[i]] = i;\n\n        // Be forgiving when decoding and correctly decode both encodings.\n        if (i >= this.ENCODED_VALS_BASE.length) {\n          this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(i)] = i;\n          this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(i)] = i;\n        }\n      }\n    }\n  }\n};\n\n/**\n * An error encountered while decoding base64 string.\n */\nexport class DecodeBase64StringError extends Error {\n  readonly name = 'DecodeBase64StringError';\n}\n\n/**\n * URL-safe base64 encoding\n */\nexport const base64Encode = function (str: string): string {\n  const utf8Bytes = stringToByteArray(str);\n  return base64.encodeByteArray(utf8Bytes, true);\n};\n\n/**\n * URL-safe base64 encoding (without \".\" padding in the end).\n * e.g. Used in JSON Web Token (JWT) parts.\n */\nexport const base64urlEncodeWithoutPadding = function (str: string): string {\n  // Use base64url encoding and remove padding in the end (dot characters).\n  return base64Encode(str).replace(/\\./g, '');\n};\n\n/**\n * URL-safe base64 decoding\n *\n * NOTE: DO NOT use the global atob() function - it does NOT support the\n * base64Url variant encoding.\n *\n * @param str To be decoded\n * @return Decoded result, if possible\n */\nexport const base64Decode = function (str: string): string | null {\n  try {\n    return base64.decodeString(str, true);\n  } catch (e) {\n    console.error('base64Decode failed: ', e);\n  }\n  return null;\n};\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Do a deep-copy of basic JavaScript Objects or Arrays.\n */\nexport function deepCopy<T>(value: T): T {\n  return deepExtend(undefined, value) as T;\n}\n\n/**\n * Copy properties from source to target (recursively allows extension\n * of Objects and Arrays).  Scalar values in the target are over-written.\n * If target is undefined, an object of the appropriate type will be created\n * (and returned).\n *\n * We recursively copy all child properties of plain Objects in the source- so\n * that namespace- like dictionaries are merged.\n *\n * Note that the target can be a function, in which case the properties in\n * the source Object are copied onto it as static properties of the Function.\n *\n * Note: we don't merge __proto__ to prevent prototype pollution\n */\nexport function deepExtend(target: unknown, source: unknown): unknown {\n  if (!(source instanceof Object)) {\n    return source;\n  }\n\n  switch (source.constructor) {\n    case Date:\n      // Treat Dates like scalars; if the target date object had any child\n      // properties - they will be lost!\n      const dateValue = source as Date;\n      return new Date(dateValue.getTime());\n\n    case Object:\n      if (target === undefined) {\n        target = {};\n      }\n      break;\n    case Array:\n      // Always copy the array source and overwrite the target.\n      target = [];\n      break;\n\n    default:\n      // Not a plain Object - treat it as a scalar.\n      return source;\n  }\n\n  for (const prop in source) {\n    // use isValidKey to guard against prototype pollution. See https://snyk.io/vuln/SNYK-JS-LODASH-450202\n    if (!source.hasOwnProperty(prop) || !isValidKey(prop)) {\n      continue;\n    }\n    (target as Record<string, unknown>)[prop] = deepExtend(\n      (target as Record<string, unknown>)[prop],\n      (source as Record<string, unknown>)[prop]\n    );\n  }\n\n  return target;\n}\n\nfunction isValidKey(key: string): boolean {\n  return key !== '__proto__';\n}\n", "/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Polyfill for `globalThis` object.\n * @returns the `globalThis` object for the given environment.\n * @public\n */\nexport function getGlobal(): typeof globalThis {\n  if (typeof self !== 'undefined') {\n    return self;\n  }\n  if (typeof window !== 'undefined') {\n    return window;\n  }\n  if (typeof global !== 'undefined') {\n    return global;\n  }\n  throw new Error('Unable to locate global object.');\n}\n", "/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { base64Decode } from './crypt';\nimport { getGlobal } from './global';\n\n/**\n * Keys for experimental properties on the `FirebaseDefaults` object.\n * @public\n */\nexport type ExperimentalKey = 'authTokenSyncURL' | 'authIdTokenMaxAge';\n\n/**\n * An object that can be injected into the environment as __FIREBASE_DEFAULTS__,\n * either as a property of globalThis, a shell environment variable, or a\n * cookie.\n *\n * This object can be used to automatically configure and initialize\n * a Firebase app as well as any emulators.\n *\n * @public\n */\nexport interface FirebaseDefaults {\n  config?: Record<string, string>;\n  emulatorHosts?: Record<string, string>;\n  _authTokenSyncURL?: string;\n  _authIdTokenMaxAge?: number;\n  /**\n   * Override Firebase's runtime environment detection and\n   * force the SDK to act as if it were in the specified environment.\n   */\n  forceEnvironment?: 'browser' | 'node';\n  [key: string]: unknown;\n}\n\ndeclare global {\n  // Need `var` for this to work.\n  // eslint-disable-next-line no-var\n  var __FIREBASE_DEFAULTS__: FirebaseDefaults | undefined;\n}\n\nconst getDefaultsFromGlobal = (): FirebaseDefaults | undefined =>\n  getGlobal().__FIREBASE_DEFAULTS__;\n\n/**\n * Attempt to read defaults from a JSON string provided to\n * process(.)env(.)__FIREBASE_DEFAULTS__ or a JSON file whose path is in\n * process(.)env(.)__FIREBASE_DEFAULTS_PATH__\n * The dots are in parens because certain compilers (Vite?) cannot\n * handle seeing that variable in comments.\n * See https://github.com/firebase/firebase-js-sdk/issues/6838\n */\nconst getDefaultsFromEnvVariable = (): FirebaseDefaults | undefined => {\n  if (typeof process === 'undefined' || typeof process.env === 'undefined') {\n    return;\n  }\n  const defaultsJsonString = process.env.__FIREBASE_DEFAULTS__;\n  if (defaultsJsonString) {\n    return JSON.parse(defaultsJsonString);\n  }\n};\n\nconst getDefaultsFromCookie = (): FirebaseDefaults | undefined => {\n  if (typeof document === 'undefined') {\n    return;\n  }\n  let match;\n  try {\n    match = document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/);\n  } catch (e) {\n    // Some environments such as Angular Universal SSR have a\n    // `document` object but error on accessing `document.cookie`.\n    return;\n  }\n  const decoded = match && base64Decode(match[1]);\n  return decoded && JSON.parse(decoded);\n};\n\n/**\n * Get the __FIREBASE_DEFAULTS__ object. It checks in order:\n * (1) if such an object exists as a property of `globalThis`\n * (2) if such an object was provided on a shell environment variable\n * (3) if such an object exists in a cookie\n * @public\n */\nexport const getDefaults = (): FirebaseDefaults | undefined => {\n  try {\n    return (\n      getDefaultsFromGlobal() ||\n      getDefaultsFromEnvVariable() ||\n      getDefaultsFromCookie()\n    );\n  } catch (e) {\n    /**\n     * Catch-all for being unable to get __FIREBASE_DEFAULTS__ due\n     * to any environment case we have not accounted for. Log to\n     * info instead of swallowing so we can find these unknown cases\n     * and add paths for them if needed.\n     */\n    console.info(`Unable to get __FIREBASE_DEFAULTS__ due to: ${e}`);\n    return;\n  }\n};\n\n/**\n * Returns emulator host stored in the __FIREBASE_DEFAULTS__ object\n * for the given product.\n * @returns a URL host formatted like `127.0.0.1:9999` or `[::1]:4000` if available\n * @public\n */\nexport const getDefaultEmulatorHost = (\n  productName: string\n): string | undefined => getDefaults()?.emulatorHosts?.[productName];\n\n/**\n * Returns emulator hostname and port stored in the __FIREBASE_DEFAULTS__ object\n * for the given product.\n * @returns a pair of hostname and port like `[\"::1\", 4000]` if available\n * @public\n */\nexport const getDefaultEmulatorHostnameAndPort = (\n  productName: string\n): [hostname: string, port: number] | undefined => {\n  const host = getDefaultEmulatorHost(productName);\n  if (!host) {\n    return undefined;\n  }\n  const separatorIndex = host.lastIndexOf(':'); // Finding the last since IPv6 addr also has colons.\n  if (separatorIndex <= 0 || separatorIndex + 1 === host.length) {\n    throw new Error(`Invalid host ${host} with no separate hostname and port!`);\n  }\n  // eslint-disable-next-line no-restricted-globals\n  const port = parseInt(host.substring(separatorIndex + 1), 10);\n  if (host[0] === '[') {\n    // Bracket-quoted `[ipv6addr]:port` => return \"ipv6addr\" (without brackets).\n    return [host.substring(1, separatorIndex - 1), port];\n  } else {\n    return [host.substring(0, separatorIndex), port];\n  }\n};\n\n/**\n * Returns Firebase app config stored in the __FIREBASE_DEFAULTS__ object.\n * @public\n */\nexport const getDefaultAppConfig = (): Record<string, string> | undefined =>\n  getDefaults()?.config;\n\n/**\n * Returns an experimental setting on the __FIREBASE_DEFAULTS__ object (properties\n * prefixed by \"_\")\n * @public\n */\nexport const getExperimentalSetting = <T extends ExperimentalKey>(\n  name: T\n): FirebaseDefaults[`_${T}`] =>\n  getDefaults()?.[`_${name}`] as FirebaseDefaults[`_${T}`];\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport class Deferred<R> {\n  promise: Promise<R>;\n  reject: (value?: unknown) => void = () => {};\n  resolve: (value?: unknown) => void = () => {};\n  constructor() {\n    this.promise = new Promise((resolve, reject) => {\n      this.resolve = resolve as (value?: unknown) => void;\n      this.reject = reject as (value?: unknown) => void;\n    });\n  }\n\n  /**\n   * Our API internals are not promisified and cannot because our callback APIs have subtle expectations around\n   * invoking promises inline, which Promises are forbidden to do. This method accepts an optional node-style callback\n   * and returns a node-style callback which will resolve or reject the Deferred's promise.\n   */\n  wrapCallback(\n    callback?: (error?: unknown, value?: unknown) => void\n  ): (error: unknown, value?: unknown) => void {\n    return (error, value?) => {\n      if (error) {\n        this.reject(error);\n      } else {\n        this.resolve(value);\n      }\n      if (typeof callback === 'function') {\n        // Attaching noop handler just in case developer wasn't expecting\n        // promises\n        this.promise.catch(() => {});\n\n        // Some of our callbacks don't expect a value and our own tests\n        // assert that the parameter length is 1\n        if (callback.length === 1) {\n          callback(error);\n        } else {\n          callback(error, value);\n        }\n      }\n    };\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CONSTANTS } from './constants';\nimport { getDefaults } from './defaults';\n\n/**\n * Type placeholder for `WorkerGlobalScope` from `webworker`\n */\ndeclare class WorkerGlobalScope {}\n\n/**\n * Returns navigator.userAgent string or '' if it's not defined.\n * @return user agent string\n */\nexport function getUA(): string {\n  if (\n    typeof navigator !== 'undefined' &&\n    typeof navigator['userAgent'] === 'string'\n  ) {\n    return navigator['userAgent'];\n  } else {\n    return '';\n  }\n}\n\n/**\n * Detect Cordova / PhoneGap / Ionic frameworks on a mobile device.\n *\n * Deliberately does not rely on checking `file://` URLs (as this fails PhoneGap\n * in the Ripple emulator) nor Cordova `onDeviceReady`, which would normally\n * wait for a callback.\n */\nexport function isMobileCordova(): boolean {\n  return (\n    typeof window !== 'undefined' &&\n    // @ts-ignore Setting up an broadly applicable index signature for Window\n    // just to deal with this case would probably be a bad idea.\n    !!(window['cordova'] || window['phonegap'] || window['PhoneGap']) &&\n    /ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(getUA())\n  );\n}\n\n/**\n * Detect Node.js.\n *\n * @return true if Node.js environment is detected or specified.\n */\n// Node detection logic from: https://github.com/iliakan/detect-node/\nexport function isNode(): boolean {\n  const forceEnvironment = getDefaults()?.forceEnvironment;\n  if (forceEnvironment === 'node') {\n    return true;\n  } else if (forceEnvironment === 'browser') {\n    return false;\n  }\n\n  try {\n    return (\n      Object.prototype.toString.call(global.process) === '[object process]'\n    );\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * Detect Browser Environment.\n * Note: This will return true for certain test frameworks that are incompletely\n * mimicking a browser, and should not lead to assuming all browser APIs are\n * available.\n */\nexport function isBrowser(): boolean {\n  return typeof window !== 'undefined' || isWebWorker();\n}\n\n/**\n * Detect Web Worker context.\n */\nexport function isWebWorker(): boolean {\n  return (\n    typeof WorkerGlobalScope !== 'undefined' &&\n    typeof self !== 'undefined' &&\n    self instanceof WorkerGlobalScope\n  );\n}\n\n/**\n * Detect Cloudflare Worker context.\n */\nexport function isCloudflareWorker(): boolean {\n  return (\n    typeof navigator !== 'undefined' &&\n    navigator.userAgent === 'Cloudflare-Workers'\n  );\n}\n\n/**\n * Detect browser extensions (Chrome and Firefox at least).\n */\ninterface BrowserRuntime {\n  id?: unknown;\n}\ndeclare const chrome: { runtime?: BrowserRuntime };\ndeclare const browser: { runtime?: BrowserRuntime };\nexport function isBrowserExtension(): boolean {\n  const runtime =\n    typeof chrome === 'object'\n      ? chrome.runtime\n      : typeof browser === 'object'\n      ? browser.runtime\n      : undefined;\n  return typeof runtime === 'object' && runtime.id !== undefined;\n}\n\n/**\n * Detect React Native.\n *\n * @return true if ReactNative environment is detected.\n */\nexport function isReactNative(): boolean {\n  return (\n    typeof navigator === 'object' && navigator['product'] === 'ReactNative'\n  );\n}\n\n/** Detects Electron apps. */\nexport function isElectron(): boolean {\n  return getUA().indexOf('Electron/') >= 0;\n}\n\n/** Detects Internet Explorer. */\nexport function isIE(): boolean {\n  const ua = getUA();\n  return ua.indexOf('MSIE ') >= 0 || ua.indexOf('Trident/') >= 0;\n}\n\n/** Detects Universal Windows Platform apps. */\nexport function isUWP(): boolean {\n  return getUA().indexOf('MSAppHost/') >= 0;\n}\n\n/**\n * Detect whether the current SDK build is the Node version.\n *\n * @return true if it's the Node SDK build.\n */\nexport function isNodeSdk(): boolean {\n  return CONSTANTS.NODE_CLIENT === true || CONSTANTS.NODE_ADMIN === true;\n}\n\n/** Returns true if we are running in Safari. */\nexport function isSafari(): boolean {\n  return (\n    !isNode() &&\n    !!navigator.userAgent &&\n    navigator.userAgent.includes('Safari') &&\n    !navigator.userAgent.includes('Chrome')\n  );\n}\n\n/**\n * This method checks if indexedDB is supported by current browser/service worker context\n * @return true if indexedDB is supported by current browser/service worker context\n */\nexport function isIndexedDBAvailable(): boolean {\n  try {\n    return typeof indexedDB === 'object';\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * This method validates browser/sw context for indexedDB by opening a dummy indexedDB database and reject\n * if errors occur during the database open operation.\n *\n * @throws exception if current browser/sw context can't run idb.open (ex: Safari iframe, Firefox\n * private browsing)\n */\nexport function validateIndexedDBOpenable(): Promise<boolean> {\n  return new Promise((resolve, reject) => {\n    try {\n      let preExist: boolean = true;\n      const DB_CHECK_NAME =\n        'validate-browser-context-for-indexeddb-analytics-module';\n      const request = self.indexedDB.open(DB_CHECK_NAME);\n      request.onsuccess = () => {\n        request.result.close();\n        // delete database only when it doesn't pre-exist\n        if (!preExist) {\n          self.indexedDB.deleteDatabase(DB_CHECK_NAME);\n        }\n        resolve(true);\n      };\n      request.onupgradeneeded = () => {\n        preExist = false;\n      };\n\n      request.onerror = () => {\n        reject(request.error?.message || '');\n      };\n    } catch (error) {\n      reject(error);\n    }\n  });\n}\n\n/**\n *\n * This method checks whether cookie is enabled within current browser\n * @return true if cookie is enabled within current browser\n */\nexport function areCookiesEnabled(): boolean {\n  if (typeof navigator === 'undefined' || !navigator.cookieEnabled) {\n    return false;\n  }\n  return true;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Standardized Firebase Error.\n *\n * Usage:\n *\n *   // TypeScript string literals for type-safe codes\n *   type Err =\n *     'unknown' |\n *     'object-not-found'\n *     ;\n *\n *   // Closure enum for type-safe error codes\n *   // at-enum {string}\n *   var Err = {\n *     UNKNOWN: 'unknown',\n *     OBJECT_NOT_FOUND: 'object-not-found',\n *   }\n *\n *   let errors: Map<Err, string> = {\n *     'generic-error': \"Unknown error\",\n *     'file-not-found': \"Could not find file: {$file}\",\n *   };\n *\n *   // Type-safe function - must pass a valid error code as param.\n *   let error = new ErrorFactory<Err>('service', 'Service', errors);\n *\n *   ...\n *   throw error.create(Err.GENERIC);\n *   ...\n *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});\n *   ...\n *   // Service: Could not file file: foo.txt (service/file-not-found).\n *\n *   catch (e) {\n *     assert(e.message === \"Could not find file: foo.txt.\");\n *     if ((e as FirebaseError)?.code === 'service/file-not-found') {\n *       console.log(\"Could not read file: \" + e['file']);\n *     }\n *   }\n */\n\nexport type ErrorMap<ErrorCode extends string> = {\n  readonly [K in ErrorCode]: string;\n};\n\nconst ERROR_NAME = 'FirebaseError';\n\nexport interface StringLike {\n  toString(): string;\n}\n\nexport interface ErrorData {\n  [key: string]: unknown;\n}\n\n// Based on code from:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types\nexport class FirebaseError extends Error {\n  /** The custom name for all FirebaseErrors. */\n  readonly name: string = ERROR_NAME;\n\n  constructor(\n    /** The error code for this error. */\n    readonly code: string,\n    message: string,\n    /** Custom data for this error. */\n    public customData?: Record<string, unknown>\n  ) {\n    super(message);\n\n    // Fix For ES5\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, FirebaseError.prototype);\n\n    // Maintains proper stack trace for where our error was thrown.\n    // Only available on V8.\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ErrorFactory.prototype.create);\n    }\n  }\n}\n\nexport class ErrorFactory<\n  ErrorCode extends string,\n  ErrorParams extends { readonly [K in ErrorCode]?: ErrorData } = {}\n> {\n  constructor(\n    private readonly service: string,\n    private readonly serviceName: string,\n    private readonly errors: ErrorMap<ErrorCode>\n  ) {}\n\n  create<K extends ErrorCode>(\n    code: K,\n    ...data: K extends keyof ErrorParams ? [ErrorParams[K]] : []\n  ): FirebaseError {\n    const customData = (data[0] as ErrorData) || {};\n    const fullCode = `${this.service}/${code}`;\n    const template = this.errors[code];\n\n    const message = template ? replaceTemplate(template, customData) : 'Error';\n    // Service Name: Error message (service/code).\n    const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;\n\n    const error = new FirebaseError(fullCode, fullMessage, customData);\n\n    return error;\n  }\n}\n\nfunction replaceTemplate(template: string, data: ErrorData): string {\n  return template.replace(PATTERN, (_, key) => {\n    const value = data[key];\n    return value != null ? String(value) : `<${key}?>`;\n  });\n}\n\nconst PATTERN = /\\{\\$([^}]+)}/g;\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function contains<T extends object>(obj: T, key: string): boolean {\n  return Object.prototype.hasOwnProperty.call(obj, key);\n}\n\nexport function safeGet<T extends object, K extends keyof T>(\n  obj: T,\n  key: K\n): T[K] | undefined {\n  if (Object.prototype.hasOwnProperty.call(obj, key)) {\n    return obj[key];\n  } else {\n    return undefined;\n  }\n}\n\nexport function isEmpty(obj: object): obj is {} {\n  for (const key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport function map<K extends string, V, U>(\n  obj: { [key in K]: V },\n  fn: (value: V, key: K, obj: { [key in K]: V }) => U,\n  contextObj?: unknown\n): { [key in K]: U } {\n  const res: Partial<{ [key in K]: U }> = {};\n  for (const key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n      res[key] = fn.call(contextObj, obj[key], key, obj);\n    }\n  }\n  return res as { [key in K]: U };\n}\n\n/**\n * Deep equal two objects. Support Arrays and Objects.\n */\nexport function deepEqual(a: object, b: object): boolean {\n  if (a === b) {\n    return true;\n  }\n\n  const aKeys = Object.keys(a);\n  const bKeys = Object.keys(b);\n  for (const k of aKeys) {\n    if (!bKeys.includes(k)) {\n      return false;\n    }\n\n    const aProp = (a as Record<string, unknown>)[k];\n    const bProp = (b as Record<string, unknown>)[k];\n    if (isObject(aProp) && isObject(bProp)) {\n      if (!deepEqual(aProp, bProp)) {\n        return false;\n      }\n    } else if (aProp !== bProp) {\n      return false;\n    }\n  }\n\n  for (const k of bKeys) {\n    if (!aKeys.includes(k)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction isObject(thing: unknown): thing is object {\n  return thing !== null && typeof thing === 'object';\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport type NextFn<T> = (value: T) => void;\nexport type ErrorFn = (error: Error) => void;\nexport type CompleteFn = () => void;\n\nexport interface Observer<T> {\n  // Called once for each value in a stream of values.\n  next: NextFn<T>;\n\n  // A stream terminates by a single call to EITHER error() or complete().\n  error: ErrorFn;\n\n  // No events will be sent to next() once complete() is called.\n  complete: CompleteFn;\n}\n\nexport type PartialObserver<T> = Partial<Observer<T>>;\n\n// TODO: Support also Unsubscribe.unsubscribe?\nexport type Unsubscribe = () => void;\n\n/**\n * The Subscribe interface has two forms - passing the inline function\n * callbacks, or a object interface with callback properties.\n */\nexport interface Subscribe<T> {\n  (next?: NextFn<T>, error?: ErrorFn, complete?: CompleteFn): Unsubscribe;\n  (observer: PartialObserver<T>): Unsubscribe;\n}\n\nexport interface Observable<T> {\n  // Subscribe method\n  subscribe: Subscribe<T>;\n}\n\nexport type Executor<T> = (observer: Observer<T>) => void;\n\n/**\n * Helper to make a Subscribe function (just like Promise helps make a\n * Thenable).\n *\n * @param executor Function which can make calls to a single Observer\n *     as a proxy.\n * @param onNoObservers Callback when count of Observers goes to zero.\n */\nexport function createSubscribe<T>(\n  executor: Executor<T>,\n  onNoObservers?: Executor<T>\n): Subscribe<T> {\n  const proxy = new ObserverProxy<T>(executor, onNoObservers);\n  return proxy.subscribe.bind(proxy);\n}\n\n/**\n * Implement fan-out for any number of Observers attached via a subscribe\n * function.\n */\nclass ObserverProxy<T> implements Observer<T> {\n  private observers: Array<Observer<T>> | undefined = [];\n  private unsubscribes: Unsubscribe[] = [];\n  private onNoObservers: Executor<T> | undefined;\n  private observerCount = 0;\n  // Micro-task scheduling by calling task.then().\n  private task = Promise.resolve();\n  private finalized = false;\n  private finalError?: Error;\n\n  /**\n   * @param executor Function which can make calls to a single Observer\n   *     as a proxy.\n   * @param onNoObservers Callback when count of Observers goes to zero.\n   */\n  constructor(executor: Executor<T>, onNoObservers?: Executor<T>) {\n    this.onNoObservers = onNoObservers;\n    // Call the executor asynchronously so subscribers that are called\n    // synchronously after the creation of the subscribe function\n    // can still receive the very first value generated in the executor.\n    this.task\n      .then(() => {\n        executor(this);\n      })\n      .catch(e => {\n        this.error(e);\n      });\n  }\n\n  next(value: T): void {\n    this.forEachObserver((observer: Observer<T>) => {\n      observer.next(value);\n    });\n  }\n\n  error(error: Error): void {\n    this.forEachObserver((observer: Observer<T>) => {\n      observer.error(error);\n    });\n    this.close(error);\n  }\n\n  complete(): void {\n    this.forEachObserver((observer: Observer<T>) => {\n      observer.complete();\n    });\n    this.close();\n  }\n\n  /**\n   * Subscribe function that can be used to add an Observer to the fan-out list.\n   *\n   * - We require that no event is sent to a subscriber synchronously to their\n   *   call to subscribe().\n   */\n  subscribe(\n    nextOrObserver?: NextFn<T> | PartialObserver<T>,\n    error?: ErrorFn,\n    complete?: CompleteFn\n  ): Unsubscribe {\n    let observer: Observer<T>;\n\n    if (\n      nextOrObserver === undefined &&\n      error === undefined &&\n      complete === undefined\n    ) {\n      throw new Error('Missing Observer.');\n    }\n\n    // Assemble an Observer object when passed as callback functions.\n    if (\n      implementsAnyMethods(nextOrObserver as { [key: string]: unknown }, [\n        'next',\n        'error',\n        'complete'\n      ])\n    ) {\n      observer = nextOrObserver as Observer<T>;\n    } else {\n      observer = {\n        next: nextOrObserver as NextFn<T>,\n        error,\n        complete\n      } as Observer<T>;\n    }\n\n    if (observer.next === undefined) {\n      observer.next = noop as NextFn<T>;\n    }\n    if (observer.error === undefined) {\n      observer.error = noop as ErrorFn;\n    }\n    if (observer.complete === undefined) {\n      observer.complete = noop as CompleteFn;\n    }\n\n    const unsub = this.unsubscribeOne.bind(this, this.observers!.length);\n\n    // Attempt to subscribe to a terminated Observable - we\n    // just respond to the Observer with the final error or complete\n    // event.\n    if (this.finalized) {\n      // eslint-disable-next-line @typescript-eslint/no-floating-promises\n      this.task.then(() => {\n        try {\n          if (this.finalError) {\n            observer.error(this.finalError);\n          } else {\n            observer.complete();\n          }\n        } catch (e) {\n          // nothing\n        }\n        return;\n      });\n    }\n\n    this.observers!.push(observer as Observer<T>);\n\n    return unsub;\n  }\n\n  // Unsubscribe is synchronous - we guarantee that no events are sent to\n  // any unsubscribed Observer.\n  private unsubscribeOne(i: number): void {\n    if (this.observers === undefined || this.observers[i] === undefined) {\n      return;\n    }\n\n    delete this.observers[i];\n\n    this.observerCount -= 1;\n    if (this.observerCount === 0 && this.onNoObservers !== undefined) {\n      this.onNoObservers(this);\n    }\n  }\n\n  private forEachObserver(fn: (observer: Observer<T>) => void): void {\n    if (this.finalized) {\n      // Already closed by previous event....just eat the additional values.\n      return;\n    }\n\n    // Since sendOne calls asynchronously - there is no chance that\n    // this.observers will become undefined.\n    for (let i = 0; i < this.observers!.length; i++) {\n      this.sendOne(i, fn);\n    }\n  }\n\n  // Call the Observer via one of it's callback function. We are careful to\n  // confirm that the observe has not been unsubscribed since this asynchronous\n  // function had been queued.\n  private sendOne(i: number, fn: (observer: Observer<T>) => void): void {\n    // Execute the callback asynchronously\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    this.task.then(() => {\n      if (this.observers !== undefined && this.observers[i] !== undefined) {\n        try {\n          fn(this.observers[i]);\n        } catch (e) {\n          // Ignore exceptions raised in Observers or missing methods of an\n          // Observer.\n          // Log error to console. b/31404806\n          if (typeof console !== 'undefined' && console.error) {\n            console.error(e);\n          }\n        }\n      }\n    });\n  }\n\n  private close(err?: Error): void {\n    if (this.finalized) {\n      return;\n    }\n    this.finalized = true;\n    if (err !== undefined) {\n      this.finalError = err;\n    }\n    // Proxy is no longer needed - garbage collect references\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    this.task.then(() => {\n      this.observers = undefined;\n      this.onNoObservers = undefined;\n    });\n  }\n}\n\n/** Turn synchronous function into one called asynchronously. */\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function async(fn: Function, onError?: ErrorFn): Function {\n  return (...args: unknown[]) => {\n    Promise.resolve(true)\n      .then(() => {\n        fn(...args);\n      })\n      .catch((error: Error) => {\n        if (onError) {\n          onError(error);\n        }\n      });\n  };\n}\n\n/**\n * Return true if the object passed in implements any of the named methods.\n */\nfunction implementsAnyMethods(\n  obj: { [key: string]: unknown },\n  methods: string[]\n): boolean {\n  if (typeof obj !== 'object' || obj === null) {\n    return false;\n  }\n\n  for (const method of methods) {\n    if (method in obj && typeof obj[method] === 'function') {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction noop(): void {\n  // do nothing\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  InstantiationMode,\n  InstanceFactory,\n  ComponentType,\n  Dictionary,\n  Name,\n  onInstanceCreatedCallback\n} from './types';\n\n/**\n * Component for service name T, e.g. `auth`, `auth-internal`\n */\nexport class Component<T extends Name = Name> {\n  multipleInstances = false;\n  /**\n   * Properties to be added to the service namespace\n   */\n  serviceProps: Dictionary = {};\n\n  instantiationMode = InstantiationMode.LAZY;\n\n  onInstanceCreated: onInstanceCreatedCallback<T> | null = null;\n\n  /**\n   *\n   * @param name The public service name, e.g. app, auth, firestore, database\n   * @param instanceFactory Service factory responsible for creating the public interface\n   * @param type whether the service provided by the component is public or private\n   */\n  constructor(\n    readonly name: T,\n    readonly instanceFactory: InstanceFactory<T>,\n    readonly type: ComponentType\n  ) {}\n\n  setInstantiationMode(mode: InstantiationMode): this {\n    this.instantiationMode = mode;\n    return this;\n  }\n\n  setMultipleInstances(multipleInstances: boolean): this {\n    this.multipleInstances = multipleInstances;\n    return this;\n  }\n\n  setServiceProps(props: Dictionary): this {\n    this.serviceProps = props;\n    return this;\n  }\n\n  setInstanceCreatedCallback(callback: onInstanceCreatedCallback<T>): this {\n    this.onInstanceCreated = callback;\n    return this;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const DEFAULT_ENTRY_NAME = '[DEFAULT]';\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Deferred } from '@firebase/util';\nimport { ComponentContainer } from './component_container';\nimport { DEFAULT_ENTRY_NAME } from './constants';\nimport {\n  InitializeOptions,\n  InstantiationMode,\n  Name,\n  NameServiceMapping,\n  OnInitCallBack\n} from './types';\nimport { Component } from './component';\n\n/**\n * Provider for instance for service name T, e.g. 'auth', 'auth-internal'\n * NameServiceMapping[T] is an alias for the type of the instance\n */\nexport class Provider<T extends Name> {\n  private component: Component<T> | null = null;\n  private readonly instances: Map<string, NameServiceMapping[T]> = new Map();\n  private readonly instancesDeferred: Map<\n    string,\n    Deferred<NameServiceMapping[T]>\n  > = new Map();\n  private readonly instancesOptions: Map<string, Record<string, unknown>> =\n    new Map();\n  private onInitCallbacks: Map<string, Set<OnInitCallBack<T>>> = new Map();\n\n  constructor(\n    private readonly name: T,\n    private readonly container: ComponentContainer\n  ) {}\n\n  /**\n   * @param identifier A provider can provide multiple instances of a service\n   * if this.component.multipleInstances is true.\n   */\n  get(identifier?: string): Promise<NameServiceMapping[T]> {\n    // if multipleInstances is not supported, use the default name\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(identifier);\n\n    if (!this.instancesDeferred.has(normalizedIdentifier)) {\n      const deferred = new Deferred<NameServiceMapping[T]>();\n      this.instancesDeferred.set(normalizedIdentifier, deferred);\n\n      if (\n        this.isInitialized(normalizedIdentifier) ||\n        this.shouldAutoInitialize()\n      ) {\n        // initialize the service if it can be auto-initialized\n        try {\n          const instance = this.getOrInitializeService({\n            instanceIdentifier: normalizedIdentifier\n          });\n          if (instance) {\n            deferred.resolve(instance);\n          }\n        } catch (e) {\n          // when the instance factory throws an exception during get(), it should not cause\n          // a fatal error. We just return the unresolved promise in this case.\n        }\n      }\n    }\n\n    return this.instancesDeferred.get(normalizedIdentifier)!.promise;\n  }\n\n  /**\n   *\n   * @param options.identifier A provider can provide multiple instances of a service\n   * if this.component.multipleInstances is true.\n   * @param options.optional If optional is false or not provided, the method throws an error when\n   * the service is not immediately available.\n   * If optional is true, the method returns null if the service is not immediately available.\n   */\n  getImmediate(options: {\n    identifier?: string;\n    optional: true;\n  }): NameServiceMapping[T] | null;\n  getImmediate(options?: {\n    identifier?: string;\n    optional?: false;\n  }): NameServiceMapping[T];\n  getImmediate(options?: {\n    identifier?: string;\n    optional?: boolean;\n  }): NameServiceMapping[T] | null {\n    // if multipleInstances is not supported, use the default name\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(\n      options?.identifier\n    );\n    const optional = options?.optional ?? false;\n\n    if (\n      this.isInitialized(normalizedIdentifier) ||\n      this.shouldAutoInitialize()\n    ) {\n      try {\n        return this.getOrInitializeService({\n          instanceIdentifier: normalizedIdentifier\n        });\n      } catch (e) {\n        if (optional) {\n          return null;\n        } else {\n          throw e;\n        }\n      }\n    } else {\n      // In case a component is not initialized and should/cannot be auto-initialized at the moment, return null if the optional flag is set, or throw\n      if (optional) {\n        return null;\n      } else {\n        throw Error(`Service ${this.name} is not available`);\n      }\n    }\n  }\n\n  getComponent(): Component<T> | null {\n    return this.component;\n  }\n\n  setComponent(component: Component<T>): void {\n    if (component.name !== this.name) {\n      throw Error(\n        `Mismatching Component ${component.name} for Provider ${this.name}.`\n      );\n    }\n\n    if (this.component) {\n      throw Error(`Component for ${this.name} has already been provided`);\n    }\n\n    this.component = component;\n\n    // return early without attempting to initialize the component if the component requires explicit initialization (calling `Provider.initialize()`)\n    if (!this.shouldAutoInitialize()) {\n      return;\n    }\n\n    // if the service is eager, initialize the default instance\n    if (isComponentEager(component)) {\n      try {\n        this.getOrInitializeService({ instanceIdentifier: DEFAULT_ENTRY_NAME });\n      } catch (e) {\n        // when the instance factory for an eager Component throws an exception during the eager\n        // initialization, it should not cause a fatal error.\n        // TODO: Investigate if we need to make it configurable, because some component may want to cause\n        // a fatal error in this case?\n      }\n    }\n\n    // Create service instances for the pending promises and resolve them\n    // NOTE: if this.multipleInstances is false, only the default instance will be created\n    // and all promises with resolve with it regardless of the identifier.\n    for (const [\n      instanceIdentifier,\n      instanceDeferred\n    ] of this.instancesDeferred.entries()) {\n      const normalizedIdentifier =\n        this.normalizeInstanceIdentifier(instanceIdentifier);\n\n      try {\n        // `getOrInitializeService()` should always return a valid instance since a component is guaranteed. use ! to make typescript happy.\n        const instance = this.getOrInitializeService({\n          instanceIdentifier: normalizedIdentifier\n        })!;\n        instanceDeferred.resolve(instance);\n      } catch (e) {\n        // when the instance factory throws an exception, it should not cause\n        // a fatal error. We just leave the promise unresolved.\n      }\n    }\n  }\n\n  clearInstance(identifier: string = DEFAULT_ENTRY_NAME): void {\n    this.instancesDeferred.delete(identifier);\n    this.instancesOptions.delete(identifier);\n    this.instances.delete(identifier);\n  }\n\n  // app.delete() will call this method on every provider to delete the services\n  // TODO: should we mark the provider as deleted?\n  async delete(): Promise<void> {\n    const services = Array.from(this.instances.values());\n\n    await Promise.all([\n      ...services\n        .filter(service => 'INTERNAL' in service) // legacy services\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        .map(service => (service as any).INTERNAL!.delete()),\n      ...services\n        .filter(service => '_delete' in service) // modularized services\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        .map(service => (service as any)._delete())\n    ]);\n  }\n\n  isComponentSet(): boolean {\n    return this.component != null;\n  }\n\n  isInitialized(identifier: string = DEFAULT_ENTRY_NAME): boolean {\n    return this.instances.has(identifier);\n  }\n\n  getOptions(identifier: string = DEFAULT_ENTRY_NAME): Record<string, unknown> {\n    return this.instancesOptions.get(identifier) || {};\n  }\n\n  initialize(opts: InitializeOptions = {}): NameServiceMapping[T] {\n    const { options = {} } = opts;\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(\n      opts.instanceIdentifier\n    );\n    if (this.isInitialized(normalizedIdentifier)) {\n      throw Error(\n        `${this.name}(${normalizedIdentifier}) has already been initialized`\n      );\n    }\n\n    if (!this.isComponentSet()) {\n      throw Error(`Component ${this.name} has not been registered yet`);\n    }\n\n    const instance = this.getOrInitializeService({\n      instanceIdentifier: normalizedIdentifier,\n      options\n    })!;\n\n    // resolve any pending promise waiting for the service instance\n    for (const [\n      instanceIdentifier,\n      instanceDeferred\n    ] of this.instancesDeferred.entries()) {\n      const normalizedDeferredIdentifier =\n        this.normalizeInstanceIdentifier(instanceIdentifier);\n      if (normalizedIdentifier === normalizedDeferredIdentifier) {\n        instanceDeferred.resolve(instance);\n      }\n    }\n\n    return instance;\n  }\n\n  /**\n   *\n   * @param callback - a function that will be invoked  after the provider has been initialized by calling provider.initialize().\n   * The function is invoked SYNCHRONOUSLY, so it should not execute any longrunning tasks in order to not block the program.\n   *\n   * @param identifier An optional instance identifier\n   * @returns a function to unregister the callback\n   */\n  onInit(callback: OnInitCallBack<T>, identifier?: string): () => void {\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(identifier);\n    const existingCallbacks =\n      this.onInitCallbacks.get(normalizedIdentifier) ??\n      new Set<OnInitCallBack<T>>();\n    existingCallbacks.add(callback);\n    this.onInitCallbacks.set(normalizedIdentifier, existingCallbacks);\n\n    const existingInstance = this.instances.get(normalizedIdentifier);\n    if (existingInstance) {\n      callback(existingInstance, normalizedIdentifier);\n    }\n\n    return () => {\n      existingCallbacks.delete(callback);\n    };\n  }\n\n  /**\n   * Invoke onInit callbacks synchronously\n   * @param instance the service instance`\n   */\n  private invokeOnInitCallbacks(\n    instance: NameServiceMapping[T],\n    identifier: string\n  ): void {\n    const callbacks = this.onInitCallbacks.get(identifier);\n    if (!callbacks) {\n      return;\n    }\n    for (const callback of callbacks) {\n      try {\n        callback(instance, identifier);\n      } catch {\n        // ignore errors in the onInit callback\n      }\n    }\n  }\n\n  private getOrInitializeService({\n    instanceIdentifier,\n    options = {}\n  }: {\n    instanceIdentifier: string;\n    options?: Record<string, unknown>;\n  }): NameServiceMapping[T] | null {\n    let instance = this.instances.get(instanceIdentifier);\n    if (!instance && this.component) {\n      instance = this.component.instanceFactory(this.container, {\n        instanceIdentifier: normalizeIdentifierForFactory(instanceIdentifier),\n        options\n      });\n      this.instances.set(instanceIdentifier, instance);\n      this.instancesOptions.set(instanceIdentifier, options);\n\n      /**\n       * Invoke onInit listeners.\n       * Note this.component.onInstanceCreated is different, which is used by the component creator,\n       * while onInit listeners are registered by consumers of the provider.\n       */\n      this.invokeOnInitCallbacks(instance, instanceIdentifier);\n\n      /**\n       * Order is important\n       * onInstanceCreated() should be called after this.instances.set(instanceIdentifier, instance); which\n       * makes `isInitialized()` return true.\n       */\n      if (this.component.onInstanceCreated) {\n        try {\n          this.component.onInstanceCreated(\n            this.container,\n            instanceIdentifier,\n            instance\n          );\n        } catch {\n          // ignore errors in the onInstanceCreatedCallback\n        }\n      }\n    }\n\n    return instance || null;\n  }\n\n  private normalizeInstanceIdentifier(\n    identifier: string = DEFAULT_ENTRY_NAME\n  ): string {\n    if (this.component) {\n      return this.component.multipleInstances ? identifier : DEFAULT_ENTRY_NAME;\n    } else {\n      return identifier; // assume multiple instances are supported before the component is provided.\n    }\n  }\n\n  private shouldAutoInitialize(): boolean {\n    return (\n      !!this.component &&\n      this.component.instantiationMode !== InstantiationMode.EXPLICIT\n    );\n  }\n}\n\n// undefined should be passed to the service factory for the default instance\nfunction normalizeIdentifierForFactory(identifier: string): string | undefined {\n  return identifier === DEFAULT_ENTRY_NAME ? undefined : identifier;\n}\n\nfunction isComponentEager<T extends Name>(component: Component<T>): boolean {\n  return component.instantiationMode === InstantiationMode.EAGER;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Provider } from './provider';\nimport { Component } from './component';\nimport { Name } from './types';\n\n/**\n * ComponentContainer that provides Providers for service name T, e.g. `auth`, `auth-internal`\n */\nexport class ComponentContainer {\n  private readonly providers = new Map<string, Provider<Name>>();\n\n  constructor(private readonly name: string) {}\n\n  /**\n   *\n   * @param component Component being added\n   * @param overwrite When a component with the same name has already been registered,\n   * if overwrite is true: overwrite the existing component with the new component and create a new\n   * provider with the new component. It can be useful in tests where you want to use different mocks\n   * for different tests.\n   * if overwrite is false: throw an exception\n   */\n  addComponent<T extends Name>(component: Component<T>): void {\n    const provider = this.getProvider(component.name);\n    if (provider.isComponentSet()) {\n      throw new Error(\n        `Component ${component.name} has already been registered with ${this.name}`\n      );\n    }\n\n    provider.setComponent(component);\n  }\n\n  addOrOverwriteComponent<T extends Name>(component: Component<T>): void {\n    const provider = this.getProvider(component.name);\n    if (provider.isComponentSet()) {\n      // delete the existing provider from the container, so we can register the new component\n      this.providers.delete(component.name);\n    }\n\n    this.addComponent(component);\n  }\n\n  /**\n   * getProvider provides a type safe interface where it can only be called with a field name\n   * present in NameServiceMapping interface.\n   *\n   * Firebase SDKs providing services should extend NameServiceMapping interface to register\n   * themselves.\n   */\n  getProvider<T extends Name>(name: T): Provider<T> {\n    if (this.providers.has(name)) {\n      return this.providers.get(name) as unknown as Provider<T>;\n    }\n\n    // create a Provider for a service that hasn't registered with Firebase\n    const provider = new Provider<T>(name, this);\n    this.providers.set(name, provider as unknown as Provider<Name>);\n\n    return provider as Provider<T>;\n  }\n\n  getProviders(): Array<Provider<Name>> {\n    return Array.from(this.providers.values());\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport type LogLevelString =\n  | 'debug'\n  | 'verbose'\n  | 'info'\n  | 'warn'\n  | 'error'\n  | 'silent';\n\nexport interface LogOptions {\n  level: LogLevelString;\n}\n\nexport type LogCallback = (callbackParams: LogCallbackParams) => void;\n\nexport interface LogCallbackParams {\n  level: LogLevelString;\n  message: string;\n  args: unknown[];\n  type: string;\n}\n\n/**\n * A container for all of the Logger instances\n */\nexport const instances: Logger[] = [];\n\n/**\n * The JS SDK supports 5 log levels and also allows a user the ability to\n * silence the logs altogether.\n *\n * The order is a follows:\n * DEBUG < VERBOSE < INFO < WARN < ERROR\n *\n * All of the log types above the current log level will be captured (i.e. if\n * you set the log level to `INFO`, errors will still be logged, but `DEBUG` and\n * `VERBOSE` logs will not)\n */\nexport enum LogLevel {\n  DEBUG,\n  VERBOSE,\n  INFO,\n  WARN,\n  ERROR,\n  SILENT\n}\n\nconst levelStringToEnum: { [key in LogLevelString]: LogLevel } = {\n  'debug': LogLevel.DEBUG,\n  'verbose': LogLevel.VERBOSE,\n  'info': LogLevel.INFO,\n  'warn': LogLevel.WARN,\n  'error': LogLevel.ERROR,\n  'silent': LogLevel.SILENT\n};\n\n/**\n * The default log level\n */\nconst defaultLogLevel: LogLevel = LogLevel.INFO;\n\n/**\n * We allow users the ability to pass their own log handler. We will pass the\n * type of log, the current log level, and any other arguments passed (i.e. the\n * messages that the user wants to log) to this function.\n */\nexport type LogHandler = (\n  loggerInstance: Logger,\n  logType: LogLevel,\n  ...args: unknown[]\n) => void;\n\n/**\n * By default, `console.debug` is not displayed in the developer console (in\n * chrome). To avoid forcing users to have to opt-in to these logs twice\n * (i.e. once for firebase, and once in the console), we are sending `DEBUG`\n * logs to the `console.log` function.\n */\nconst ConsoleMethod = {\n  [LogLevel.DEBUG]: 'log',\n  [LogLevel.VERBOSE]: 'log',\n  [LogLevel.INFO]: 'info',\n  [LogLevel.WARN]: 'warn',\n  [LogLevel.ERROR]: 'error'\n};\n\n/**\n * The default log handler will forward DEBUG, VERBOSE, INFO, WARN, and ERROR\n * messages on to their corresponding console counterparts (if the log method\n * is supported by the current log level)\n */\nconst defaultLogHandler: LogHandler = (instance, logType, ...args): void => {\n  if (logType < instance.logLevel) {\n    return;\n  }\n  const now = new Date().toISOString();\n  const method = ConsoleMethod[logType as keyof typeof ConsoleMethod];\n  if (method) {\n    console[method as 'log' | 'info' | 'warn' | 'error'](\n      `[${now}]  ${instance.name}:`,\n      ...args\n    );\n  } else {\n    throw new Error(\n      `Attempted to log a message with an invalid logType (value: ${logType})`\n    );\n  }\n};\n\nexport class Logger {\n  /**\n   * Gives you an instance of a Logger to capture messages according to\n   * Firebase's logging scheme.\n   *\n   * @param name The name that the logs will be associated with\n   */\n  constructor(public name: string) {\n    /**\n     * Capture the current instance for later use\n     */\n    instances.push(this);\n  }\n\n  /**\n   * The log level of the given Logger instance.\n   */\n  private _logLevel = defaultLogLevel;\n\n  get logLevel(): LogLevel {\n    return this._logLevel;\n  }\n\n  set logLevel(val: LogLevel) {\n    if (!(val in LogLevel)) {\n      throw new TypeError(`Invalid value \"${val}\" assigned to \\`logLevel\\``);\n    }\n    this._logLevel = val;\n  }\n\n  // Workaround for setter/getter having to be the same type.\n  setLogLevel(val: LogLevel | LogLevelString): void {\n    this._logLevel = typeof val === 'string' ? levelStringToEnum[val] : val;\n  }\n\n  /**\n   * The main (internal) log handler for the Logger instance.\n   * Can be set to a new function in internal package code but not by user.\n   */\n  private _logHandler: LogHandler = defaultLogHandler;\n  get logHandler(): LogHandler {\n    return this._logHandler;\n  }\n  set logHandler(val: LogHandler) {\n    if (typeof val !== 'function') {\n      throw new TypeError('Value assigned to `logHandler` must be a function');\n    }\n    this._logHandler = val;\n  }\n\n  /**\n   * The optional, additional, user-defined log handler for the Logger instance.\n   */\n  private _userLogHandler: LogHandler | null = null;\n  get userLogHandler(): LogHandler | null {\n    return this._userLogHandler;\n  }\n  set userLogHandler(val: LogHandler | null) {\n    this._userLogHandler = val;\n  }\n\n  /**\n   * The functions below are all based on the `console` interface\n   */\n\n  debug(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.DEBUG, ...args);\n    this._logHandler(this, LogLevel.DEBUG, ...args);\n  }\n  log(...args: unknown[]): void {\n    this._userLogHandler &&\n      this._userLogHandler(this, LogLevel.VERBOSE, ...args);\n    this._logHandler(this, LogLevel.VERBOSE, ...args);\n  }\n  info(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.INFO, ...args);\n    this._logHandler(this, LogLevel.INFO, ...args);\n  }\n  warn(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.WARN, ...args);\n    this._logHandler(this, LogLevel.WARN, ...args);\n  }\n  error(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.ERROR, ...args);\n    this._logHandler(this, LogLevel.ERROR, ...args);\n  }\n}\n\nexport function setLogLevel(level: LogLevelString | LogLevel): void {\n  instances.forEach(inst => {\n    inst.setLogLevel(level);\n  });\n}\n\nexport function setUserLogHandler(\n  logCallback: LogCallback | null,\n  options?: LogOptions\n): void {\n  for (const instance of instances) {\n    let customLogLevel: LogLevel | null = null;\n    if (options && options.level) {\n      customLogLevel = levelStringToEnum[options.level];\n    }\n    if (logCallback === null) {\n      instance.userLogHandler = null;\n    } else {\n      instance.userLogHandler = (\n        instance: Logger,\n        level: LogLevel,\n        ...args: unknown[]\n      ) => {\n        const message = args\n          .map(arg => {\n            if (arg == null) {\n              return null;\n            } else if (typeof arg === 'string') {\n              return arg;\n            } else if (typeof arg === 'number' || typeof arg === 'boolean') {\n              return arg.toString();\n            } else if (arg instanceof Error) {\n              return arg.message;\n            } else {\n              try {\n                return JSON.stringify(arg);\n              } catch (ignored) {\n                return null;\n              }\n            }\n          })\n          .filter(arg => arg)\n          .join(' ');\n        if (level >= (customLogLevel ?? instance.logLevel)) {\n          logCallback({\n            level: LogLevel[level].toLowerCase() as LogLevelString,\n            message,\n            args,\n            type: instance.name\n          });\n        }\n      };\n    }\n  }\n}\n", "import { w as wrap, r as replaceTraps } from './wrap-idb-value.js';\nexport { u as unwrap, w as wrap } from './wrap-idb-value.js';\n\n/**\n * Open a database.\n *\n * @param name Name of the database.\n * @param version Schema version.\n * @param callbacks Additional callbacks.\n */\nfunction openDB(name, version, { blocked, upgrade, blocking, terminated } = {}) {\n    const request = indexedDB.open(name, version);\n    const openPromise = wrap(request);\n    if (upgrade) {\n        request.addEventListener('upgradeneeded', (event) => {\n            upgrade(wrap(request.result), event.oldVersion, event.newVersion, wrap(request.transaction), event);\n        });\n    }\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event.newVersion, event));\n    }\n    openPromise\n        .then((db) => {\n        if (terminated)\n            db.addEventListener('close', () => terminated());\n        if (blocking) {\n            db.addEventListener('versionchange', (event) => blocking(event.oldVersion, event.newVersion, event));\n        }\n    })\n        .catch(() => { });\n    return openPromise;\n}\n/**\n * Delete a database.\n *\n * @param name Name of the database.\n */\nfunction deleteDB(name, { blocked } = {}) {\n    const request = indexedDB.deleteDatabase(name);\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event));\n    }\n    return wrap(request).then(() => undefined);\n}\n\nconst readMethods = ['get', 'getKey', 'getAll', 'getAllKeys', 'count'];\nconst writeMethods = ['put', 'add', 'delete', 'clear'];\nconst cachedMethods = new Map();\nfunction getMethod(target, prop) {\n    if (!(target instanceof IDBDatabase &&\n        !(prop in target) &&\n        typeof prop === 'string')) {\n        return;\n    }\n    if (cachedMethods.get(prop))\n        return cachedMethods.get(prop);\n    const targetFuncName = prop.replace(/FromIndex$/, '');\n    const useIndex = prop !== targetFuncName;\n    const isWrite = writeMethods.includes(targetFuncName);\n    if (\n    // Bail if the target doesn't exist on the target. Eg, getAll isn't in Edge.\n    !(targetFuncName in (useIndex ? IDBIndex : IDBObjectStore).prototype) ||\n        !(isWrite || readMethods.includes(targetFuncName))) {\n        return;\n    }\n    const method = async function (storeName, ...args) {\n        // isWrite ? 'readwrite' : undefined gzipps better, but fails in Edge :(\n        const tx = this.transaction(storeName, isWrite ? 'readwrite' : 'readonly');\n        let target = tx.store;\n        if (useIndex)\n            target = target.index(args.shift());\n        // Must reject if op rejects.\n        // If it's a write operation, must reject if tx.done rejects.\n        // Must reject with op rejection first.\n        // Must resolve with op value.\n        // Must handle both promises (no unhandled rejections)\n        return (await Promise.all([\n            target[targetFuncName](...args),\n            isWrite && tx.done,\n        ]))[0];\n    };\n    cachedMethods.set(prop, method);\n    return method;\n}\nreplaceTraps((oldTraps) => ({\n    ...oldTraps,\n    get: (target, prop, receiver) => getMethod(target, prop) || oldTraps.get(target, prop, receiver),\n    has: (target, prop) => !!getMethod(target, prop) || oldTraps.has(target, prop),\n}));\n\nexport { deleteDB, openDB };\n", "const instanceOfAny = (object, constructors) => constructors.some((c) => object instanceof c);\n\nlet idbProxyableTypes;\nlet cursorAdvanceMethods;\n// This is a function to prevent it throwing up in node environments.\nfunction getIdbProxyableTypes() {\n    return (idbProxyableTypes ||\n        (idbProxyableTypes = [\n            IDBDatabase,\n            IDBObjectStore,\n            IDBIndex,\n            IDBCursor,\n            IDBTransaction,\n        ]));\n}\n// This is a function to prevent it throwing up in node environments.\nfunction getCursorAdvanceMethods() {\n    return (cursorAdvanceMethods ||\n        (cursorAdvanceMethods = [\n            IDBCursor.prototype.advance,\n            IDBCursor.prototype.continue,\n            IDBCursor.prototype.continuePrimaryKey,\n        ]));\n}\nconst cursorRequestMap = new WeakMap();\nconst transactionDoneMap = new WeakMap();\nconst transactionStoreNamesMap = new WeakMap();\nconst transformCache = new WeakMap();\nconst reverseTransformCache = new WeakMap();\nfunction promisifyRequest(request) {\n    const promise = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            request.removeEventListener('success', success);\n            request.removeEventListener('error', error);\n        };\n        const success = () => {\n            resolve(wrap(request.result));\n            unlisten();\n        };\n        const error = () => {\n            reject(request.error);\n            unlisten();\n        };\n        request.addEventListener('success', success);\n        request.addEventListener('error', error);\n    });\n    promise\n        .then((value) => {\n        // Since cursoring reuses the IDBRequest (*sigh*), we cache it for later retrieval\n        // (see wrapFunction).\n        if (value instanceof IDBCursor) {\n            cursorRequestMap.set(value, request);\n        }\n        // Catching to avoid \"Uncaught Promise exceptions\"\n    })\n        .catch(() => { });\n    // This mapping exists in reverseTransformCache but doesn't doesn't exist in transformCache. This\n    // is because we create many promises from a single IDBRequest.\n    reverseTransformCache.set(promise, request);\n    return promise;\n}\nfunction cacheDonePromiseForTransaction(tx) {\n    // Early bail if we've already created a done promise for this transaction.\n    if (transactionDoneMap.has(tx))\n        return;\n    const done = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            tx.removeEventListener('complete', complete);\n            tx.removeEventListener('error', error);\n            tx.removeEventListener('abort', error);\n        };\n        const complete = () => {\n            resolve();\n            unlisten();\n        };\n        const error = () => {\n            reject(tx.error || new DOMException('AbortError', 'AbortError'));\n            unlisten();\n        };\n        tx.addEventListener('complete', complete);\n        tx.addEventListener('error', error);\n        tx.addEventListener('abort', error);\n    });\n    // Cache it for later retrieval.\n    transactionDoneMap.set(tx, done);\n}\nlet idbProxyTraps = {\n    get(target, prop, receiver) {\n        if (target instanceof IDBTransaction) {\n            // Special handling for transaction.done.\n            if (prop === 'done')\n                return transactionDoneMap.get(target);\n            // Polyfill for objectStoreNames because of Edge.\n            if (prop === 'objectStoreNames') {\n                return target.objectStoreNames || transactionStoreNamesMap.get(target);\n            }\n            // Make tx.store return the only store in the transaction, or undefined if there are many.\n            if (prop === 'store') {\n                return receiver.objectStoreNames[1]\n                    ? undefined\n                    : receiver.objectStore(receiver.objectStoreNames[0]);\n            }\n        }\n        // Else transform whatever we get back.\n        return wrap(target[prop]);\n    },\n    set(target, prop, value) {\n        target[prop] = value;\n        return true;\n    },\n    has(target, prop) {\n        if (target instanceof IDBTransaction &&\n            (prop === 'done' || prop === 'store')) {\n            return true;\n        }\n        return prop in target;\n    },\n};\nfunction replaceTraps(callback) {\n    idbProxyTraps = callback(idbProxyTraps);\n}\nfunction wrapFunction(func) {\n    // Due to expected object equality (which is enforced by the caching in `wrap`), we\n    // only create one new func per func.\n    // Edge doesn't support objectStoreNames (booo), so we polyfill it here.\n    if (func === IDBDatabase.prototype.transaction &&\n        !('objectStoreNames' in IDBTransaction.prototype)) {\n        return function (storeNames, ...args) {\n            const tx = func.call(unwrap(this), storeNames, ...args);\n            transactionStoreNamesMap.set(tx, storeNames.sort ? storeNames.sort() : [storeNames]);\n            return wrap(tx);\n        };\n    }\n    // Cursor methods are special, as the behaviour is a little more different to standard IDB. In\n    // IDB, you advance the cursor and wait for a new 'success' on the IDBRequest that gave you the\n    // cursor. It's kinda like a promise that can resolve with many values. That doesn't make sense\n    // with real promises, so each advance methods returns a new promise for the cursor object, or\n    // undefined if the end of the cursor has been reached.\n    if (getCursorAdvanceMethods().includes(func)) {\n        return function (...args) {\n            // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n            // the original object.\n            func.apply(unwrap(this), args);\n            return wrap(cursorRequestMap.get(this));\n        };\n    }\n    return function (...args) {\n        // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n        // the original object.\n        return wrap(func.apply(unwrap(this), args));\n    };\n}\nfunction transformCachableValue(value) {\n    if (typeof value === 'function')\n        return wrapFunction(value);\n    // This doesn't return, it just creates a 'done' promise for the transaction,\n    // which is later returned for transaction.done (see idbObjectHandler).\n    if (value instanceof IDBTransaction)\n        cacheDonePromiseForTransaction(value);\n    if (instanceOfAny(value, getIdbProxyableTypes()))\n        return new Proxy(value, idbProxyTraps);\n    // Return the same value back if we're not going to transform it.\n    return value;\n}\nfunction wrap(value) {\n    // We sometimes generate multiple promises from a single IDBRequest (eg when cursoring), because\n    // IDB is weird and a single IDBRequest can yield many responses, so these can't be cached.\n    if (value instanceof IDBRequest)\n        return promisifyRequest(value);\n    // If we've already transformed this value before, reuse the transformed value.\n    // This is faster, but it also provides object equality.\n    if (transformCache.has(value))\n        return transformCache.get(value);\n    const newValue = transformCachableValue(value);\n    // Not all types are transformed.\n    // These may be primitive types, so they can't be WeakMap keys.\n    if (newValue !== value) {\n        transformCache.set(value, newValue);\n        reverseTransformCache.set(newValue, value);\n    }\n    return newValue;\n}\nconst unwrap = (value) => reverseTransformCache.get(value);\n\nexport { reverseTransformCache as a, instanceOfAny as i, replaceTraps as r, unwrap as u, wrap as w };\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  ComponentContainer,\n  ComponentType,\n  Provider,\n  Name\n} from '@firebase/component';\nimport { PlatformLoggerService, VersionService } from './types';\n\nexport class PlatformLoggerServiceImpl implements PlatformLoggerService {\n  constructor(private readonly container: ComponentContainer) {}\n  // In initial implementation, this will be called by installations on\n  // auth token refresh, and installations will send this string.\n  getPlatformInfoString(): string {\n    const providers = this.container.getProviders();\n    // Loop through providers and get library/version pairs from any that are\n    // version components.\n    return providers\n      .map(provider => {\n        if (isVersionServiceProvider(provider)) {\n          const service = provider.getImmediate() as VersionService;\n          return `${service.library}/${service.version}`;\n        } else {\n          return null;\n        }\n      })\n      .filter(logString => logString)\n      .join(' ');\n  }\n}\n/**\n *\n * @param provider check if this provider provides a VersionService\n *\n * NOTE: Using Provider<'app-version'> is a hack to indicate that the provider\n * provides VersionService. The provider is not necessarily a 'app-version'\n * provider.\n */\nfunction isVersionServiceProvider(provider: Provider<Name>): boolean {\n  const component = provider.getComponent();\n  return component?.type === ComponentType.VERSION;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from '@firebase/logger';\n\nexport const logger = new Logger('@firebase/app');\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { name as appName } from '../package.json';\nimport { name as appCompatName } from '../../app-compat/package.json';\nimport { name as analyticsCompatName } from '../../../packages/analytics-compat/package.json';\nimport { name as analyticsName } from '../../../packages/analytics/package.json';\nimport { name as appCheckCompatName } from '../../../packages/app-check-compat/package.json';\nimport { name as appCheckName } from '../../../packages/app-check/package.json';\nimport { name as authName } from '../../../packages/auth/package.json';\nimport { name as authCompatName } from '../../../packages/auth-compat/package.json';\nimport { name as databaseName } from '../../../packages/database/package.json';\nimport { name as dataconnectName } from '../../../packages/data-connect/package.json';\nimport { name as databaseCompatName } from '../../../packages/database-compat/package.json';\nimport { name as functionsName } from '../../../packages/functions/package.json';\nimport { name as functionsCompatName } from '../../../packages/functions-compat/package.json';\nimport { name as installationsName } from '../../../packages/installations/package.json';\nimport { name as installationsCompatName } from '../../../packages/installations-compat/package.json';\nimport { name as messagingName } from '../../../packages/messaging/package.json';\nimport { name as messagingCompatName } from '../../../packages/messaging-compat/package.json';\nimport { name as performanceName } from '../../../packages/performance/package.json';\nimport { name as performanceCompatName } from '../../../packages/performance-compat/package.json';\nimport { name as remoteConfigName } from '../../../packages/remote-config/package.json';\nimport { name as remoteConfigCompatName } from '../../../packages/remote-config-compat/package.json';\nimport { name as storageName } from '../../../packages/storage/package.json';\nimport { name as storageCompatName } from '../../../packages/storage-compat/package.json';\nimport { name as firestoreName } from '../../../packages/firestore/package.json';\nimport { name as vertexName } from '../../../packages/vertexai/package.json';\nimport { name as firestoreCompatName } from '../../../packages/firestore-compat/package.json';\nimport { name as packageName } from '../../../packages/firebase/package.json';\n\n/**\n * The default app name\n *\n * @internal\n */\nexport const DEFAULT_ENTRY_NAME = '[DEFAULT]';\n\nexport const PLATFORM_LOG_STRING = {\n  [appName]: 'fire-core',\n  [appCompatName]: 'fire-core-compat',\n  [analyticsName]: 'fire-analytics',\n  [analyticsCompatName]: 'fire-analytics-compat',\n  [appCheckName]: 'fire-app-check',\n  [appCheckCompatName]: 'fire-app-check-compat',\n  [authName]: 'fire-auth',\n  [authCompatName]: 'fire-auth-compat',\n  [databaseName]: 'fire-rtdb',\n  [dataconnectName]: 'fire-data-connect',\n  [databaseCompatName]: 'fire-rtdb-compat',\n  [functionsName]: 'fire-fn',\n  [functionsCompatName]: 'fire-fn-compat',\n  [installationsName]: 'fire-iid',\n  [installationsCompatName]: 'fire-iid-compat',\n  [messagingName]: 'fire-fcm',\n  [messagingCompatName]: 'fire-fcm-compat',\n  [performanceName]: 'fire-perf',\n  [performanceCompatName]: 'fire-perf-compat',\n  [remoteConfigName]: 'fire-rc',\n  [remoteConfigCompatName]: 'fire-rc-compat',\n  [storageName]: 'fire-gcs',\n  [storageCompatName]: 'fire-gcs-compat',\n  [firestoreName]: 'fire-fst',\n  [firestoreCompatName]: 'fire-fst-compat',\n  [vertexName]: 'fire-vertex',\n  'fire-js': 'fire-js', // Platform identifier for JS SDK.\n  [packageName]: 'fire-js-all'\n} as const;\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseApp,\n  FirebaseOptions,\n  FirebaseServerApp\n} from './public-types';\nimport { Component, Provider, Name } from '@firebase/component';\nimport { logger } from './logger';\nimport { DEFAULT_ENTRY_NAME } from './constants';\nimport { FirebaseAppImpl } from './firebaseApp';\nimport { FirebaseServerAppImpl } from './firebaseServerApp';\n\n/**\n * @internal\n */\nexport const _apps = new Map<string, FirebaseApp>();\n\n/**\n * @internal\n */\nexport const _serverApps = new Map<string, FirebaseServerApp>();\n\n/**\n * Registered components.\n *\n * @internal\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport const _components = new Map<string, Component<any>>();\n\n/**\n * @param component - the component being added to this app's container\n *\n * @internal\n */\nexport function _addComponent<T extends Name>(\n  app: FirebaseApp,\n  component: Component<T>\n): void {\n  try {\n    (app as FirebaseAppImpl).container.addComponent(component);\n  } catch (e) {\n    logger.debug(\n      `Component ${component.name} failed to register with FirebaseApp ${app.name}`,\n      e\n    );\n  }\n}\n\n/**\n *\n * @internal\n */\nexport function _addOrOverwriteComponent(\n  app: FirebaseApp,\n  component: Component\n): void {\n  (app as FirebaseAppImpl).container.addOrOverwriteComponent(component);\n}\n\n/**\n *\n * @param component - the component to register\n * @returns whether or not the component is registered successfully\n *\n * @internal\n */\nexport function _registerComponent<T extends Name>(\n  component: Component<T>\n): boolean {\n  const componentName = component.name;\n  if (_components.has(componentName)) {\n    logger.debug(\n      `There were multiple attempts to register component ${componentName}.`\n    );\n\n    return false;\n  }\n\n  _components.set(componentName, component);\n\n  // add the component to existing app instances\n  for (const app of _apps.values()) {\n    _addComponent(app as FirebaseAppImpl, component);\n  }\n\n  for (const serverApp of _serverApps.values()) {\n    _addComponent(serverApp as FirebaseServerAppImpl, component);\n  }\n\n  return true;\n}\n\n/**\n *\n * @param app - FirebaseApp instance\n * @param name - service name\n *\n * @returns the provider for the service with the matching name\n *\n * @internal\n */\nexport function _getProvider<T extends Name>(\n  app: FirebaseApp,\n  name: T\n): Provider<T> {\n  const heartbeatController = (app as FirebaseAppImpl).container\n    .getProvider('heartbeat')\n    .getImmediate({ optional: true });\n  if (heartbeatController) {\n    void heartbeatController.triggerHeartbeat();\n  }\n  return (app as FirebaseAppImpl).container.getProvider(name);\n}\n\n/**\n *\n * @param app - FirebaseApp instance\n * @param name - service name\n * @param instanceIdentifier - service instance identifier in case the service supports multiple instances\n *\n * @internal\n */\nexport function _removeServiceInstance<T extends Name>(\n  app: FirebaseApp,\n  name: T,\n  instanceIdentifier: string = DEFAULT_ENTRY_NAME\n): void {\n  _getProvider(app, name).clearInstance(instanceIdentifier);\n}\n\n/**\n *\n * @param obj - an object of type FirebaseApp or FirebaseOptions.\n *\n * @returns true if the provide object is of type FirebaseApp.\n *\n * @internal\n */\nexport function _isFirebaseApp(\n  obj: FirebaseApp | FirebaseOptions\n): obj is FirebaseApp {\n  return (obj as FirebaseApp).options !== undefined;\n}\n\n/**\n *\n * @param obj - an object of type FirebaseApp.\n *\n * @returns true if the provided object is of type FirebaseServerAppImpl.\n *\n * @internal\n */\nexport function _isFirebaseServerApp(\n  obj: FirebaseApp | FirebaseServerApp\n): obj is FirebaseServerApp {\n  return (obj as FirebaseServerApp).settings !== undefined;\n}\n\n/**\n * Test only\n *\n * @internal\n */\nexport function _clearComponents(): void {\n  _components.clear();\n}\n\n/**\n * Exported in order to be used in app-compat package\n */\nexport { DEFAULT_ENTRY_NAME as _DEFAULT_ENTRY_NAME };\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, ErrorMap } from '@firebase/util';\n\nexport const enum AppError {\n  NO_APP = 'no-app',\n  BAD_APP_NAME = 'bad-app-name',\n  DUPLICATE_APP = 'duplicate-app',\n  APP_DELETED = 'app-deleted',\n  SERVER_APP_DELETED = 'server-app-deleted',\n  NO_OPTIONS = 'no-options',\n  INVALID_APP_ARGUMENT = 'invalid-app-argument',\n  INVALID_LOG_ARGUMENT = 'invalid-log-argument',\n  IDB_OPEN = 'idb-open',\n  IDB_GET = 'idb-get',\n  IDB_WRITE = 'idb-set',\n  IDB_DELETE = 'idb-delete',\n  FINALIZATION_REGISTRY_NOT_SUPPORTED = 'finalization-registry-not-supported',\n  INVALID_SERVER_APP_ENVIRONMENT = 'invalid-server-app-environment'\n}\n\nconst ERRORS: ErrorMap<AppError> = {\n  [AppError.NO_APP]:\n    \"No Firebase App '{$appName}' has been created - \" +\n    'call initializeApp() first',\n  [AppError.BAD_APP_NAME]: \"Illegal App name: '{$appName}'\",\n  [AppError.DUPLICATE_APP]:\n    \"Firebase App named '{$appName}' already exists with different options or config\",\n  [AppError.APP_DELETED]: \"Firebase App named '{$appName}' already deleted\",\n  [AppError.SERVER_APP_DELETED]: 'Firebase Server App has been deleted',\n  [AppError.NO_OPTIONS]:\n    'Need to provide options, when not being deployed to hosting via source.',\n  [AppError.INVALID_APP_ARGUMENT]:\n    'firebase.{$appName}() takes either no argument or a ' +\n    'Firebase App instance.',\n  [AppError.INVALID_LOG_ARGUMENT]:\n    'First argument to `onLog` must be null or a function.',\n  [AppError.IDB_OPEN]:\n    'Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.',\n  [AppError.IDB_GET]:\n    'Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.',\n  [AppError.IDB_WRITE]:\n    'Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.',\n  [AppError.IDB_DELETE]:\n    'Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}.',\n  [AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED]:\n    'FirebaseServerApp deleteOnDeref field defined but the JS runtime does not support FinalizationRegistry.',\n  [AppError.INVALID_SERVER_APP_ENVIRONMENT]:\n    'FirebaseServerApp is not for use in browser environments.'\n};\n\ninterface ErrorParams {\n  [AppError.NO_APP]: { appName: string };\n  [AppError.BAD_APP_NAME]: { appName: string };\n  [AppError.DUPLICATE_APP]: { appName: string };\n  [AppError.APP_DELETED]: { appName: string };\n  [AppError.INVALID_APP_ARGUMENT]: { appName: string };\n  [AppError.IDB_OPEN]: { originalErrorMessage?: string };\n  [AppError.IDB_GET]: { originalErrorMessage?: string };\n  [AppError.IDB_WRITE]: { originalErrorMessage?: string };\n  [AppError.IDB_DELETE]: { originalErrorMessage?: string };\n  [AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED]: { appName?: string };\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<AppError, ErrorParams>(\n  'app',\n  'Firebase',\n  ERRORS\n);\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseApp,\n  FirebaseOptions,\n  FirebaseAppSettings\n} from './public-types';\nimport {\n  ComponentContainer,\n  Component,\n  ComponentType\n} from '@firebase/component';\nimport { ERROR_FACTORY, AppError } from './errors';\n\nexport class FirebaseAppImpl implements FirebaseApp {\n  protected readonly _options: FirebaseOptions;\n  protected readonly _name: string;\n  /**\n   * Original config values passed in as a constructor parameter.\n   * It is only used to compare with another config object to support idempotent initializeApp().\n   *\n   * Updating automaticDataCollectionEnabled on the App instance will not change its value in _config.\n   */\n  private readonly _config: Required<FirebaseAppSettings>;\n  private _automaticDataCollectionEnabled: boolean;\n  protected _isDeleted = false;\n  private readonly _container: ComponentContainer;\n\n  constructor(\n    options: FirebaseOptions,\n    config: Required<FirebaseAppSettings>,\n    container: ComponentContainer\n  ) {\n    this._options = { ...options };\n    this._config = { ...config };\n    this._name = config.name;\n    this._automaticDataCollectionEnabled =\n      config.automaticDataCollectionEnabled;\n    this._container = container;\n    this.container.addComponent(\n      new Component('app', () => this, ComponentType.PUBLIC)\n    );\n  }\n\n  get automaticDataCollectionEnabled(): boolean {\n    this.checkDestroyed();\n    return this._automaticDataCollectionEnabled;\n  }\n\n  set automaticDataCollectionEnabled(val: boolean) {\n    this.checkDestroyed();\n    this._automaticDataCollectionEnabled = val;\n  }\n\n  get name(): string {\n    this.checkDestroyed();\n    return this._name;\n  }\n\n  get options(): FirebaseOptions {\n    this.checkDestroyed();\n    return this._options;\n  }\n\n  get config(): Required<FirebaseAppSettings> {\n    this.checkDestroyed();\n    return this._config;\n  }\n\n  get container(): ComponentContainer {\n    return this._container;\n  }\n\n  get isDeleted(): boolean {\n    return this._isDeleted;\n  }\n\n  set isDeleted(val: boolean) {\n    this._isDeleted = val;\n  }\n\n  /**\n   * This function will throw an Error if the App has already been deleted -\n   * use before performing API actions on the App.\n   */\n  protected checkDestroyed(): void {\n    if (this.isDeleted) {\n      throw ERROR_FACTORY.create(AppError.APP_DELETED, { appName: this._name });\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseAppSettings,\n  FirebaseServerApp,\n  FirebaseServerAppSettings,\n  FirebaseOptions\n} from './public-types';\nimport { deleteApp, registerVersion } from './api';\nimport { ComponentContainer } from '@firebase/component';\nimport { FirebaseAppImpl } from './firebaseApp';\nimport { ERROR_FACTORY, AppError } from './errors';\nimport { name as packageName, version } from '../package.json';\n\nexport class FirebaseServerAppImpl\n  extends FirebaseAppImpl\n  implements FirebaseServerApp\n{\n  private readonly _serverConfig: FirebaseServerAppSettings;\n  private _finalizationRegistry: FinalizationRegistry<object> | null;\n  private _refCount: number;\n\n  constructor(\n    options: FirebaseOptions | FirebaseAppImpl,\n    serverConfig: FirebaseServerAppSettings,\n    name: string,\n    container: ComponentContainer\n  ) {\n    // Build configuration parameters for the FirebaseAppImpl base class.\n    const automaticDataCollectionEnabled =\n      serverConfig.automaticDataCollectionEnabled !== undefined\n        ? serverConfig.automaticDataCollectionEnabled\n        : false;\n\n    // Create the FirebaseAppSettings object for the FirebaseAppImp constructor.\n    const config: Required<FirebaseAppSettings> = {\n      name,\n      automaticDataCollectionEnabled\n    };\n\n    if ((options as FirebaseOptions).apiKey !== undefined) {\n      // Construct the parent FirebaseAppImp object.\n      super(options as FirebaseOptions, config, container);\n    } else {\n      const appImpl: FirebaseAppImpl = options as FirebaseAppImpl;\n      super(appImpl.options, config, container);\n    }\n\n    // Now construct the data for the FirebaseServerAppImpl.\n    this._serverConfig = {\n      automaticDataCollectionEnabled,\n      ...serverConfig\n    };\n\n    this._finalizationRegistry = null;\n    if (typeof FinalizationRegistry !== 'undefined') {\n      this._finalizationRegistry = new FinalizationRegistry(() => {\n        this.automaticCleanup();\n      });\n    }\n\n    this._refCount = 0;\n    this.incRefCount(this._serverConfig.releaseOnDeref);\n\n    // Do not retain a hard reference to the dref object, otherwise the FinalizationRegistry\n    // will never trigger.\n    this._serverConfig.releaseOnDeref = undefined;\n    serverConfig.releaseOnDeref = undefined;\n\n    registerVersion(packageName, version, 'serverapp');\n  }\n\n  toJSON(): undefined {\n    return undefined;\n  }\n\n  get refCount(): number {\n    return this._refCount;\n  }\n\n  // Increment the reference count of this server app. If an object is provided, register it\n  // with the finalization registry.\n  incRefCount(obj: object | undefined): void {\n    if (this.isDeleted) {\n      return;\n    }\n    this._refCount++;\n    if (obj !== undefined && this._finalizationRegistry !== null) {\n      this._finalizationRegistry.register(obj, this);\n    }\n  }\n\n  // Decrement the reference count.\n  decRefCount(): number {\n    if (this.isDeleted) {\n      return 0;\n    }\n    return --this._refCount;\n  }\n\n  // Invoked by the FinalizationRegistry callback to note that this app should go through its\n  // reference counts and delete itself if no reference count remain. The coordinating logic that\n  // handles this is in deleteApp(...).\n  private automaticCleanup(): void {\n    void deleteApp(this);\n  }\n\n  get settings(): FirebaseServerAppSettings {\n    this.checkDestroyed();\n    return this._serverConfig;\n  }\n\n  /**\n   * This function will throw an Error if the App has already been deleted -\n   * use before performing API actions on the App.\n   */\n  protected checkDestroyed(): void {\n    if (this.isDeleted) {\n      throw ERROR_FACTORY.create(AppError.SERVER_APP_DELETED);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseApp,\n  FirebaseServerApp,\n  FirebaseOptions,\n  FirebaseAppSettings,\n  FirebaseServerAppSettings\n} from './public-types';\nimport { DEFAULT_ENTRY_NAME, PLATFORM_LOG_STRING } from './constants';\nimport { ERROR_FACTORY, AppError } from './errors';\nimport {\n  ComponentContainer,\n  Component,\n  Name,\n  ComponentType\n} from '@firebase/component';\nimport { version } from '../../firebase/package.json';\nimport { FirebaseAppImpl } from './firebaseApp';\nimport { FirebaseServerAppImpl } from './firebaseServerApp';\nimport {\n  _apps,\n  _components,\n  _isFirebaseApp,\n  _registerComponent,\n  _serverApps\n} from './internal';\nimport { logger } from './logger';\nimport {\n  LogLevelString,\n  setLogLevel as setLogLevelImpl,\n  LogCallback,\n  LogOptions,\n  setUserLogHandler\n} from '@firebase/logger';\nimport {\n  deepEqual,\n  getDefaultAppConfig,\n  isBrowser,\n  isWebWorker\n} from '@firebase/util';\n\nexport { FirebaseError } from '@firebase/util';\n\n/**\n * The current SDK version.\n *\n * @public\n */\nexport const SDK_VERSION = version;\n\n/**\n * Creates and initializes a {@link @firebase/app#FirebaseApp} instance.\n *\n * See\n * {@link\n *   https://firebase.google.com/docs/web/setup#add_firebase_to_your_app\n *   | Add Firebase to your app} and\n * {@link\n *   https://firebase.google.com/docs/web/setup#multiple-projects\n *   | Initialize multiple projects} for detailed documentation.\n *\n * @example\n * ```javascript\n *\n * // Initialize default app\n * // Retrieve your own options values by adding a web app on\n * // https://console.firebase.google.com\n * initializeApp({\n *   apiKey: \"AIza....\",                             // Auth / General Use\n *   authDomain: \"YOUR_APP.firebaseapp.com\",         // Auth with popup/redirect\n *   databaseURL: \"https://YOUR_APP.firebaseio.com\", // Realtime Database\n *   storageBucket: \"YOUR_APP.appspot.com\",          // Storage\n *   messagingSenderId: \"123456789\"                  // Cloud Messaging\n * });\n * ```\n *\n * @example\n * ```javascript\n *\n * // Initialize another app\n * const otherApp = initializeApp({\n *   databaseURL: \"https://<OTHER_DATABASE_NAME>.firebaseio.com\",\n *   storageBucket: \"<OTHER_STORAGE_BUCKET>.appspot.com\"\n * }, \"otherApp\");\n * ```\n *\n * @param options - Options to configure the app's services.\n * @param name - Optional name of the app to initialize. If no name\n *   is provided, the default is `\"[DEFAULT]\"`.\n *\n * @returns The initialized app.\n *\n * @public\n */\nexport function initializeApp(\n  options: FirebaseOptions,\n  name?: string\n): FirebaseApp;\n/**\n * Creates and initializes a FirebaseApp instance.\n *\n * @param options - Options to configure the app's services.\n * @param config - FirebaseApp Configuration\n *\n * @public\n */\nexport function initializeApp(\n  options: FirebaseOptions,\n  config?: FirebaseAppSettings\n): FirebaseApp;\n/**\n * Creates and initializes a FirebaseApp instance.\n *\n * @public\n */\nexport function initializeApp(): FirebaseApp;\nexport function initializeApp(\n  _options?: FirebaseOptions,\n  rawConfig = {}\n): FirebaseApp {\n  let options = _options;\n\n  if (typeof rawConfig !== 'object') {\n    const name = rawConfig;\n    rawConfig = { name };\n  }\n\n  const config: Required<FirebaseAppSettings> = {\n    name: DEFAULT_ENTRY_NAME,\n    automaticDataCollectionEnabled: false,\n    ...rawConfig\n  };\n  const name = config.name;\n\n  if (typeof name !== 'string' || !name) {\n    throw ERROR_FACTORY.create(AppError.BAD_APP_NAME, {\n      appName: String(name)\n    });\n  }\n\n  options ||= getDefaultAppConfig();\n\n  if (!options) {\n    throw ERROR_FACTORY.create(AppError.NO_OPTIONS);\n  }\n\n  const existingApp = _apps.get(name) as FirebaseAppImpl;\n  if (existingApp) {\n    // return the existing app if options and config deep equal the ones in the existing app.\n    if (\n      deepEqual(options, existingApp.options) &&\n      deepEqual(config, existingApp.config)\n    ) {\n      return existingApp;\n    } else {\n      throw ERROR_FACTORY.create(AppError.DUPLICATE_APP, { appName: name });\n    }\n  }\n\n  const container = new ComponentContainer(name);\n  for (const component of _components.values()) {\n    container.addComponent(component);\n  }\n\n  const newApp = new FirebaseAppImpl(options, config, container);\n\n  _apps.set(name, newApp);\n\n  return newApp;\n}\n\n/**\n * Creates and initializes a {@link @firebase/app#FirebaseServerApp} instance.\n *\n * The `FirebaseServerApp` is similar to `FirebaseApp`, but is intended for execution in\n * server side rendering environments only. Initialization will fail if invoked from a\n * browser environment.\n *\n * See\n * {@link\n *   https://firebase.google.com/docs/web/setup#add_firebase_to_your_app\n *   | Add Firebase to your app} and\n * {@link\n *   https://firebase.google.com/docs/web/setup#multiple-projects\n *   | Initialize multiple projects} for detailed documentation.\n *\n * @example\n * ```javascript\n *\n * // Initialize an instance of `FirebaseServerApp`.\n * // Retrieve your own options values by adding a web app on\n * // https://console.firebase.google.com\n * initializeServerApp({\n *     apiKey: \"AIza....\",                             // Auth / General Use\n *     authDomain: \"YOUR_APP.firebaseapp.com\",         // Auth with popup/redirect\n *     databaseURL: \"https://YOUR_APP.firebaseio.com\", // Realtime Database\n *     storageBucket: \"YOUR_APP.appspot.com\",          // Storage\n *     messagingSenderId: \"123456789\"                  // Cloud Messaging\n *   },\n *   {\n *    authIdToken: \"Your Auth ID Token\"\n *   });\n * ```\n *\n * @param options - `Firebase.AppOptions` to configure the app's services, or a\n *   a `FirebaseApp` instance which contains the `AppOptions` within.\n * @param config - `FirebaseServerApp` configuration.\n *\n * @returns The initialized `FirebaseServerApp`.\n *\n * @public\n */\nexport function initializeServerApp(\n  options: FirebaseOptions | FirebaseApp,\n  config: FirebaseServerAppSettings\n): FirebaseServerApp;\n\nexport function initializeServerApp(\n  _options: FirebaseOptions | FirebaseApp,\n  _serverAppConfig: FirebaseServerAppSettings\n): FirebaseServerApp {\n  if (isBrowser() && !isWebWorker()) {\n    // FirebaseServerApp isn't designed to be run in browsers.\n    throw ERROR_FACTORY.create(AppError.INVALID_SERVER_APP_ENVIRONMENT);\n  }\n\n  if (_serverAppConfig.automaticDataCollectionEnabled === undefined) {\n    _serverAppConfig.automaticDataCollectionEnabled = false;\n  }\n\n  let appOptions: FirebaseOptions;\n  if (_isFirebaseApp(_options)) {\n    appOptions = _options.options;\n  } else {\n    appOptions = _options;\n  }\n\n  // Build an app name based on a hash of the configuration options.\n  const nameObj = {\n    ..._serverAppConfig,\n    ...appOptions\n  };\n\n  // However, Do not mangle the name based on releaseOnDeref, since it will vary between the\n  // construction of FirebaseServerApp instances. For example, if the object is the request headers.\n  if (nameObj.releaseOnDeref !== undefined) {\n    delete nameObj.releaseOnDeref;\n  }\n\n  const hashCode = (s: string): number => {\n    return [...s].reduce(\n      (hash, c) => (Math.imul(31, hash) + c.charCodeAt(0)) | 0,\n      0\n    );\n  };\n\n  if (_serverAppConfig.releaseOnDeref !== undefined) {\n    if (typeof FinalizationRegistry === 'undefined') {\n      throw ERROR_FACTORY.create(\n        AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED,\n        {}\n      );\n    }\n  }\n\n  const nameString = '' + hashCode(JSON.stringify(nameObj));\n  const existingApp = _serverApps.get(nameString) as FirebaseServerApp;\n  if (existingApp) {\n    (existingApp as FirebaseServerAppImpl).incRefCount(\n      _serverAppConfig.releaseOnDeref\n    );\n    return existingApp;\n  }\n\n  const container = new ComponentContainer(nameString);\n  for (const component of _components.values()) {\n    container.addComponent(component);\n  }\n\n  const newApp = new FirebaseServerAppImpl(\n    appOptions,\n    _serverAppConfig,\n    nameString,\n    container\n  );\n\n  _serverApps.set(nameString, newApp);\n\n  return newApp;\n}\n\n/**\n * Retrieves a {@link @firebase/app#FirebaseApp} instance.\n *\n * When called with no arguments, the default app is returned. When an app name\n * is provided, the app corresponding to that name is returned.\n *\n * An exception is thrown if the app being retrieved has not yet been\n * initialized.\n *\n * @example\n * ```javascript\n * // Return the default app\n * const app = getApp();\n * ```\n *\n * @example\n * ```javascript\n * // Return a named app\n * const otherApp = getApp(\"otherApp\");\n * ```\n *\n * @param name - Optional name of the app to return. If no name is\n *   provided, the default is `\"[DEFAULT]\"`.\n *\n * @returns The app corresponding to the provided app name.\n *   If no app name is provided, the default app is returned.\n *\n * @public\n */\nexport function getApp(name: string = DEFAULT_ENTRY_NAME): FirebaseApp {\n  const app = _apps.get(name);\n  if (!app && name === DEFAULT_ENTRY_NAME && getDefaultAppConfig()) {\n    return initializeApp();\n  }\n  if (!app) {\n    throw ERROR_FACTORY.create(AppError.NO_APP, { appName: name });\n  }\n\n  return app;\n}\n\n/**\n * A (read-only) array of all initialized apps.\n * @public\n */\nexport function getApps(): FirebaseApp[] {\n  return Array.from(_apps.values());\n}\n\n/**\n * Renders this app unusable and frees the resources of all associated\n * services.\n *\n * @example\n * ```javascript\n * deleteApp(app)\n *   .then(function() {\n *     console.log(\"App deleted successfully\");\n *   })\n *   .catch(function(error) {\n *     console.log(\"Error deleting app:\", error);\n *   });\n * ```\n *\n * @public\n */\nexport async function deleteApp(app: FirebaseApp): Promise<void> {\n  let cleanupProviders = false;\n  const name = app.name;\n  if (_apps.has(name)) {\n    cleanupProviders = true;\n    _apps.delete(name);\n  } else if (_serverApps.has(name)) {\n    const firebaseServerApp = app as FirebaseServerAppImpl;\n    if (firebaseServerApp.decRefCount() <= 0) {\n      _serverApps.delete(name);\n      cleanupProviders = true;\n    }\n  }\n\n  if (cleanupProviders) {\n    await Promise.all(\n      (app as FirebaseAppImpl).container\n        .getProviders()\n        .map(provider => provider.delete())\n    );\n    (app as FirebaseAppImpl).isDeleted = true;\n  }\n}\n\n/**\n * Registers a library's name and version for platform logging purposes.\n * @param library - Name of 1p or 3p library (e.g. firestore, angularfire)\n * @param version - Current version of that library.\n * @param variant - Bundle variant, e.g., node, rn, etc.\n *\n * @public\n */\nexport function registerVersion(\n  libraryKeyOrName: string,\n  version: string,\n  variant?: string\n): void {\n  // TODO: We can use this check to whitelist strings when/if we set up\n  // a good whitelist system.\n  let library = PLATFORM_LOG_STRING[libraryKeyOrName] ?? libraryKeyOrName;\n  if (variant) {\n    library += `-${variant}`;\n  }\n  const libraryMismatch = library.match(/\\s|\\//);\n  const versionMismatch = version.match(/\\s|\\//);\n  if (libraryMismatch || versionMismatch) {\n    const warning = [\n      `Unable to register library \"${library}\" with version \"${version}\":`\n    ];\n    if (libraryMismatch) {\n      warning.push(\n        `library name \"${library}\" contains illegal characters (whitespace or \"/\")`\n      );\n    }\n    if (libraryMismatch && versionMismatch) {\n      warning.push('and');\n    }\n    if (versionMismatch) {\n      warning.push(\n        `version name \"${version}\" contains illegal characters (whitespace or \"/\")`\n      );\n    }\n    logger.warn(warning.join(' '));\n    return;\n  }\n  _registerComponent(\n    new Component(\n      `${library}-version` as Name,\n      () => ({ library, version }),\n      ComponentType.VERSION\n    )\n  );\n}\n\n/**\n * Sets log handler for all Firebase SDKs.\n * @param logCallback - An optional custom log handler that executes user code whenever\n * the Firebase SDK makes a logging call.\n *\n * @public\n */\nexport function onLog(\n  logCallback: LogCallback | null,\n  options?: LogOptions\n): void {\n  if (logCallback !== null && typeof logCallback !== 'function') {\n    throw ERROR_FACTORY.create(AppError.INVALID_LOG_ARGUMENT);\n  }\n  setUserLogHandler(logCallback, options);\n}\n\n/**\n * Sets log level for all Firebase SDKs.\n *\n * All of the log types above the current log level are captured (i.e. if\n * you set the log level to `info`, errors are logged, but `debug` and\n * `verbose` logs are not).\n *\n * @public\n */\nexport function setLogLevel(logLevel: LogLevelString): void {\n  setLogLevelImpl(logLevel);\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\nimport { DBSchema, openDB, IDBPDatabase } from 'idb';\nimport { AppError, ERROR_FACTORY } from './errors';\nimport { FirebaseApp } from './public-types';\nimport { HeartbeatsInIndexedDB } from './types';\nimport { logger } from './logger';\n\nconst DB_NAME = 'firebase-heartbeat-database';\nconst DB_VERSION = 1;\nconst STORE_NAME = 'firebase-heartbeat-store';\n\ninterface AppDB extends DBSchema {\n  'firebase-heartbeat-store': {\n    key: string;\n    value: HeartbeatsInIndexedDB;\n  };\n}\n\nlet dbPromise: Promise<IDBPDatabase<AppDB>> | null = null;\nfunction getDbPromise(): Promise<IDBPDatabase<AppDB>> {\n  if (!dbPromise) {\n    dbPromise = openDB<AppDB>(DB_NAME, DB_VERSION, {\n      upgrade: (db, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            try {\n              db.createObjectStore(STORE_NAME);\n            } catch (e) {\n              // Safari/iOS browsers throw occasional exceptions on\n              // db.createObjectStore() that may be a bug. Avoid blocking\n              // the rest of the app functionality.\n              console.warn(e);\n            }\n        }\n      }\n    }).catch(e => {\n      throw ERROR_FACTORY.create(AppError.IDB_OPEN, {\n        originalErrorMessage: e.message\n      });\n    });\n  }\n  return dbPromise;\n}\n\nexport async function readHeartbeatsFromIndexedDB(\n  app: FirebaseApp\n): Promise<HeartbeatsInIndexedDB | undefined> {\n  try {\n    const db = await getDbPromise();\n    const tx = db.transaction(STORE_NAME);\n    const result = await tx.objectStore(STORE_NAME).get(computeKey(app));\n    // We already have the value but tx.done can throw,\n    // so we need to await it here to catch errors\n    await tx.done;\n    return result;\n  } catch (e) {\n    if (e instanceof FirebaseError) {\n      logger.warn(e.message);\n    } else {\n      const idbGetError = ERROR_FACTORY.create(AppError.IDB_GET, {\n        originalErrorMessage: (e as Error)?.message\n      });\n      logger.warn(idbGetError.message);\n    }\n  }\n}\n\nexport async function writeHeartbeatsToIndexedDB(\n  app: FirebaseApp,\n  heartbeatObject: HeartbeatsInIndexedDB\n): Promise<void> {\n  try {\n    const db = await getDbPromise();\n    const tx = db.transaction(STORE_NAME, 'readwrite');\n    const objectStore = tx.objectStore(STORE_NAME);\n    await objectStore.put(heartbeatObject, computeKey(app));\n    await tx.done;\n  } catch (e) {\n    if (e instanceof FirebaseError) {\n      logger.warn(e.message);\n    } else {\n      const idbGetError = ERROR_FACTORY.create(AppError.IDB_WRITE, {\n        originalErrorMessage: (e as Error)?.message\n      });\n      logger.warn(idbGetError.message);\n    }\n  }\n}\n\nfunction computeKey(app: FirebaseApp): string {\n  return `${app.name}!${app.options.appId}`;\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ComponentContainer } from '@firebase/component';\nimport {\n  base64urlEncodeWithoutPadding,\n  isIndexedDBAvailable,\n  validateIndexedDBOpenable\n} from '@firebase/util';\nimport {\n  readHeartbeatsFromIndexedDB,\n  writeHeartbeatsToIndexedDB\n} from './indexeddb';\nimport { FirebaseApp } from './public-types';\nimport {\n  HeartbeatsByUserAgent,\n  HeartbeatService,\n  HeartbeatsInIndexedDB,\n  HeartbeatStorage,\n  SingleDateHeartbeat\n} from './types';\nimport { logger } from './logger';\n\nconst MAX_HEADER_BYTES = 1024;\n// 30 days\nconst STORED_HEARTBEAT_RETENTION_MAX_MILLIS = 30 * 24 * 60 * 60 * 1000;\n\nexport class HeartbeatServiceImpl implements HeartbeatService {\n  /**\n   * The persistence layer for heartbeats\n   * Leave public for easier testing.\n   */\n  _storage: HeartbeatStorageImpl;\n\n  /**\n   * In-memory cache for heartbeats, used by getHeartbeatsHeader() to generate\n   * the header string.\n   * Stores one record per date. This will be consolidated into the standard\n   * format of one record per user agent string before being sent as a header.\n   * Populated from indexedDB when the controller is instantiated and should\n   * be kept in sync with indexedDB.\n   * Leave public for easier testing.\n   */\n  _heartbeatsCache: HeartbeatsInIndexedDB | null = null;\n\n  /**\n   * the initialization promise for populating heartbeatCache.\n   * If getHeartbeatsHeader() is called before the promise resolves\n   * (heartbeatsCache == null), it should wait for this promise\n   * Leave public for easier testing.\n   */\n  _heartbeatsCachePromise: Promise<HeartbeatsInIndexedDB>;\n  constructor(private readonly container: ComponentContainer) {\n    const app = this.container.getProvider('app').getImmediate();\n    this._storage = new HeartbeatStorageImpl(app);\n    this._heartbeatsCachePromise = this._storage.read().then(result => {\n      this._heartbeatsCache = result;\n      return result;\n    });\n  }\n\n  /**\n   * Called to report a heartbeat. The function will generate\n   * a HeartbeatsByUserAgent object, update heartbeatsCache, and persist it\n   * to IndexedDB.\n   * Note that we only store one heartbeat per day. So if a heartbeat for today is\n   * already logged, subsequent calls to this function in the same day will be ignored.\n   */\n  async triggerHeartbeat(): Promise<void> {\n    try {\n      const platformLogger = this.container\n        .getProvider('platform-logger')\n        .getImmediate();\n\n      // This is the \"Firebase user agent\" string from the platform logger\n      // service, not the browser user agent.\n      const agent = platformLogger.getPlatformInfoString();\n      const date = getUTCDateString();\n      if (this._heartbeatsCache?.heartbeats == null) {\n        this._heartbeatsCache = await this._heartbeatsCachePromise;\n        // If we failed to construct a heartbeats cache, then return immediately.\n        if (this._heartbeatsCache?.heartbeats == null) {\n          return;\n        }\n      }\n      // Do not store a heartbeat if one is already stored for this day\n      // or if a header has already been sent today.\n      if (\n        this._heartbeatsCache.lastSentHeartbeatDate === date ||\n        this._heartbeatsCache.heartbeats.some(\n          singleDateHeartbeat => singleDateHeartbeat.date === date\n        )\n      ) {\n        return;\n      } else {\n        // There is no entry for this date. Create one.\n        this._heartbeatsCache.heartbeats.push({ date, agent });\n      }\n      // Remove entries older than 30 days.\n      this._heartbeatsCache.heartbeats =\n        this._heartbeatsCache.heartbeats.filter(singleDateHeartbeat => {\n          const hbTimestamp = new Date(singleDateHeartbeat.date).valueOf();\n          const now = Date.now();\n          return now - hbTimestamp <= STORED_HEARTBEAT_RETENTION_MAX_MILLIS;\n        });\n      return this._storage.overwrite(this._heartbeatsCache);\n    } catch (e) {\n      logger.warn(e);\n    }\n  }\n\n  /**\n   * Returns a base64 encoded string which can be attached to the heartbeat-specific header directly.\n   * It also clears all heartbeats from memory as well as in IndexedDB.\n   *\n   * NOTE: Consuming product SDKs should not send the header if this method\n   * returns an empty string.\n   */\n  async getHeartbeatsHeader(): Promise<string> {\n    try {\n      if (this._heartbeatsCache === null) {\n        await this._heartbeatsCachePromise;\n      }\n      // If it's still null or the array is empty, there is no data to send.\n      if (\n        this._heartbeatsCache?.heartbeats == null ||\n        this._heartbeatsCache.heartbeats.length === 0\n      ) {\n        return '';\n      }\n      const date = getUTCDateString();\n      // Extract as many heartbeats from the cache as will fit under the size limit.\n      const { heartbeatsToSend, unsentEntries } = extractHeartbeatsForHeader(\n        this._heartbeatsCache.heartbeats\n      );\n      const headerString = base64urlEncodeWithoutPadding(\n        JSON.stringify({ version: 2, heartbeats: heartbeatsToSend })\n      );\n      // Store last sent date to prevent another being logged/sent for the same day.\n      this._heartbeatsCache.lastSentHeartbeatDate = date;\n      if (unsentEntries.length > 0) {\n        // Store any unsent entries if they exist.\n        this._heartbeatsCache.heartbeats = unsentEntries;\n        // This seems more likely than emptying the array (below) to lead to some odd state\n        // since the cache isn't empty and this will be called again on the next request,\n        // and is probably safest if we await it.\n        await this._storage.overwrite(this._heartbeatsCache);\n      } else {\n        this._heartbeatsCache.heartbeats = [];\n        // Do not wait for this, to reduce latency.\n        void this._storage.overwrite(this._heartbeatsCache);\n      }\n      return headerString;\n    } catch (e) {\n      logger.warn(e);\n      return '';\n    }\n  }\n}\n\nfunction getUTCDateString(): string {\n  const today = new Date();\n  // Returns date format 'YYYY-MM-DD'\n  return today.toISOString().substring(0, 10);\n}\n\nexport function extractHeartbeatsForHeader(\n  heartbeatsCache: SingleDateHeartbeat[],\n  maxSize = MAX_HEADER_BYTES\n): {\n  heartbeatsToSend: HeartbeatsByUserAgent[];\n  unsentEntries: SingleDateHeartbeat[];\n} {\n  // Heartbeats grouped by user agent in the standard format to be sent in\n  // the header.\n  const heartbeatsToSend: HeartbeatsByUserAgent[] = [];\n  // Single date format heartbeats that are not sent.\n  let unsentEntries = heartbeatsCache.slice();\n  for (const singleDateHeartbeat of heartbeatsCache) {\n    // Look for an existing entry with the same user agent.\n    const heartbeatEntry = heartbeatsToSend.find(\n      hb => hb.agent === singleDateHeartbeat.agent\n    );\n    if (!heartbeatEntry) {\n      // If no entry for this user agent exists, create one.\n      heartbeatsToSend.push({\n        agent: singleDateHeartbeat.agent,\n        dates: [singleDateHeartbeat.date]\n      });\n      if (countBytes(heartbeatsToSend) > maxSize) {\n        // If the header would exceed max size, remove the added heartbeat\n        // entry and stop adding to the header.\n        heartbeatsToSend.pop();\n        break;\n      }\n    } else {\n      heartbeatEntry.dates.push(singleDateHeartbeat.date);\n      // If the header would exceed max size, remove the added date\n      // and stop adding to the header.\n      if (countBytes(heartbeatsToSend) > maxSize) {\n        heartbeatEntry.dates.pop();\n        break;\n      }\n    }\n    // Pop unsent entry from queue. (Skipped if adding the entry exceeded\n    // quota and the loop breaks early.)\n    unsentEntries = unsentEntries.slice(1);\n  }\n  return {\n    heartbeatsToSend,\n    unsentEntries\n  };\n}\n\nexport class HeartbeatStorageImpl implements HeartbeatStorage {\n  private _canUseIndexedDBPromise: Promise<boolean>;\n  constructor(public app: FirebaseApp) {\n    this._canUseIndexedDBPromise = this.runIndexedDBEnvironmentCheck();\n  }\n  async runIndexedDBEnvironmentCheck(): Promise<boolean> {\n    if (!isIndexedDBAvailable()) {\n      return false;\n    } else {\n      return validateIndexedDBOpenable()\n        .then(() => true)\n        .catch(() => false);\n    }\n  }\n  /**\n   * Read all heartbeats.\n   */\n  async read(): Promise<HeartbeatsInIndexedDB> {\n    const canUseIndexedDB = await this._canUseIndexedDBPromise;\n    if (!canUseIndexedDB) {\n      return { heartbeats: [] };\n    } else {\n      const idbHeartbeatObject = await readHeartbeatsFromIndexedDB(this.app);\n      if (idbHeartbeatObject?.heartbeats) {\n        return idbHeartbeatObject;\n      } else {\n        return { heartbeats: [] };\n      }\n    }\n  }\n  // overwrite the storage with the provided heartbeats\n  async overwrite(heartbeatsObject: HeartbeatsInIndexedDB): Promise<void> {\n    const canUseIndexedDB = await this._canUseIndexedDBPromise;\n    if (!canUseIndexedDB) {\n      return;\n    } else {\n      const existingHeartbeatsObject = await this.read();\n      return writeHeartbeatsToIndexedDB(this.app, {\n        lastSentHeartbeatDate:\n          heartbeatsObject.lastSentHeartbeatDate ??\n          existingHeartbeatsObject.lastSentHeartbeatDate,\n        heartbeats: heartbeatsObject.heartbeats\n      });\n    }\n  }\n  // add heartbeats\n  async add(heartbeatsObject: HeartbeatsInIndexedDB): Promise<void> {\n    const canUseIndexedDB = await this._canUseIndexedDBPromise;\n    if (!canUseIndexedDB) {\n      return;\n    } else {\n      const existingHeartbeatsObject = await this.read();\n      return writeHeartbeatsToIndexedDB(this.app, {\n        lastSentHeartbeatDate:\n          heartbeatsObject.lastSentHeartbeatDate ??\n          existingHeartbeatsObject.lastSentHeartbeatDate,\n        heartbeats: [\n          ...existingHeartbeatsObject.heartbeats,\n          ...heartbeatsObject.heartbeats\n        ]\n      });\n    }\n  }\n}\n\n/**\n * Calculate bytes of a HeartbeatsByUserAgent array after being wrapped\n * in a platform logging header JSON object, stringified, and converted\n * to base 64.\n */\nexport function countBytes(heartbeatsCache: HeartbeatsByUserAgent[]): number {\n  // base64 has a restricted set of characters, all of which should be 1 byte.\n  return base64urlEncodeWithoutPadding(\n    // heartbeatsCache wrapper properties\n    JSON.stringify({ version: 2, heartbeats: heartbeatsCache })\n  ).length;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Component, ComponentType } from '@firebase/component';\nimport { PlatformLoggerServiceImpl } from './platformLoggerService';\nimport { name, version } from '../package.json';\nimport { _registerComponent } from './internal';\nimport { registerVersion } from './api';\nimport { HeartbeatServiceImpl } from './heartbeatService';\n\nexport function registerCoreComponents(variant?: string): void {\n  _registerComponent(\n    new Component(\n      'platform-logger',\n      container => new PlatformLoggerServiceImpl(container),\n      ComponentType.PRIVATE\n    )\n  );\n  _registerComponent(\n    new Component(\n      'heartbeat',\n      container => new HeartbeatServiceImpl(container),\n      ComponentType.PRIVATE\n    )\n  );\n\n  // Register `app` package.\n  registerVersion(name, version, variant);\n  // BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n  // Register platform SDK identifier (no version).\n  registerVersion('fire-js', '');\n}\n", "/**\n * Firebase App\n *\n * @remarks This package coordinates the communication between the different Firebase components\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerCoreComponents } from './registerCoreComponents';\n\nexport * from './api';\nexport * from './internal';\nexport * from './public-types';\n\nregisterCoreComponents('__RUNTIME_ENV__');\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseOptions } from './public-types';\nimport {\n  Component,\n  ComponentContainer,\n  ComponentType,\n  InstantiationMode,\n  Name\n} from '@firebase/component';\nimport {\n  deleteApp,\n  _addComponent,\n  _addOrOverwriteComponent,\n  _DEFAULT_ENTRY_NAME,\n  _FirebaseAppInternal as _FirebaseAppExp\n} from '@firebase/app';\nimport { _FirebaseService, _FirebaseNamespace } from './types';\nimport { Compat } from '@firebase/util';\n\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport interface _FirebaseApp {\n  /**\n   * The (read-only) name (identifier) for this App. '[DEFAULT]' is the default\n   * App.\n   */\n  name: string;\n\n  /**\n   * The (read-only) configuration options from the app initialization.\n   */\n  options: FirebaseOptions;\n\n  /**\n   * The settable config flag for GDPR opt-in/opt-out\n   */\n  automaticDataCollectionEnabled: boolean;\n\n  /**\n   * Make the given App unusable and free resources.\n   */\n  delete(): Promise<void>;\n}\n/**\n * Global context object for a collection of services using\n * a shared authentication state.\n *\n * marked as internal because it references internal types exported from @firebase/app\n * @internal\n */\nexport class FirebaseAppImpl implements Compat<_FirebaseAppExp>, _FirebaseApp {\n  private container: ComponentContainer;\n\n  constructor(\n    readonly _delegate: _FirebaseAppExp,\n    private readonly firebase: _FirebaseNamespace\n  ) {\n    // add itself to container\n    _addComponent(\n      _delegate,\n      new Component('app-compat', () => this, ComponentType.PUBLIC)\n    );\n\n    this.container = _delegate.container;\n  }\n\n  get automaticDataCollectionEnabled(): boolean {\n    return this._delegate.automaticDataCollectionEnabled;\n  }\n\n  set automaticDataCollectionEnabled(val) {\n    this._delegate.automaticDataCollectionEnabled = val;\n  }\n\n  get name(): string {\n    return this._delegate.name;\n  }\n\n  get options(): FirebaseOptions {\n    return this._delegate.options;\n  }\n\n  delete(): Promise<void> {\n    return new Promise<void>(resolve => {\n      this._delegate.checkDestroyed();\n      resolve();\n    }).then(() => {\n      this.firebase.INTERNAL.removeApp(this.name);\n      return deleteApp(this._delegate);\n    });\n  }\n\n  /**\n   * Return a service instance associated with this app (creating it\n   * on demand), identified by the passed instanceIdentifier.\n   *\n   * NOTE: Currently storage and functions are the only ones that are leveraging this\n   * functionality. They invoke it by calling:\n   *\n   * ```javascript\n   * firebase.app().storage('STORAGE BUCKET ID')\n   * ```\n   *\n   * The service name is passed to this already\n   * @internal\n   */\n  _getService(\n    name: string,\n    instanceIdentifier: string = _DEFAULT_ENTRY_NAME\n  ): _FirebaseService {\n    this._delegate.checkDestroyed();\n\n    // Initialize instance if InstantiationMode is `EXPLICIT`.\n    const provider = this._delegate.container.getProvider(name as Name);\n    if (\n      !provider.isInitialized() &&\n      provider.getComponent()?.instantiationMode === InstantiationMode.EXPLICIT\n    ) {\n      provider.initialize();\n    }\n\n    // getImmediate will always succeed because _getService is only called for registered components.\n    return provider.getImmediate({\n      identifier: instanceIdentifier\n    }) as unknown as _FirebaseService;\n  }\n\n  /**\n   * Remove a service instance from the cache, so we will create a new instance for this service\n   * when people try to get it again.\n   *\n   * NOTE: currently only firestore uses this functionality to support firestore shutdown.\n   *\n   * @param name The service name\n   * @param instanceIdentifier instance identifier in case multiple instances are allowed\n   * @internal\n   */\n  _removeServiceInstance(\n    name: string,\n    instanceIdentifier: string = _DEFAULT_ENTRY_NAME\n  ): void {\n    this._delegate.container\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      .getProvider(name as any)\n      .clearInstance(instanceIdentifier);\n  }\n\n  /**\n   * @param component the component being added to this app's container\n   * @internal\n   */\n  _addComponent(component: Component): void {\n    _addComponent(this._delegate, component);\n  }\n\n  _addOrOverwriteComponent(component: Component): void {\n    _addOrOverwriteComponent(this._delegate, component);\n  }\n\n  toJSON(): object {\n    return {\n      name: this.name,\n      automaticDataCollectionEnabled: this.automaticDataCollectionEnabled,\n      options: this.options\n    };\n  }\n}\n\n// TODO: investigate why the following needs to be commented out\n// Prevent dead-code elimination of these methods w/o invalid property\n// copying.\n// (FirebaseAppImpl.prototype.name && FirebaseAppImpl.prototype.options) ||\n//   FirebaseAppImpl.prototype.delete ||\n//   console.log('dc');\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, ErrorMap } from '@firebase/util';\n\nexport const enum AppError {\n  NO_APP = 'no-app',\n  INVALID_APP_ARGUMENT = 'invalid-app-argument'\n}\n\nconst ERRORS: ErrorMap<AppError> = {\n  [AppError.NO_APP]:\n    \"No Firebase App '{$appName}' has been created - \" +\n    'call Firebase App.initializeApp()',\n  [AppError.INVALID_APP_ARGUMENT]:\n    'firebase.{$appName}() takes either no argument or a ' +\n    'Firebase App instance.'\n};\n\ntype ErrorParams = { [key in AppError]: { appName: string } };\n\nexport const ERROR_FACTORY = new ErrorFactory<AppError, ErrorParams>(\n  'app-compat',\n  'Firebase',\n  ERRORS\n);\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, FirebaseOptions } from './public-types';\nimport {\n  _FirebaseNamespace,\n  _FirebaseService,\n  FirebaseServiceNamespace\n} from './types';\nimport * as modularAPIs from '@firebase/app';\nimport { _FirebaseAppInternal as _FirebaseAppExp } from '@firebase/app';\nimport { Component, ComponentType, Name } from '@firebase/component';\n\nimport { deepExtend, contains } from '@firebase/util';\nimport { FirebaseAppImpl } from './firebaseApp';\nimport { ERROR_FACTORY, AppError } from './errors';\nimport { FirebaseAppLiteImpl } from './lite/firebaseAppLite';\n\n/**\n * Because auth can't share code with other components, we attach the utility functions\n * in an internal namespace to share code.\n * This function return a firebase namespace object without\n * any utility functions, so it can be shared between the regular firebaseNamespace and\n * the lite version.\n */\nexport function createFirebaseNamespaceCore(\n  firebaseAppImpl: typeof FirebaseAppImpl | typeof FirebaseAppLiteImpl\n): _FirebaseNamespace {\n  const apps: { [name: string]: FirebaseApp } = {};\n  // // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  // const components = new Map<string, Component<any>>();\n\n  // A namespace is a plain JavaScript Object.\n  const namespace: _FirebaseNamespace = {\n    // Hack to prevent Babel from modifying the object returned\n    // as the firebase namespace.\n    // @ts-ignore\n    __esModule: true,\n    initializeApp: initializeAppCompat,\n    // @ts-ignore\n    app,\n    registerVersion: modularAPIs.registerVersion,\n    setLogLevel: modularAPIs.setLogLevel,\n    onLog: modularAPIs.onLog,\n    // @ts-ignore\n    apps: null,\n    SDK_VERSION: modularAPIs.SDK_VERSION,\n    INTERNAL: {\n      registerComponent: registerComponentCompat,\n      removeApp,\n      useAsService,\n      modularAPIs\n    }\n  };\n\n  // Inject a circular default export to allow Babel users who were previously\n  // using:\n  //\n  //   import firebase from 'firebase';\n  //   which becomes: var firebase = require('firebase').default;\n  //\n  // instead of\n  //\n  //   import * as firebase from 'firebase';\n  //   which becomes: var firebase = require('firebase');\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  (namespace as any)['default'] = namespace;\n\n  // firebase.apps is a read-only getter.\n  Object.defineProperty(namespace, 'apps', {\n    get: getApps\n  });\n\n  /**\n   * Called by App.delete() - but before any services associated with the App\n   * are deleted.\n   */\n  function removeApp(name: string): void {\n    delete apps[name];\n  }\n\n  /**\n   * Get the App object for a given name (or DEFAULT).\n   */\n  function app(name?: string): FirebaseApp {\n    name = name || modularAPIs._DEFAULT_ENTRY_NAME;\n    if (!contains(apps, name)) {\n      throw ERROR_FACTORY.create(AppError.NO_APP, { appName: name });\n    }\n    return apps[name];\n  }\n\n  // @ts-ignore\n  app['App'] = firebaseAppImpl;\n\n  /**\n   * Create a new App instance (name must be unique).\n   *\n   * This function is idempotent. It can be called more than once and return the same instance using the same options and config.\n   */\n  function initializeAppCompat(\n    options: FirebaseOptions,\n    rawConfig = {}\n  ): FirebaseApp {\n    const app = modularAPIs.initializeApp(\n      options,\n      rawConfig\n    ) as _FirebaseAppExp;\n\n    if (contains(apps, app.name)) {\n      return apps[app.name];\n    }\n\n    const appCompat = new firebaseAppImpl(app, namespace);\n    apps[app.name] = appCompat;\n    return appCompat;\n  }\n\n  /*\n   * Return an array of all the non-deleted FirebaseApps.\n   */\n  function getApps(): FirebaseApp[] {\n    // Make a copy so caller cannot mutate the apps list.\n    return Object.keys(apps).map(name => apps[name]);\n  }\n\n  function registerComponentCompat<T extends Name>(\n    component: Component<T>\n  ): FirebaseServiceNamespace<_FirebaseService> | null {\n    const componentName = component.name;\n    const componentNameWithoutCompat = componentName.replace('-compat', '');\n    if (\n      modularAPIs._registerComponent(component) &&\n      component.type === ComponentType.PUBLIC\n    ) {\n      // create service namespace for public components\n      // The Service namespace is an accessor function ...\n      const serviceNamespace = (\n        appArg: FirebaseApp = app()\n      ): _FirebaseService => {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        if (typeof (appArg as any)[componentNameWithoutCompat] !== 'function') {\n          // Invalid argument.\n          // This happens in the following case: firebase.storage('gs:/')\n          throw ERROR_FACTORY.create(AppError.INVALID_APP_ARGUMENT, {\n            appName: componentName\n          });\n        }\n\n        // Forward service instance lookup to the FirebaseApp.\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        return (appArg as any)[componentNameWithoutCompat]();\n      };\n\n      // ... and a container for service-level properties.\n      if (component.serviceProps !== undefined) {\n        deepExtend(serviceNamespace, component.serviceProps);\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      (namespace as any)[componentNameWithoutCompat] = serviceNamespace;\n\n      // Patch the FirebaseAppImpl prototype\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      (firebaseAppImpl.prototype as any)[componentNameWithoutCompat] =\n        // TODO: The eslint disable can be removed and the 'ignoreRestArgs'\n        // option added to the no-explicit-any rule when ESlint releases it.\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        function (...args: any) {\n          const serviceFxn = this._getService.bind(this, componentName);\n          return serviceFxn.apply(\n            this,\n            component.multipleInstances ? args : []\n          );\n        };\n    }\n\n    return component.type === ComponentType.PUBLIC\n      ? // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        (namespace as any)[componentNameWithoutCompat]\n      : null;\n  }\n\n  // Map the requested service to a registered service name\n  // (used to map auth to serverAuth service when needed).\n  function useAsService(app: FirebaseApp, name: string): string | null {\n    if (name === 'serverAuth') {\n      return null;\n    }\n\n    const useService = name;\n\n    return useService;\n  }\n\n  return namespace;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseNamespace } from './public-types';\nimport { createSubscribe, deepExtend, ErrorFactory } from '@firebase/util';\nimport { FirebaseAppImpl } from './firebaseApp';\nimport { createFirebaseNamespaceCore } from './firebaseNamespaceCore';\n\n/**\n * Return a firebase namespace object.\n *\n * In production, this will be called exactly once and the result\n * assigned to the 'firebase' global.  It may be called multiple times\n * in unit tests.\n */\nexport function createFirebaseNamespace(): FirebaseNamespace {\n  const namespace = createFirebaseNamespaceCore(FirebaseAppImpl);\n  namespace.INTERNAL = {\n    ...namespace.INTERNAL,\n    createFirebaseNamespace,\n    extendNamespace,\n    createSubscribe,\n    ErrorFactory,\n    deepExtend\n  };\n\n  /**\n   * Patch the top-level firebase namespace with additional properties.\n   *\n   * firebase.INTERNAL.extendNamespace()\n   */\n  function extendNamespace(props: { [prop: string]: unknown }): void {\n    deepExtend(namespace, props);\n  }\n\n  return namespace;\n}\n\nexport const firebase = createFirebaseNamespace();\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from '@firebase/logger';\n\nexport const logger = new Logger('@firebase/app-compat');\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseNamespace } from './public-types';\nimport { getGlobal } from '@firebase/util';\nimport { firebase as firebaseNamespace } from './firebaseNamespace';\nimport { logger } from './logger';\nimport { registerCoreComponents } from './registerCoreComponents';\n\ndeclare global {\n  interface Window {\n    firebase: FirebaseNamespace;\n  }\n}\n\ntry {\n  const globals = getGlobal();\n  // Firebase Lite detection\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  if ((globals as any).firebase !== undefined) {\n    logger.warn(`\n      Warning: Firebase is already defined in the global scope. Please make sure\n      Firebase library is only loaded once.\n    `);\n\n    // eslint-disable-next-line\n    const sdkVersion = ((globals as any).firebase as FirebaseNamespace)\n      .SDK_VERSION;\n    if (sdkVersion && sdkVersion.indexOf('LITE') >= 0) {\n      logger.warn(`\n        Warning: You are trying to load Firebase while using Firebase Performance standalone script.\n        You should load Firebase Performance with this instance of Firebase to avoid loading duplicate code.\n        `);\n    }\n  }\n} catch {\n  // ignore errors thrown by getGlobal\n}\n\nconst firebase = firebaseNamespace;\n\nregisterCoreComponents();\n\n// eslint-disable-next-line import/no-default-export\nexport default firebase;\n\nexport { _FirebaseNamespace, _FirebaseService } from './types';\nexport { FirebaseApp, FirebaseNamespace } from './public-types';\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerVersion } from '@firebase/app';\n\nimport { name, version } from '../package.json';\n\nexport function registerCoreComponents(variant?: string): void {\n  // Register `app` package.\n  registerVersion(name, version, variant);\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport firebase from '@firebase/app-compat';\nimport { name, version } from '../../package.json';\n\nfirebase.registerVersion(name, version, 'app-compat');\n\nexport default firebase;\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { version } from '../../package.json';\n\nexport const PENDING_TIMEOUT_MS = 10000;\n\nexport const PACKAGE_VERSION = `w:${version}`;\nexport const INTERNAL_AUTH_VERSION = 'FIS_v2';\n\nexport const INSTALLATIONS_API_URL =\n  'https://firebaseinstallations.googleapis.com/v1';\n\nexport const TOKEN_EXPIRATION_BUFFER = 60 * 60 * 1000; // One hour\n\nexport const SERVICE = 'installations';\nexport const SERVICE_NAME = 'Installations';\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, FirebaseError } from '@firebase/util';\nimport { SERVICE, SERVICE_NAME } from './constants';\n\nexport const enum ErrorCode {\n  MISSING_APP_CONFIG_VALUES = 'missing-app-config-values',\n  NOT_REGISTERED = 'not-registered',\n  INSTALLATION_NOT_FOUND = 'installation-not-found',\n  REQUEST_FAILED = 'request-failed',\n  APP_OFFLINE = 'app-offline',\n  DELETE_PENDING_REGISTRATION = 'delete-pending-registration'\n}\n\nconst ERROR_DESCRIPTION_MAP: { readonly [key in ErrorCode]: string } = {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]:\n    'Missing App configuration value: \"{$valueName}\"',\n  [ErrorCode.NOT_REGISTERED]: 'Firebase Installation is not registered.',\n  [ErrorCode.INSTALLATION_NOT_FOUND]: 'Firebase Installation not found.',\n  [ErrorCode.REQUEST_FAILED]:\n    '{$requestName} request failed with error \"{$serverCode} {$serverStatus}: {$serverMessage}\"',\n  [ErrorCode.APP_OFFLINE]: 'Could not process request. Application offline.',\n  [ErrorCode.DELETE_PENDING_REGISTRATION]:\n    \"Can't delete installation while there is a pending registration request.\"\n};\n\ninterface ErrorParams {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]: {\n    valueName: string;\n  };\n  [ErrorCode.REQUEST_FAILED]: {\n    requestName: string;\n    [index: string]: string | number; // to make TypeScript 3.8 happy\n  } & ServerErrorData;\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<ErrorCode, ErrorParams>(\n  SERVICE,\n  SERVICE_NAME,\n  ERROR_DESCRIPTION_MAP\n);\n\nexport interface ServerErrorData {\n  serverCode: number;\n  serverMessage: string;\n  serverStatus: string;\n}\n\nexport type ServerError = FirebaseError & { customData: ServerErrorData };\n\n/** Returns true if error is a FirebaseError that is based on an error from the server. */\nexport function isServerError(error: unknown): error is ServerError {\n  return (\n    error instanceof FirebaseError &&\n    error.code.includes(ErrorCode.REQUEST_FAILED)\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\nimport { GenerateAuthTokenResponse } from '../interfaces/api-response';\nimport {\n  CompletedAuthToken,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport {\n  INSTALLATIONS_API_URL,\n  INTERNAL_AUTH_VERSION\n} from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\nimport { AppConfig } from '../interfaces/installation-impl';\n\nexport function getInstallationsEndpoint({ projectId }: AppConfig): string {\n  return `${INSTALLATIONS_API_URL}/projects/${projectId}/installations`;\n}\n\nexport function extractAuthTokenInfoFromResponse(\n  response: GenerateAuthTokenResponse\n): CompletedAuthToken {\n  return {\n    token: response.token,\n    requestStatus: RequestStatus.COMPLETED,\n    expiresIn: getExpiresInFromResponseExpiresIn(response.expiresIn),\n    creationTime: Date.now()\n  };\n}\n\nexport async function getErrorFromResponse(\n  requestName: string,\n  response: Response\n): Promise<FirebaseError> {\n  const responseJson: ErrorResponse = await response.json();\n  const errorData = responseJson.error;\n  return ERROR_FACTORY.create(ErrorCode.REQUEST_FAILED, {\n    requestName,\n    serverCode: errorData.code,\n    serverMessage: errorData.message,\n    serverStatus: errorData.status\n  });\n}\n\nexport function getHeaders({ apiKey }: AppConfig): Headers {\n  return new Headers({\n    'Content-Type': 'application/json',\n    Accept: 'application/json',\n    'x-goog-api-key': apiKey\n  });\n}\n\nexport function getHeadersWithAuth(\n  appConfig: AppConfig,\n  { refreshToken }: RegisteredInstallationEntry\n): Headers {\n  const headers = getHeaders(appConfig);\n  headers.append('Authorization', getAuthorizationHeader(refreshToken));\n  return headers;\n}\n\nexport interface ErrorResponse {\n  error: {\n    code: number;\n    message: string;\n    status: string;\n  };\n}\n\n/**\n * Calls the passed in fetch wrapper and returns the response.\n * If the returned response has a status of 5xx, re-runs the function once and\n * returns the response.\n */\nexport async function retryIfServerError(\n  fn: () => Promise<Response>\n): Promise<Response> {\n  const result = await fn();\n\n  if (result.status >= 500 && result.status < 600) {\n    // Internal Server Error. Retry request.\n    return fn();\n  }\n\n  return result;\n}\n\nfunction getExpiresInFromResponseExpiresIn(responseExpiresIn: string): number {\n  // This works because the server will never respond with fractions of a second.\n  return Number(responseExpiresIn.replace('s', '000'));\n}\n\nfunction getAuthorizationHeader(refreshToken: string): string {\n  return `${INTERNAL_AUTH_VERSION} ${refreshToken}`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** Returns a promise that resolves after given time passes. */\nexport function sleep(ms: number): Promise<void> {\n  return new Promise<void>(resolve => {\n    setTimeout(resolve, ms);\n  });\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { bufferToBase64UrlSafe } from './buffer-to-base64-url-safe';\n\nexport const VALID_FID_PATTERN = /^[cdef][\\w-]{21}$/;\nexport const INVALID_FID = '';\n\n/**\n * Generates a new FID using random values from Web Crypto API.\n * Returns an empty string if FID generation fails for any reason.\n */\nexport function generateFid(): string {\n  try {\n    // A valid FID has exactly 22 base64 characters, which is 132 bits, or 16.5\n    // bytes. our implementation generates a 17 byte array instead.\n    const fidByteArray = new Uint8Array(17);\n    const crypto =\n      self.crypto || (self as unknown as { msCrypto: Crypto }).msCrypto;\n    crypto.getRandomValues(fidByteArray);\n\n    // Replace the first 4 random bits with the constant FID header of 0b0111.\n    fidByteArray[0] = 0b01110000 + (fidByteArray[0] % 0b00010000);\n\n    const fid = encode(fidByteArray);\n\n    return VALID_FID_PATTERN.test(fid) ? fid : INVALID_FID;\n  } catch {\n    // FID generation errored\n    return INVALID_FID;\n  }\n}\n\n/** Converts a FID Uint8Array to a base64 string representation. */\nfunction encode(fidByteArray: Uint8Array): string {\n  const b64String = bufferToBase64UrlSafe(fidByteArray);\n\n  // Remove the 23rd character that was added because of the extra 4 bits at the\n  // end of our 17 byte array, and the '=' padding.\n  return b64String.substr(0, 22);\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function bufferToBase64UrlSafe(array: Uint8Array): string {\n  const b64 = btoa(String.fromCharCode(...array));\n  return b64.replace(/\\+/g, '-').replace(/\\//g, '_');\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AppConfig } from '../interfaces/installation-impl';\n\n/** Returns a string key that can be used to identify the app. */\nexport function getKey(appConfig: AppConfig): string {\n  return `${appConfig.appName}!${appConfig.appId}`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getKey } from '../util/get-key';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { IdChangeCallbackFn } from '../api';\n\nconst fidChangeCallbacks: Map<string, Set<IdChangeCallbackFn>> = new Map();\n\n/**\n * Calls the onIdChange callbacks with the new FID value, and broadcasts the\n * change to other tabs.\n */\nexport function fidChanged(appConfig: AppConfig, fid: string): void {\n  const key = getKey(appConfig);\n\n  callFidChangeCallbacks(key, fid);\n  broadcastFidChange(key, fid);\n}\n\nexport function addCallback(\n  appConfig: AppConfig,\n  callback: IdChangeCallbackFn\n): void {\n  // Open the broadcast channel if it's not already open,\n  // to be able to listen to change events from other tabs.\n  getBroadcastChannel();\n\n  const key = getKey(appConfig);\n\n  let callbackSet = fidChangeCallbacks.get(key);\n  if (!callbackSet) {\n    callbackSet = new Set();\n    fidChangeCallbacks.set(key, callbackSet);\n  }\n  callbackSet.add(callback);\n}\n\nexport function removeCallback(\n  appConfig: AppConfig,\n  callback: IdChangeCallbackFn\n): void {\n  const key = getKey(appConfig);\n\n  const callbackSet = fidChangeCallbacks.get(key);\n\n  if (!callbackSet) {\n    return;\n  }\n\n  callbackSet.delete(callback);\n  if (callbackSet.size === 0) {\n    fidChangeCallbacks.delete(key);\n  }\n\n  // Close broadcast channel if there are no more callbacks.\n  closeBroadcastChannel();\n}\n\nfunction callFidChangeCallbacks(key: string, fid: string): void {\n  const callbacks = fidChangeCallbacks.get(key);\n  if (!callbacks) {\n    return;\n  }\n\n  for (const callback of callbacks) {\n    callback(fid);\n  }\n}\n\nfunction broadcastFidChange(key: string, fid: string): void {\n  const channel = getBroadcastChannel();\n  if (channel) {\n    channel.postMessage({ key, fid });\n  }\n  closeBroadcastChannel();\n}\n\nlet broadcastChannel: BroadcastChannel | null = null;\n/** Opens and returns a BroadcastChannel if it is supported by the browser. */\nfunction getBroadcastChannel(): BroadcastChannel | null {\n  if (!broadcastChannel && 'BroadcastChannel' in self) {\n    broadcastChannel = new BroadcastChannel('[Firebase] FID Change');\n    broadcastChannel.onmessage = e => {\n      callFidChangeCallbacks(e.data.key, e.data.fid);\n    };\n  }\n  return broadcastChannel;\n}\n\nfunction closeBroadcastChannel(): void {\n  if (fidChangeCallbacks.size === 0 && broadcastChannel) {\n    broadcastChannel.close();\n    broadcastChannel = null;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DBSchema, IDBPDatabase, openDB } from 'idb';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { InstallationEntry } from '../interfaces/installation-entry';\nimport { getKey } from '../util/get-key';\nimport { fidChanged } from './fid-changed';\n\nconst DATABASE_NAME = 'firebase-installations-database';\nconst DATABASE_VERSION = 1;\nconst OBJECT_STORE_NAME = 'firebase-installations-store';\n\ninterface InstallationsDB extends DBSchema {\n  'firebase-installations-store': {\n    key: string;\n    value: InstallationEntry | undefined;\n  };\n}\n\nlet dbPromise: Promise<IDBPDatabase<InstallationsDB>> | null = null;\nfunction getDbPromise(): Promise<IDBPDatabase<InstallationsDB>> {\n  if (!dbPromise) {\n    dbPromise = openDB(DATABASE_NAME, DATABASE_VERSION, {\n      upgrade: (db, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            db.createObjectStore(OBJECT_STORE_NAME);\n        }\n      }\n    });\n  }\n  return dbPromise;\n}\n\n/** Gets record(s) from the objectStore that match the given key. */\nexport async function get(\n  appConfig: AppConfig\n): Promise<InstallationEntry | undefined> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  return db\n    .transaction(OBJECT_STORE_NAME)\n    .objectStore(OBJECT_STORE_NAME)\n    .get(key) as Promise<InstallationEntry>;\n}\n\n/** Assigns or overwrites the record for the given key with the given value. */\nexport async function set<ValueType extends InstallationEntry>(\n  appConfig: AppConfig,\n  value: ValueType\n): Promise<ValueType> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  const objectStore = tx.objectStore(OBJECT_STORE_NAME);\n  const oldValue = (await objectStore.get(key)) as InstallationEntry;\n  await objectStore.put(value, key);\n  await tx.done;\n\n  if (!oldValue || oldValue.fid !== value.fid) {\n    fidChanged(appConfig, value.fid);\n  }\n\n  return value;\n}\n\n/** Removes record(s) from the objectStore that match the given key. */\nexport async function remove(appConfig: AppConfig): Promise<void> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).delete(key);\n  await tx.done;\n}\n\n/**\n * Atomically updates a record with the result of updateFn, which gets\n * called with the current value. If newValue is undefined, the record is\n * deleted instead.\n * @return Updated value\n */\nexport async function update<ValueType extends InstallationEntry | undefined>(\n  appConfig: AppConfig,\n  updateFn: (previousValue: InstallationEntry | undefined) => ValueType\n): Promise<ValueType> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  const store = tx.objectStore(OBJECT_STORE_NAME);\n  const oldValue: InstallationEntry | undefined = (await store.get(\n    key\n  )) as InstallationEntry;\n  const newValue = updateFn(oldValue);\n\n  if (newValue === undefined) {\n    await store.delete(key);\n  } else {\n    await store.put(newValue, key);\n  }\n  await tx.done;\n\n  if (newValue && (!oldValue || oldValue.fid !== newValue.fid)) {\n    fidChanged(appConfig, newValue.fid);\n  }\n\n  return newValue;\n}\n\nexport async function clear(): Promise<void> {\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).clear();\n  await tx.done;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { createInstallationRequest } from '../functions/create-installation-request';\nimport {\n  AppConfig,\n  FirebaseInstallationsImpl\n} from '../interfaces/installation-impl';\nimport {\n  InProgressInstallationEntry,\n  InstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { PENDING_TIMEOUT_MS } from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode, isServerError } from '../util/errors';\nimport { sleep } from '../util/sleep';\nimport { generateFid, INVALID_FID } from './generate-fid';\nimport { remove, set, update } from './idb-manager';\n\nexport interface InstallationEntryWithRegistrationPromise {\n  installationEntry: InstallationEntry;\n  /** Exist iff the installationEntry is not registered. */\n  registrationPromise?: Promise<RegisteredInstallationEntry>;\n}\n\n/**\n * Updates and returns the InstallationEntry from the database.\n * Also triggers a registration request if it is necessary and possible.\n */\nexport async function getInstallationEntry(\n  installations: FirebaseInstallationsImpl\n): Promise<InstallationEntryWithRegistrationPromise> {\n  let registrationPromise: Promise<RegisteredInstallationEntry> | undefined;\n\n  const installationEntry = await update(installations.appConfig, oldEntry => {\n    const installationEntry = updateOrCreateInstallationEntry(oldEntry);\n    const entryWithPromise = triggerRegistrationIfNecessary(\n      installations,\n      installationEntry\n    );\n    registrationPromise = entryWithPromise.registrationPromise;\n    return entryWithPromise.installationEntry;\n  });\n\n  if (installationEntry.fid === INVALID_FID) {\n    // FID generation failed. Waiting for the FID from the server.\n    return { installationEntry: await registrationPromise! };\n  }\n\n  return {\n    installationEntry,\n    registrationPromise\n  };\n}\n\n/**\n * Creates a new Installation Entry if one does not exist.\n * Also clears timed out pending requests.\n */\nfunction updateOrCreateInstallationEntry(\n  oldEntry: InstallationEntry | undefined\n): InstallationEntry {\n  const entry: InstallationEntry = oldEntry || {\n    fid: generateFid(),\n    registrationStatus: RequestStatus.NOT_STARTED\n  };\n\n  return clearTimedOutRequest(entry);\n}\n\n/**\n * If the Firebase Installation is not registered yet, this will trigger the\n * registration and return an InProgressInstallationEntry.\n *\n * If registrationPromise does not exist, the installationEntry is guaranteed\n * to be registered.\n */\nfunction triggerRegistrationIfNecessary(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: InstallationEntry\n): InstallationEntryWithRegistrationPromise {\n  if (installationEntry.registrationStatus === RequestStatus.NOT_STARTED) {\n    if (!navigator.onLine) {\n      // Registration required but app is offline.\n      const registrationPromiseWithError = Promise.reject(\n        ERROR_FACTORY.create(ErrorCode.APP_OFFLINE)\n      );\n      return {\n        installationEntry,\n        registrationPromise: registrationPromiseWithError\n      };\n    }\n\n    // Try registering. Change status to IN_PROGRESS.\n    const inProgressEntry: InProgressInstallationEntry = {\n      fid: installationEntry.fid,\n      registrationStatus: RequestStatus.IN_PROGRESS,\n      registrationTime: Date.now()\n    };\n    const registrationPromise = registerInstallation(\n      installations,\n      inProgressEntry\n    );\n    return { installationEntry: inProgressEntry, registrationPromise };\n  } else if (\n    installationEntry.registrationStatus === RequestStatus.IN_PROGRESS\n  ) {\n    return {\n      installationEntry,\n      registrationPromise: waitUntilFidRegistration(installations)\n    };\n  } else {\n    return { installationEntry };\n  }\n}\n\n/** This will be executed only once for each new Firebase Installation. */\nasync function registerInstallation(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: InProgressInstallationEntry\n): Promise<RegisteredInstallationEntry> {\n  try {\n    const registeredInstallationEntry = await createInstallationRequest(\n      installations,\n      installationEntry\n    );\n    return set(installations.appConfig, registeredInstallationEntry);\n  } catch (e) {\n    if (isServerError(e) && e.customData.serverCode === 409) {\n      // Server returned a \"FID cannot be used\" error.\n      // Generate a new ID next time.\n      await remove(installations.appConfig);\n    } else {\n      // Registration failed. Set FID as not registered.\n      await set(installations.appConfig, {\n        fid: installationEntry.fid,\n        registrationStatus: RequestStatus.NOT_STARTED\n      });\n    }\n    throw e;\n  }\n}\n\n/** Call if FID registration is pending in another request. */\nasync function waitUntilFidRegistration(\n  installations: FirebaseInstallationsImpl\n): Promise<RegisteredInstallationEntry> {\n  // Unfortunately, there is no way of reliably observing when a value in\n  // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\n  // so we need to poll.\n\n  let entry: InstallationEntry = await updateInstallationRequest(\n    installations.appConfig\n  );\n  while (entry.registrationStatus === RequestStatus.IN_PROGRESS) {\n    // createInstallation request still in progress.\n    await sleep(100);\n\n    entry = await updateInstallationRequest(installations.appConfig);\n  }\n\n  if (entry.registrationStatus === RequestStatus.NOT_STARTED) {\n    // The request timed out or failed in a different call. Try again.\n    const { installationEntry, registrationPromise } =\n      await getInstallationEntry(installations);\n\n    if (registrationPromise) {\n      return registrationPromise;\n    } else {\n      // if there is no registrationPromise, entry is registered.\n      return installationEntry as RegisteredInstallationEntry;\n    }\n  }\n\n  return entry;\n}\n\n/**\n * Called only if there is a CreateInstallation request in progress.\n *\n * Updates the InstallationEntry in the DB based on the status of the\n * CreateInstallation request.\n *\n * Returns the updated InstallationEntry.\n */\nfunction updateInstallationRequest(\n  appConfig: AppConfig\n): Promise<InstallationEntry> {\n  return update(appConfig, oldEntry => {\n    if (!oldEntry) {\n      throw ERROR_FACTORY.create(ErrorCode.INSTALLATION_NOT_FOUND);\n    }\n    return clearTimedOutRequest(oldEntry);\n  });\n}\n\nfunction clearTimedOutRequest(entry: InstallationEntry): InstallationEntry {\n  if (hasInstallationRequestTimedOut(entry)) {\n    return {\n      fid: entry.fid,\n      registrationStatus: RequestStatus.NOT_STARTED\n    };\n  }\n\n  return entry;\n}\n\nfunction hasInstallationRequestTimedOut(\n  installationEntry: InstallationEntry\n): boolean {\n  return (\n    installationEntry.registrationStatus === RequestStatus.IN_PROGRESS &&\n    installationEntry.registrationTime + PENDING_TIMEOUT_MS < Date.now()\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CreateInstallationResponse } from '../interfaces/api-response';\nimport {\n  InProgressInstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { INTERNAL_AUTH_VERSION, PACKAGE_VERSION } from '../util/constants';\nimport {\n  extractAuthTokenInfoFromResponse,\n  getErrorFromResponse,\n  getHeaders,\n  getInstallationsEndpoint,\n  retryIfServerError\n} from './common';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\n\nexport async function createInstallationRequest(\n  { appConfig, heartbeatServiceProvider }: FirebaseInstallationsImpl,\n  { fid }: InProgressInstallationEntry\n): Promise<RegisteredInstallationEntry> {\n  const endpoint = getInstallationsEndpoint(appConfig);\n\n  const headers = getHeaders(appConfig);\n\n  // If heartbeat service exists, add the heartbeat string to the header.\n  const heartbeatService = heartbeatServiceProvider.getImmediate({\n    optional: true\n  });\n  if (heartbeatService) {\n    const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\n    if (heartbeatsHeader) {\n      headers.append('x-firebase-client', heartbeatsHeader);\n    }\n  }\n\n  const body = {\n    fid,\n    authVersion: INTERNAL_AUTH_VERSION,\n    appId: appConfig.appId,\n    sdkVersion: PACKAGE_VERSION\n  };\n\n  const request: RequestInit = {\n    method: 'POST',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  const response = await retryIfServerError(() => fetch(endpoint, request));\n  if (response.ok) {\n    const responseValue: CreateInstallationResponse = await response.json();\n    const registeredInstallationEntry: RegisteredInstallationEntry = {\n      fid: responseValue.fid || fid,\n      registrationStatus: RequestStatus.COMPLETED,\n      refreshToken: responseValue.refreshToken,\n      authToken: extractAuthTokenInfoFromResponse(responseValue.authToken)\n    };\n    return registeredInstallationEntry;\n  } else {\n    throw await getErrorFromResponse('Create Installation', response);\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { GenerateAuthTokenResponse } from '../interfaces/api-response';\nimport {\n  CompletedAuthToken,\n  RegisteredInstallationEntry\n} from '../interfaces/installation-entry';\nimport { PACKAGE_VERSION } from '../util/constants';\nimport {\n  extractAuthTokenInfoFromResponse,\n  getErrorFromResponse,\n  getHeadersWithAuth,\n  getInstallationsEndpoint,\n  retryIfServerError\n} from './common';\nimport {\n  FirebaseInstallationsImpl,\n  AppConfig\n} from '../interfaces/installation-impl';\n\nexport async function generateAuthTokenRequest(\n  { appConfig, heartbeatServiceProvider }: FirebaseInstallationsImpl,\n  installationEntry: RegisteredInstallationEntry\n): Promise<CompletedAuthToken> {\n  const endpoint = getGenerateAuthTokenEndpoint(appConfig, installationEntry);\n\n  const headers = getHeadersWithAuth(appConfig, installationEntry);\n\n  // If heartbeat service exists, add the heartbeat string to the header.\n  const heartbeatService = heartbeatServiceProvider.getImmediate({\n    optional: true\n  });\n  if (heartbeatService) {\n    const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\n    if (heartbeatsHeader) {\n      headers.append('x-firebase-client', heartbeatsHeader);\n    }\n  }\n\n  const body = {\n    installation: {\n      sdkVersion: PACKAGE_VERSION,\n      appId: appConfig.appId\n    }\n  };\n\n  const request: RequestInit = {\n    method: 'POST',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  const response = await retryIfServerError(() => fetch(endpoint, request));\n  if (response.ok) {\n    const responseValue: GenerateAuthTokenResponse = await response.json();\n    const completedAuthToken: CompletedAuthToken =\n      extractAuthTokenInfoFromResponse(responseValue);\n    return completedAuthToken;\n  } else {\n    throw await getErrorFromResponse('Generate Auth Token', response);\n  }\n}\n\nfunction getGenerateAuthTokenEndpoint(\n  appConfig: AppConfig,\n  { fid }: RegisteredInstallationEntry\n): string {\n  return `${getInstallationsEndpoint(appConfig)}/${fid}/authTokens:generate`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { generateAuthTokenRequest } from '../functions/generate-auth-token-request';\nimport {\n  AppConfig,\n  FirebaseInstallationsImpl\n} from '../interfaces/installation-impl';\nimport {\n  AuthToken,\n  CompletedAuthToken,\n  InProgressAuthToken,\n  InstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { PENDING_TIMEOUT_MS, TOKEN_EXPIRATION_BUFFER } from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode, isServerError } from '../util/errors';\nimport { sleep } from '../util/sleep';\nimport { remove, set, update } from './idb-manager';\n\n/**\n * Returns a valid authentication token for the installation. Generates a new\n * token if one doesn't exist, is expired or about to expire.\n *\n * Should only be called if the Firebase Installation is registered.\n */\nexport async function refreshAuthToken(\n  installations: FirebaseInstallationsImpl,\n  forceRefresh = false\n): Promise<CompletedAuthToken> {\n  let tokenPromise: Promise<CompletedAuthToken> | undefined;\n  const entry = await update(installations.appConfig, oldEntry => {\n    if (!isEntryRegistered(oldEntry)) {\n      throw ERROR_FACTORY.create(ErrorCode.NOT_REGISTERED);\n    }\n\n    const oldAuthToken = oldEntry.authToken;\n    if (!forceRefresh && isAuthTokenValid(oldAuthToken)) {\n      // There is a valid token in the DB.\n      return oldEntry;\n    } else if (oldAuthToken.requestStatus === RequestStatus.IN_PROGRESS) {\n      // There already is a token request in progress.\n      tokenPromise = waitUntilAuthTokenRequest(installations, forceRefresh);\n      return oldEntry;\n    } else {\n      // No token or token expired.\n      if (!navigator.onLine) {\n        throw ERROR_FACTORY.create(ErrorCode.APP_OFFLINE);\n      }\n\n      const inProgressEntry = makeAuthTokenRequestInProgressEntry(oldEntry);\n      tokenPromise = fetchAuthTokenFromServer(installations, inProgressEntry);\n      return inProgressEntry;\n    }\n  });\n\n  const authToken = tokenPromise\n    ? await tokenPromise\n    : (entry.authToken as CompletedAuthToken);\n  return authToken;\n}\n\n/**\n * Call only if FID is registered and Auth Token request is in progress.\n *\n * Waits until the current pending request finishes. If the request times out,\n * tries once in this thread as well.\n */\nasync function waitUntilAuthTokenRequest(\n  installations: FirebaseInstallationsImpl,\n  forceRefresh: boolean\n): Promise<CompletedAuthToken> {\n  // Unfortunately, there is no way of reliably observing when a value in\n  // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\n  // so we need to poll.\n\n  let entry = await updateAuthTokenRequest(installations.appConfig);\n  while (entry.authToken.requestStatus === RequestStatus.IN_PROGRESS) {\n    // generateAuthToken still in progress.\n    await sleep(100);\n\n    entry = await updateAuthTokenRequest(installations.appConfig);\n  }\n\n  const authToken = entry.authToken;\n  if (authToken.requestStatus === RequestStatus.NOT_STARTED) {\n    // The request timed out or failed in a different call. Try again.\n    return refreshAuthToken(installations, forceRefresh);\n  } else {\n    return authToken;\n  }\n}\n\n/**\n * Called only if there is a GenerateAuthToken request in progress.\n *\n * Updates the InstallationEntry in the DB based on the status of the\n * GenerateAuthToken request.\n *\n * Returns the updated InstallationEntry.\n */\nfunction updateAuthTokenRequest(\n  appConfig: AppConfig\n): Promise<RegisteredInstallationEntry> {\n  return update(appConfig, oldEntry => {\n    if (!isEntryRegistered(oldEntry)) {\n      throw ERROR_FACTORY.create(ErrorCode.NOT_REGISTERED);\n    }\n\n    const oldAuthToken = oldEntry.authToken;\n    if (hasAuthTokenRequestTimedOut(oldAuthToken)) {\n      return {\n        ...oldEntry,\n        authToken: { requestStatus: RequestStatus.NOT_STARTED }\n      };\n    }\n\n    return oldEntry;\n  });\n}\n\nasync function fetchAuthTokenFromServer(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: RegisteredInstallationEntry\n): Promise<CompletedAuthToken> {\n  try {\n    const authToken = await generateAuthTokenRequest(\n      installations,\n      installationEntry\n    );\n    const updatedInstallationEntry: RegisteredInstallationEntry = {\n      ...installationEntry,\n      authToken\n    };\n    await set(installations.appConfig, updatedInstallationEntry);\n    return authToken;\n  } catch (e) {\n    if (\n      isServerError(e) &&\n      (e.customData.serverCode === 401 || e.customData.serverCode === 404)\n    ) {\n      // Server returned a \"FID not found\" or a \"Invalid authentication\" error.\n      // Generate a new ID next time.\n      await remove(installations.appConfig);\n    } else {\n      const updatedInstallationEntry: RegisteredInstallationEntry = {\n        ...installationEntry,\n        authToken: { requestStatus: RequestStatus.NOT_STARTED }\n      };\n      await set(installations.appConfig, updatedInstallationEntry);\n    }\n    throw e;\n  }\n}\n\nfunction isEntryRegistered(\n  installationEntry: InstallationEntry | undefined\n): installationEntry is RegisteredInstallationEntry {\n  return (\n    installationEntry !== undefined &&\n    installationEntry.registrationStatus === RequestStatus.COMPLETED\n  );\n}\n\nfunction isAuthTokenValid(authToken: AuthToken): boolean {\n  return (\n    authToken.requestStatus === RequestStatus.COMPLETED &&\n    !isAuthTokenExpired(authToken)\n  );\n}\n\nfunction isAuthTokenExpired(authToken: CompletedAuthToken): boolean {\n  const now = Date.now();\n  return (\n    now < authToken.creationTime ||\n    authToken.creationTime + authToken.expiresIn < now + TOKEN_EXPIRATION_BUFFER\n  );\n}\n\n/** Returns an updated InstallationEntry with an InProgressAuthToken. */\nfunction makeAuthTokenRequestInProgressEntry(\n  oldEntry: RegisteredInstallationEntry\n): RegisteredInstallationEntry {\n  const inProgressAuthToken: InProgressAuthToken = {\n    requestStatus: RequestStatus.IN_PROGRESS,\n    requestTime: Date.now()\n  };\n  return {\n    ...oldEntry,\n    authToken: inProgressAuthToken\n  };\n}\n\nfunction hasAuthTokenRequestTimedOut(authToken: AuthToken): boolean {\n  return (\n    authToken.requestStatus === RequestStatus.IN_PROGRESS &&\n    authToken.requestTime + PENDING_TIMEOUT_MS < Date.now()\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getInstallationEntry } from '../helpers/get-installation-entry';\nimport { refreshAuthToken } from '../helpers/refresh-auth-token';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * Returns a Firebase Installations auth token, identifying the current\n * Firebase Installation.\n * @param installations - The `Installations` instance.\n * @param forceRefresh - Force refresh regardless of token expiration.\n *\n * @public\n */\nexport async function getToken(\n  installations: Installations,\n  forceRefresh = false\n): Promise<string> {\n  const installationsImpl = installations as FirebaseInstallationsImpl;\n  await completeInstallationRegistration(installationsImpl);\n\n  // At this point we either have a Registered Installation in the DB, or we've\n  // already thrown an error.\n  const authToken = await refreshAuthToken(installationsImpl, forceRefresh);\n  return authToken.token;\n}\n\nasync function completeInstallationRegistration(\n  installations: FirebaseInstallationsImpl\n): Promise<void> {\n  const { registrationPromise } = await getInstallationEntry(installations);\n\n  if (registrationPromise) {\n    // A createInstallation request is in progress. Wait until it finishes.\n    await registrationPromise;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, FirebaseOptions } from '@firebase/app';\nimport { FirebaseError } from '@firebase/util';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nexport function extractAppConfig(app: FirebaseApp): AppConfig {\n  if (!app || !app.options) {\n    throw getMissingValueError('App Configuration');\n  }\n\n  if (!app.name) {\n    throw getMissingValueError('App Name');\n  }\n\n  // Required app config keys\n  const configKeys: Array<keyof FirebaseOptions> = [\n    'projectId',\n    'apiKey',\n    'appId'\n  ];\n\n  for (const keyName of configKeys) {\n    if (!app.options[keyName]) {\n      throw getMissingValueError(keyName);\n    }\n  }\n\n  return {\n    appName: app.name,\n    projectId: app.options.projectId!,\n    apiKey: app.options.apiKey!,\n    appId: app.options.appId!\n  };\n}\n\nfunction getMissingValueError(valueName: string): FirebaseError {\n  return ERROR_FACTORY.create(ErrorCode.MISSING_APP_CONFIG_VALUES, {\n    valueName\n  });\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _registerComponent, _getProvider } from '@firebase/app';\nimport {\n  Component,\n  ComponentType,\n  InstanceFactory,\n  ComponentContainer\n} from '@firebase/component';\nimport { getId, getToken } from '../api/index';\nimport { _FirebaseInstallationsInternal } from '../interfaces/public-types';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { extractAppConfig } from '../helpers/extract-app-config';\n\nconst INSTALLATIONS_NAME = 'installations';\nconst INSTALLATIONS_NAME_INTERNAL = 'installations-internal';\n\nconst publicFactory: InstanceFactory<'installations'> = (\n  container: ComponentContainer\n) => {\n  const app = container.getProvider('app').getImmediate();\n  // Throws if app isn't configured properly.\n  const appConfig = extractAppConfig(app);\n  const heartbeatServiceProvider = _getProvider(app, 'heartbeat');\n\n  const installationsImpl: FirebaseInstallationsImpl = {\n    app,\n    appConfig,\n    heartbeatServiceProvider,\n    _delete: () => Promise.resolve()\n  };\n  return installationsImpl;\n};\n\nconst internalFactory: InstanceFactory<'installations-internal'> = (\n  container: ComponentContainer\n) => {\n  const app = container.getProvider('app').getImmediate();\n  // Internal FIS instance relies on public FIS instance.\n  const installations = _getProvider(app, INSTALLATIONS_NAME).getImmediate();\n\n  const installationsInternal: _FirebaseInstallationsInternal = {\n    getId: () => getId(installations),\n    getToken: (forceRefresh?: boolean) => getToken(installations, forceRefresh)\n  };\n  return installationsInternal;\n};\n\nexport function registerInstallations(): void {\n  _registerComponent(\n    new Component(INSTALLATIONS_NAME, publicFactory, ComponentType.PUBLIC)\n  );\n  _registerComponent(\n    new Component(\n      INSTALLATIONS_NAME_INTERNAL,\n      internalFactory,\n      ComponentType.PRIVATE\n    )\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getInstallationEntry } from '../helpers/get-installation-entry';\nimport { refreshAuthToken } from '../helpers/refresh-auth-token';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * Creates a Firebase Installation if there isn't one for the app and\n * returns the Installation ID.\n * @param installations - The `Installations` instance.\n *\n * @public\n */\nexport async function getId(installations: Installations): Promise<string> {\n  const installationsImpl = installations as FirebaseInstallationsImpl;\n  const { installationEntry, registrationPromise } = await getInstallationEntry(\n    installationsImpl\n  );\n\n  if (registrationPromise) {\n    registrationPromise.catch(console.error);\n  } else {\n    // If the installation is already registered, update the authentication\n    // token if needed.\n    refreshAuthToken(installationsImpl).catch(console.error);\n  }\n\n  return installationEntry.fid;\n}\n", "/**\n * The Firebase Installations Web SDK.\n * This SDK does not work in a Node.js environment.\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerInstallations } from './functions/config';\nimport { registerVersion } from '@firebase/app';\nimport { name, version } from '../package.json';\n\nexport * from './api';\nexport * from './interfaces/public-types';\n\nregisterInstallations();\nregisterVersion(name, version);\n// BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\nregisterVersion(name, version, '__BUILD_TARGET__');\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from '../utils/errors';\nimport { isIndexedDBAvailable, areCookiesEnabled } from '@firebase/util';\nimport { consoleLogger } from '../utils/console_logger';\n\ndeclare global {\n  interface Window {\n    PerformanceObserver: typeof PerformanceObserver;\n    perfMetrics?: { onFirstInputDelay(fn: (fid: number) => void): void };\n  }\n}\n\nlet apiInstance: Api | undefined;\nlet windowInstance: Window | undefined;\n\nexport type EntryType =\n  | 'mark'\n  | 'measure'\n  | 'paint'\n  | 'resource'\n  | 'frame'\n  | 'navigation';\n\n/**\n * This class holds a reference to various browser related objects injected by\n * set methods.\n */\nexport class Api {\n  private readonly performance: Performance;\n  /** PerformanceObserver constructor function. */\n  private readonly PerformanceObserver: typeof PerformanceObserver;\n  private readonly windowLocation: Location;\n  readonly onFirstInputDelay?: (fn: (fid: number) => void) => void;\n  readonly localStorage?: Storage;\n  readonly document: Document;\n  readonly navigator: Navigator;\n\n  constructor(readonly window?: Window) {\n    if (!window) {\n      throw ERROR_FACTORY.create(ErrorCode.NO_WINDOW);\n    }\n    this.performance = window.performance;\n    this.PerformanceObserver = window.PerformanceObserver;\n    this.windowLocation = window.location;\n    this.navigator = window.navigator;\n    this.document = window.document;\n    if (this.navigator && this.navigator.cookieEnabled) {\n      // If user blocks cookies on the browser, accessing localStorage will\n      // throw an exception.\n      this.localStorage = window.localStorage;\n    }\n    if (window.perfMetrics && window.perfMetrics.onFirstInputDelay) {\n      this.onFirstInputDelay = window.perfMetrics.onFirstInputDelay;\n    }\n  }\n\n  getUrl(): string {\n    // Do not capture the string query part of url.\n    return this.windowLocation.href.split('?')[0];\n  }\n\n  mark(name: string): void {\n    if (!this.performance || !this.performance.mark) {\n      return;\n    }\n    this.performance.mark(name);\n  }\n\n  measure(measureName: string, mark1: string, mark2: string): void {\n    if (!this.performance || !this.performance.measure) {\n      return;\n    }\n    this.performance.measure(measureName, mark1, mark2);\n  }\n\n  getEntriesByType(type: EntryType): PerformanceEntry[] {\n    if (!this.performance || !this.performance.getEntriesByType) {\n      return [];\n    }\n    return this.performance.getEntriesByType(type);\n  }\n\n  getEntriesByName(name: string): PerformanceEntry[] {\n    if (!this.performance || !this.performance.getEntriesByName) {\n      return [];\n    }\n    return this.performance.getEntriesByName(name);\n  }\n\n  getTimeOrigin(): number {\n    // Polyfill the time origin with performance.timing.navigationStart.\n    return (\n      this.performance &&\n      (this.performance.timeOrigin || this.performance.timing.navigationStart)\n    );\n  }\n\n  requiredApisAvailable(): boolean {\n    if (!fetch || !Promise || !areCookiesEnabled()) {\n      consoleLogger.info(\n        'Firebase Performance cannot start if browser does not support fetch and Promise or cookie is disabled.'\n      );\n      return false;\n    }\n\n    if (!isIndexedDBAvailable()) {\n      consoleLogger.info('IndexedDB is not supported by current browser');\n      return false;\n    }\n    return true;\n  }\n\n  setupObserver(\n    entryType: EntryType,\n    callback: (entry: PerformanceEntry) => void\n  ): void {\n    if (!this.PerformanceObserver) {\n      return;\n    }\n    const observer = new this.PerformanceObserver(list => {\n      for (const entry of list.getEntries()) {\n        // `entry` is a PerformanceEntry instance.\n        callback(entry);\n      }\n    });\n\n    // Start observing the entry types you care about.\n    observer.observe({ entryTypes: [entryType] });\n  }\n\n  static getInstance(): Api {\n    if (apiInstance === undefined) {\n      apiInstance = new Api(windowInstance);\n    }\n    return apiInstance;\n  }\n}\n\nexport function setupApi(window: Window): void {\n  windowInstance = window;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { version } from '../package.json';\n\nexport const SDK_VERSION = version;\n/** The prefix for start User Timing marks used for creating Traces. */\nexport const TRACE_START_MARK_PREFIX = 'FB-PERF-TRACE-START';\n/** The prefix for stop User Timing marks used for creating Traces. */\nexport const TRACE_STOP_MARK_PREFIX = 'FB-PERF-TRACE-STOP';\n/** The prefix for User Timing measure used for creating Traces. */\nexport const TRACE_MEASURE_PREFIX = 'FB-PERF-TRACE-MEASURE';\n/** The prefix for out of the box page load Trace name. */\nexport const OOB_TRACE_PAGE_LOAD_PREFIX = '_wt_';\n\nexport const FIRST_PAINT_COUNTER_NAME = '_fp';\n\nexport const FIRST_CONTENTFUL_PAINT_COUNTER_NAME = '_fcp';\n\nexport const FIRST_INPUT_DELAY_COUNTER_NAME = '_fid';\n\nexport const CONFIG_LOCAL_STORAGE_KEY = '@firebase/performance/config';\n\nexport const CONFIG_EXPIRY_LOCAL_STORAGE_KEY =\n  '@firebase/performance/configexpire';\n\nexport const SERVICE = 'performance';\nexport const SERVICE_NAME = 'Performance';\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory } from '@firebase/util';\nimport { SERVICE, SERVICE_NAME } from '../constants';\n\nexport const enum ErrorCode {\n  TRACE_STARTED_BEFORE = 'trace started',\n  TRACE_STOPPED_BEFORE = 'trace stopped',\n  NONPOSITIVE_TRACE_START_TIME = 'nonpositive trace startTime',\n  NONPOSITIVE_TRACE_DURATION = 'nonpositive trace duration',\n  NO_WINDOW = 'no window',\n  NO_APP_ID = 'no app id',\n  NO_PROJECT_ID = 'no project id',\n  NO_API_KEY = 'no api key',\n  INVALID_CC_LOG = 'invalid cc log',\n  FB_NOT_DEFAULT = 'FB not default',\n  RC_NOT_OK = 'RC response not ok',\n  INVALID_ATTRIBUTE_NAME = 'invalid attribute name',\n  INVALID_ATTRIBUTE_VALUE = 'invalid attribute value',\n  INVALID_CUSTOM_METRIC_NAME = 'invalid custom metric name',\n  INVALID_STRING_MERGER_PARAMETER = 'invalid String merger input',\n  ALREADY_INITIALIZED = 'already initialized'\n}\n\nconst ERROR_DESCRIPTION_MAP: { readonly [key in ErrorCode]: string } = {\n  [ErrorCode.TRACE_STARTED_BEFORE]: 'Trace {$traceName} was started before.',\n  [ErrorCode.TRACE_STOPPED_BEFORE]: 'Trace {$traceName} is not running.',\n  [ErrorCode.NONPOSITIVE_TRACE_START_TIME]:\n    'Trace {$traceName} startTime should be positive.',\n  [ErrorCode.NONPOSITIVE_TRACE_DURATION]:\n    'Trace {$traceName} duration should be positive.',\n  [ErrorCode.NO_WINDOW]: 'Window is not available.',\n  [ErrorCode.NO_APP_ID]: 'App id is not available.',\n  [ErrorCode.NO_PROJECT_ID]: 'Project id is not available.',\n  [ErrorCode.NO_API_KEY]: 'Api key is not available.',\n  [ErrorCode.INVALID_CC_LOG]: 'Attempted to queue invalid cc event',\n  [ErrorCode.FB_NOT_DEFAULT]:\n    'Performance can only start when Firebase app instance is the default one.',\n  [ErrorCode.RC_NOT_OK]: 'RC response is not ok',\n  [ErrorCode.INVALID_ATTRIBUTE_NAME]:\n    'Attribute name {$attributeName} is invalid.',\n  [ErrorCode.INVALID_ATTRIBUTE_VALUE]:\n    'Attribute value {$attributeValue} is invalid.',\n  [ErrorCode.INVALID_CUSTOM_METRIC_NAME]:\n    'Custom metric name {$customMetricName} is invalid',\n  [ErrorCode.INVALID_STRING_MERGER_PARAMETER]:\n    'Input for String merger is invalid, contact support team to resolve.',\n  [ErrorCode.ALREADY_INITIALIZED]:\n    'initializePerformance() has already been called with ' +\n    'different options. To avoid this error, call initializePerformance() with the ' +\n    'same options as when it was originally called, or call getPerformance() to return the' +\n    ' already initialized instance.'\n};\n\ninterface ErrorParams {\n  [ErrorCode.TRACE_STARTED_BEFORE]: { traceName: string };\n  [ErrorCode.TRACE_STOPPED_BEFORE]: { traceName: string };\n  [ErrorCode.NONPOSITIVE_TRACE_START_TIME]: { traceName: string };\n  [ErrorCode.NONPOSITIVE_TRACE_DURATION]: { traceName: string };\n  [ErrorCode.INVALID_ATTRIBUTE_NAME]: { attributeName: string };\n  [ErrorCode.INVALID_ATTRIBUTE_VALUE]: { attributeValue: string };\n  [ErrorCode.INVALID_CUSTOM_METRIC_NAME]: { customMetricName: string };\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<ErrorCode, ErrorParams>(\n  SERVICE,\n  SERVICE_NAME,\n  ERROR_DESCRIPTION_MAP\n);\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger, LogLevel } from '@firebase/logger';\nimport { SERVICE_NAME } from '../constants';\n\nexport const consoleLogger = new Logger(SERVICE_NAME);\nconsoleLogger.logLevel = LogLevel.INFO;\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _FirebaseInstallationsInternal } from '@firebase/installations';\n\nlet iid: string | undefined;\nlet authToken: string | undefined;\n\nexport function getIidPromise(\n  installationsService: _FirebaseInstallationsInternal\n): Promise<string> {\n  const iidPromise = installationsService.getId();\n  // eslint-disable-next-line @typescript-eslint/no-floating-promises\n  iidPromise.then((iidVal: string) => {\n    iid = iidVal;\n  });\n  return iidPromise;\n}\n\n// This method should be used after the iid is retrieved by getIidPromise method.\nexport function getIid(): string | undefined {\n  return iid;\n}\n\nexport function getAuthTokenPromise(\n  installationsService: _FirebaseInstallationsInternal\n): Promise<string> {\n  const authTokenPromise = installationsService.getToken();\n  // eslint-disable-next-line @typescript-eslint/no-floating-promises\n  authTokenPromise.then((authTokenVal: string) => {\n    authToken = authTokenVal;\n  });\n  return authTokenPromise;\n}\n\nexport function getAuthenticationToken(): string | undefined {\n  return authToken;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { mergeStrings } from '../utils/string_merger';\n\nlet settingsServiceInstance: SettingsService | undefined;\n\nexport class SettingsService {\n  // The variable which controls logging of automatic traces and HTTP/S network monitoring.\n  instrumentationEnabled = true;\n\n  // The variable which controls logging of custom traces.\n  dataCollectionEnabled = true;\n\n  // Configuration flags set through remote config.\n  loggingEnabled = false;\n  // Sampling rate between 0 and 1.\n  tracesSamplingRate = 1;\n  networkRequestsSamplingRate = 1;\n\n  // Address of logging service.\n  logEndPointUrl =\n    'https://firebaselogging.googleapis.com/v0cc/log?format=json_proto';\n  // Performance event transport endpoint URL which should be compatible with proto3.\n  // New Address for transport service, not configurable via Remote Config.\n  flTransportEndpointUrl = mergeStrings(\n    'hts/frbslgigp.ogepscmv/ieo/eaylg',\n    'tp:/ieaeogn-agolai.o/1frlglgc/o'\n  );\n\n  transportKey = mergeStrings('AzSC8r6ReiGqFMyfvgow', 'Iayx0u-XT3vksVM-pIV');\n\n  // Source type for performance event logs.\n  logSource = 462;\n\n  // Flags which control per session logging of traces and network requests.\n  logTraceAfterSampling = false;\n  logNetworkAfterSampling = false;\n\n  // TTL of config retrieved from remote config in hours.\n  configTimeToLive = 12;\n\n  getFlTransportFullUrl(): string {\n    return this.flTransportEndpointUrl.concat('?key=', this.transportKey);\n  }\n\n  static getInstance(): SettingsService {\n    if (settingsServiceInstance === undefined) {\n      settingsServiceInstance = new SettingsService();\n    }\n    return settingsServiceInstance;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from './errors';\n\nexport function mergeStrings(part1: string, part2: string): string {\n  const sizeDiff = part1.length - part2.length;\n  if (sizeDiff < 0 || sizeDiff > 1) {\n    throw ERROR_FACTORY.create(ErrorCode.INVALID_STRING_MERGER_PARAMETER);\n  }\n\n  const resultArray = [];\n  for (let i = 0; i < part1.length; i++) {\n    resultArray.push(part1.charAt(i));\n    if (part2.length > i) {\n      resultArray.push(part2.charAt(i));\n    }\n  }\n\n  return resultArray.join('');\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Api } from '../services/api_service';\n\n// The values and orders of the following enums should not be changed.\nconst enum ServiceWorkerStatus {\n  UNKNOWN = 0,\n  UNSUPPORTED = 1,\n  CONTROLLED = 2,\n  UNCONTROLLED = 3\n}\n\nexport enum VisibilityState {\n  UNKNOWN = 0,\n  VISIBLE = 1,\n  HIDDEN = 2\n}\n\nconst enum EffectiveConnectionType {\n  UNKNOWN = 0,\n  CONNECTION_SLOW_2G = 1,\n  CONNECTION_2G = 2,\n  CONNECTION_3G = 3,\n  CONNECTION_4G = 4\n}\n\n/**\n * NetworkInformation\n *\n * ref: https://developer.mozilla.org/en-US/docs/Web/API/NetworkInformation\n */\ninterface NetworkInformationWithEffectiveType extends NetworkInformation {\n  // `effectiveType` is an experimental property and not included in\n  // TypeScript's typings for the native NetworkInformation interface\n  readonly effectiveType?: 'slow-2g' | '2g' | '3g' | '4g';\n}\n\ninterface NavigatorWithConnection extends Navigator {\n  readonly connection: NetworkInformationWithEffectiveType;\n}\n\nconst RESERVED_ATTRIBUTE_PREFIXES = ['firebase_', 'google_', 'ga_'];\nconst ATTRIBUTE_FORMAT_REGEX = new RegExp('^[a-zA-Z]\\\\w*$');\nconst MAX_ATTRIBUTE_NAME_LENGTH = 40;\nconst MAX_ATTRIBUTE_VALUE_LENGTH = 100;\n\nexport function getServiceWorkerStatus(): ServiceWorkerStatus {\n  const navigator = Api.getInstance().navigator;\n  if (navigator?.serviceWorker) {\n    if (navigator.serviceWorker.controller) {\n      return ServiceWorkerStatus.CONTROLLED;\n    } else {\n      return ServiceWorkerStatus.UNCONTROLLED;\n    }\n  } else {\n    return ServiceWorkerStatus.UNSUPPORTED;\n  }\n}\n\nexport function getVisibilityState(): VisibilityState {\n  const document = Api.getInstance().document;\n  const visibilityState = document.visibilityState;\n  switch (visibilityState) {\n    case 'visible':\n      return VisibilityState.VISIBLE;\n    case 'hidden':\n      return VisibilityState.HIDDEN;\n    default:\n      return VisibilityState.UNKNOWN;\n  }\n}\n\nexport function getEffectiveConnectionType(): EffectiveConnectionType {\n  const navigator = Api.getInstance().navigator;\n  const navigatorConnection = (navigator as NavigatorWithConnection).connection;\n  const effectiveType =\n    navigatorConnection && navigatorConnection.effectiveType;\n  switch (effectiveType) {\n    case 'slow-2g':\n      return EffectiveConnectionType.CONNECTION_SLOW_2G;\n    case '2g':\n      return EffectiveConnectionType.CONNECTION_2G;\n    case '3g':\n      return EffectiveConnectionType.CONNECTION_3G;\n    case '4g':\n      return EffectiveConnectionType.CONNECTION_4G;\n    default:\n      return EffectiveConnectionType.UNKNOWN;\n  }\n}\n\nexport function isValidCustomAttributeName(name: string): boolean {\n  if (name.length === 0 || name.length > MAX_ATTRIBUTE_NAME_LENGTH) {\n    return false;\n  }\n  const matchesReservedPrefix = RESERVED_ATTRIBUTE_PREFIXES.some(prefix =>\n    name.startsWith(prefix)\n  );\n  return !matchesReservedPrefix && !!name.match(ATTRIBUTE_FORMAT_REGEX);\n}\n\nexport function isValidCustomAttributeValue(value: string): boolean {\n  return value.length !== 0 && value.length <= MAX_ATTRIBUTE_VALUE_LENGTH;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from './errors';\nimport { FirebaseApp } from '@firebase/app';\n\nexport function getAppId(firebaseApp: FirebaseApp): string {\n  const appId = firebaseApp.options?.appId;\n  if (!appId) {\n    throw ERROR_FACTORY.create(ErrorCode.NO_APP_ID);\n  }\n  return appId;\n}\n\nexport function getProjectId(firebaseApp: FirebaseApp): string {\n  const projectId = firebaseApp.options?.projectId;\n  if (!projectId) {\n    throw ERROR_FACTORY.create(ErrorCode.NO_PROJECT_ID);\n  }\n  return projectId;\n}\n\nexport function getApiKey(firebaseApp: FirebaseApp): string {\n  const apiKey = firebaseApp.options?.apiKey;\n  if (!apiKey) {\n    throw ERROR_FACTORY.create(ErrorCode.NO_API_KEY);\n  }\n  return apiKey;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  CONFIG_EXPIRY_LOCAL_STORAGE_KEY,\n  CONFIG_LOCAL_STORAGE_KEY,\n  SDK_VERSION\n} from '../constants';\nimport { consoleLogger } from '../utils/console_logger';\nimport { ERROR_FACTORY, ErrorCode } from '../utils/errors';\n\nimport { Api } from './api_service';\nimport { getAuthTokenPromise } from './iid_service';\nimport { SettingsService } from './settings_service';\nimport { Performance<PERSON>ontroller } from '../controllers/perf';\nimport { getProjectId, getApiKey, getAppId } from '../utils/app_utils';\n\nconst REMOTE_CONFIG_SDK_VERSION = '0.0.1';\n\ninterface SecondaryConfig {\n  loggingEnabled?: boolean;\n  logSource?: number;\n  logEndPointUrl?: string;\n  transportKey?: string;\n  tracesSamplingRate?: number;\n  networkRequestsSamplingRate?: number;\n}\n\n// These values will be used if the remote config object is successfully\n// retrieved, but the template does not have these fields.\nconst DEFAULT_CONFIGS: SecondaryConfig = {\n  loggingEnabled: true\n};\n\n/* eslint-disable camelcase */\ninterface RemoteConfigTemplate {\n  fpr_enabled?: string;\n  fpr_log_source?: string;\n  fpr_log_endpoint_url?: string;\n  fpr_log_transport_key?: string;\n  fpr_log_transport_web_percent?: string;\n  fpr_vc_network_request_sampling_rate?: string;\n  fpr_vc_trace_sampling_rate?: string;\n  fpr_vc_session_sampling_rate?: string;\n}\n/* eslint-enable camelcase */\n\ninterface RemoteConfigResponse {\n  entries?: RemoteConfigTemplate;\n  state?: string;\n}\n\nconst FIS_AUTH_PREFIX = 'FIREBASE_INSTALLATIONS_AUTH';\n\nexport function getConfig(\n  performanceController: PerformanceController,\n  iid: string\n): Promise<void> {\n  const config = getStoredConfig();\n  if (config) {\n    processConfig(config);\n    return Promise.resolve();\n  }\n\n  return getRemoteConfig(performanceController, iid)\n    .then(processConfig)\n    .then(\n      config => storeConfig(config),\n      /** Do nothing for error, use defaults set in settings service. */\n      () => {}\n    );\n}\n\nfunction getStoredConfig(): RemoteConfigResponse | undefined {\n  const localStorage = Api.getInstance().localStorage;\n  if (!localStorage) {\n    return;\n  }\n  const expiryString = localStorage.getItem(CONFIG_EXPIRY_LOCAL_STORAGE_KEY);\n  if (!expiryString || !configValid(expiryString)) {\n    return;\n  }\n\n  const configStringified = localStorage.getItem(CONFIG_LOCAL_STORAGE_KEY);\n  if (!configStringified) {\n    return;\n  }\n  try {\n    const configResponse: RemoteConfigResponse = JSON.parse(configStringified);\n    return configResponse;\n  } catch {\n    return;\n  }\n}\n\nfunction storeConfig(config: RemoteConfigResponse | undefined): void {\n  const localStorage = Api.getInstance().localStorage;\n  if (!config || !localStorage) {\n    return;\n  }\n\n  localStorage.setItem(CONFIG_LOCAL_STORAGE_KEY, JSON.stringify(config));\n  localStorage.setItem(\n    CONFIG_EXPIRY_LOCAL_STORAGE_KEY,\n    String(\n      Date.now() +\n        SettingsService.getInstance().configTimeToLive * 60 * 60 * 1000\n    )\n  );\n}\n\nconst COULD_NOT_GET_CONFIG_MSG =\n  'Could not fetch config, will use default configs';\n\nfunction getRemoteConfig(\n  performanceController: PerformanceController,\n  iid: string\n): Promise<RemoteConfigResponse | undefined> {\n  // Perf needs auth token only to retrieve remote config.\n  return getAuthTokenPromise(performanceController.installations)\n    .then(authToken => {\n      const projectId = getProjectId(performanceController.app);\n      const apiKey = getApiKey(performanceController.app);\n      const configEndPoint = `https://firebaseremoteconfig.googleapis.com/v1/projects/${projectId}/namespaces/fireperf:fetch?key=${apiKey}`;\n      const request = new Request(configEndPoint, {\n        method: 'POST',\n        headers: { Authorization: `${FIS_AUTH_PREFIX} ${authToken}` },\n        /* eslint-disable camelcase */\n        body: JSON.stringify({\n          app_instance_id: iid,\n          app_instance_id_token: authToken,\n          app_id: getAppId(performanceController.app),\n          app_version: SDK_VERSION,\n          sdk_version: REMOTE_CONFIG_SDK_VERSION\n        })\n        /* eslint-enable camelcase */\n      });\n      return fetch(request).then(response => {\n        if (response.ok) {\n          return response.json() as RemoteConfigResponse;\n        }\n        // In case response is not ok. This will be caught by catch.\n        throw ERROR_FACTORY.create(ErrorCode.RC_NOT_OK);\n      });\n    })\n    .catch(() => {\n      consoleLogger.info(COULD_NOT_GET_CONFIG_MSG);\n      return undefined;\n    });\n}\n\n/**\n * Processes config coming either from calling RC or from local storage.\n * This method only runs if call is successful or config in storage\n * is valid.\n */\nfunction processConfig(\n  config?: RemoteConfigResponse\n): RemoteConfigResponse | undefined {\n  if (!config) {\n    return config;\n  }\n  const settingsServiceInstance = SettingsService.getInstance();\n  const entries = config.entries || {};\n  if (entries.fpr_enabled !== undefined) {\n    // TODO: Change the assignment of loggingEnabled once the received type is\n    // known.\n    settingsServiceInstance.loggingEnabled =\n      String(entries.fpr_enabled) === 'true';\n  } else if (DEFAULT_CONFIGS.loggingEnabled !== undefined) {\n    // Config retrieved successfully, but there is no fpr_enabled in template.\n    // Use secondary configs value.\n    settingsServiceInstance.loggingEnabled = DEFAULT_CONFIGS.loggingEnabled;\n  }\n  if (entries.fpr_log_source) {\n    settingsServiceInstance.logSource = Number(entries.fpr_log_source);\n  } else if (DEFAULT_CONFIGS.logSource) {\n    settingsServiceInstance.logSource = DEFAULT_CONFIGS.logSource;\n  }\n\n  if (entries.fpr_log_endpoint_url) {\n    settingsServiceInstance.logEndPointUrl = entries.fpr_log_endpoint_url;\n  } else if (DEFAULT_CONFIGS.logEndPointUrl) {\n    settingsServiceInstance.logEndPointUrl = DEFAULT_CONFIGS.logEndPointUrl;\n  }\n\n  // Key from Remote Config has to be non-empty string, otherwise use local value.\n  if (entries.fpr_log_transport_key) {\n    settingsServiceInstance.transportKey = entries.fpr_log_transport_key;\n  } else if (DEFAULT_CONFIGS.transportKey) {\n    settingsServiceInstance.transportKey = DEFAULT_CONFIGS.transportKey;\n  }\n\n  if (entries.fpr_vc_network_request_sampling_rate !== undefined) {\n    settingsServiceInstance.networkRequestsSamplingRate = Number(\n      entries.fpr_vc_network_request_sampling_rate\n    );\n  } else if (DEFAULT_CONFIGS.networkRequestsSamplingRate !== undefined) {\n    settingsServiceInstance.networkRequestsSamplingRate =\n      DEFAULT_CONFIGS.networkRequestsSamplingRate;\n  }\n  if (entries.fpr_vc_trace_sampling_rate !== undefined) {\n    settingsServiceInstance.tracesSamplingRate = Number(\n      entries.fpr_vc_trace_sampling_rate\n    );\n  } else if (DEFAULT_CONFIGS.tracesSamplingRate !== undefined) {\n    settingsServiceInstance.tracesSamplingRate =\n      DEFAULT_CONFIGS.tracesSamplingRate;\n  }\n  // Set the per session trace and network logging flags.\n  settingsServiceInstance.logTraceAfterSampling = shouldLogAfterSampling(\n    settingsServiceInstance.tracesSamplingRate\n  );\n  settingsServiceInstance.logNetworkAfterSampling = shouldLogAfterSampling(\n    settingsServiceInstance.networkRequestsSamplingRate\n  );\n  return config;\n}\n\nfunction configValid(expiry: string): boolean {\n  return Number(expiry) > Date.now();\n}\n\nfunction shouldLogAfterSampling(samplingRate: number): boolean {\n  return Math.random() <= samplingRate;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getIidPromise } from './iid_service';\nimport { getConfig } from './remote_config_service';\nimport { Api } from './api_service';\nimport { PerformanceController } from '../controllers/perf';\n\nconst enum InitializationStatus {\n  notInitialized = 1,\n  initializationPending,\n  initialized\n}\n\nlet initializationStatus = InitializationStatus.notInitialized;\n\nlet initializationPromise: Promise<void> | undefined;\n\nexport function getInitializationPromise(\n  performanceController: PerformanceController\n): Promise<void> {\n  initializationStatus = InitializationStatus.initializationPending;\n\n  initializationPromise =\n    initializationPromise || initializePerf(performanceController);\n\n  return initializationPromise;\n}\n\nexport function isPerfInitialized(): boolean {\n  return initializationStatus === InitializationStatus.initialized;\n}\n\nfunction initializePerf(\n  performanceController: PerformanceController\n): Promise<void> {\n  return getDocumentReadyComplete()\n    .then(() => getIidPromise(performanceController.installations))\n    .then(iid => getConfig(performanceController, iid))\n    .then(\n      () => changeInitializationStatus(),\n      () => changeInitializationStatus()\n    );\n}\n\n/**\n * Returns a promise which resolves whenever the document readystate is complete or\n * immediately if it is called after page load complete.\n */\nfunction getDocumentReadyComplete(): Promise<void> {\n  const document = Api.getInstance().document;\n  return new Promise(resolve => {\n    if (document && document.readyState !== 'complete') {\n      const handler = (): void => {\n        if (document.readyState === 'complete') {\n          document.removeEventListener('readystatechange', handler);\n          resolve();\n        }\n      };\n      document.addEventListener('readystatechange', handler);\n    } else {\n      resolve();\n    }\n  });\n}\n\nfunction changeInitializationStatus(): void {\n  initializationStatus = InitializationStatus.initialized;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SettingsService } from './settings_service';\nimport { ERROR_FACTORY, ErrorCode } from '../utils/errors';\nimport { consoleLogger } from '../utils/console_logger';\n\nconst DEFAULT_SEND_INTERVAL_MS = 10 * 1000;\nconst INITIAL_SEND_TIME_DELAY_MS = 5.5 * 1000;\n// If end point does not work, the call will be tried for these many times.\nconst DEFAULT_REMAINING_TRIES = 3;\nconst MAX_EVENT_COUNT_PER_REQUEST = 1000;\nlet remainingTries = DEFAULT_REMAINING_TRIES;\n\ninterface LogResponseDetails {\n  responseAction?: string;\n}\n\ninterface BatchEvent {\n  message: string;\n  eventTime: number;\n}\n\n/* eslint-disable camelcase */\n// CC/Fl accepted log format.\ninterface TransportBatchLogFormat {\n  request_time_ms: string;\n  client_info: ClientInfo;\n  log_source: number;\n  log_event: Log[];\n}\n\ninterface ClientInfo {\n  client_type: number;\n  js_client_info: {};\n}\n\ninterface Log {\n  source_extension_json_proto3: string;\n  event_time_ms: string;\n}\n/* eslint-enable camelcase */\n\nlet queue: BatchEvent[] = [];\n\nlet isTransportSetup: boolean = false;\n\nexport function setupTransportService(): void {\n  if (!isTransportSetup) {\n    processQueue(INITIAL_SEND_TIME_DELAY_MS);\n    isTransportSetup = true;\n  }\n}\n\n/**\n * Utilized by testing to clean up message queue and un-initialize transport service.\n */\nexport function resetTransportService(): void {\n  isTransportSetup = false;\n  queue = [];\n}\n\nfunction processQueue(timeOffset: number): void {\n  setTimeout(() => {\n    // If there is no remainingTries left, stop retrying.\n    if (remainingTries === 0) {\n      return;\n    }\n\n    // If there are no events to process, wait for DEFAULT_SEND_INTERVAL_MS and try again.\n    if (!queue.length) {\n      return processQueue(DEFAULT_SEND_INTERVAL_MS);\n    }\n\n    dispatchQueueEvents();\n  }, timeOffset);\n}\n\nfunction dispatchQueueEvents(): void {\n  // Extract events up to the maximum cap of single logRequest from top of \"official queue\".\n  // The staged events will be used for current logRequest attempt, remaining events will be kept\n  // for next attempt.\n  const staged = queue.splice(0, MAX_EVENT_COUNT_PER_REQUEST);\n\n  /* eslint-disable camelcase */\n  // We will pass the JSON serialized event to the backend.\n  const log_event: Log[] = staged.map(evt => ({\n    source_extension_json_proto3: evt.message,\n    event_time_ms: String(evt.eventTime)\n  }));\n\n  const data: TransportBatchLogFormat = {\n    request_time_ms: String(Date.now()),\n    client_info: {\n      client_type: 1, // 1 is JS\n      js_client_info: {}\n    },\n    log_source: SettingsService.getInstance().logSource,\n    log_event\n  };\n  /* eslint-enable camelcase */\n\n  sendEventsToFl(data, staged).catch(() => {\n    // If the request fails for some reason, add the events that were attempted\n    // back to the primary queue to retry later.\n    queue = [...staged, ...queue];\n    remainingTries--;\n    consoleLogger.info(`Tries left: ${remainingTries}.`);\n    processQueue(DEFAULT_SEND_INTERVAL_MS);\n  });\n}\n\nfunction sendEventsToFl(\n  data: TransportBatchLogFormat,\n  staged: BatchEvent[]\n): Promise<void> {\n  return postToFlEndpoint(data)\n    .then(res => {\n      if (!res.ok) {\n        consoleLogger.info('Call to Firebase backend failed.');\n      }\n      return res.json();\n    })\n    .then(res => {\n      // Find the next call wait time from the response.\n      const transportWait = Number(res.nextRequestWaitMillis);\n      let requestOffset = DEFAULT_SEND_INTERVAL_MS;\n      if (!isNaN(transportWait)) {\n        requestOffset = Math.max(transportWait, requestOffset);\n      }\n\n      // Delete request if response include RESPONSE_ACTION_UNKNOWN or DELETE_REQUEST action.\n      // Otherwise, retry request using normal scheduling if response include RETRY_REQUEST_LATER.\n      const logResponseDetails: LogResponseDetails[] = res.logResponseDetails;\n      if (\n        Array.isArray(logResponseDetails) &&\n        logResponseDetails.length > 0 &&\n        logResponseDetails[0].responseAction === 'RETRY_REQUEST_LATER'\n      ) {\n        queue = [...staged, ...queue];\n        consoleLogger.info(`Retry transport request later.`);\n      }\n\n      remainingTries = DEFAULT_REMAINING_TRIES;\n      // Schedule the next process.\n      processQueue(requestOffset);\n    });\n}\n\nfunction postToFlEndpoint(data: TransportBatchLogFormat): Promise<Response> {\n  const flTransportFullUrl =\n    SettingsService.getInstance().getFlTransportFullUrl();\n  return fetch(flTransportFullUrl, {\n    method: 'POST',\n    body: JSON.stringify(data)\n  });\n}\n\nfunction addToQueue(evt: BatchEvent): void {\n  if (!evt.eventTime || !evt.message) {\n    throw ERROR_FACTORY.create(ErrorCode.INVALID_CC_LOG);\n  }\n  // Add the new event to the queue.\n  queue = [...queue, evt];\n}\n\n/** Log handler for cc service to send the performance logs to the server. */\nexport function transportHandler(\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  serializer: (...args: any[]) => string\n): (...args: unknown[]) => void {\n  return (...args) => {\n    const message = serializer(...args);\n    addToQueue({\n      message,\n      eventTime: Date.now()\n    });\n  };\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getIid } from './iid_service';\nimport { NetworkRequest } from '../resources/network_request';\nimport { Trace } from '../resources/trace';\nimport { Api } from './api_service';\nimport { SettingsService } from './settings_service';\nimport {\n  getServiceWorkerStatus,\n  getVisibilityState,\n  VisibilityState,\n  getEffectiveConnectionType\n} from '../utils/attributes_utils';\nimport {\n  isPerfInitialized,\n  getInitializationPromise\n} from './initialization_service';\nimport { transportHandler } from './transport_service';\nimport { SDK_VERSION } from '../constants';\nimport { FirebaseApp } from '@firebase/app';\nimport { getAppId } from '../utils/app_utils';\n\nconst enum ResourceType {\n  NetworkRequest,\n  Trace\n}\n\n/* eslint-disable camelcase */\ninterface ApplicationInfo {\n  google_app_id: string;\n  app_instance_id?: string;\n  web_app_info: WebAppInfo;\n  application_process_state: number;\n}\n\ninterface WebAppInfo {\n  sdk_version: string;\n  page_url: string;\n  service_worker_status: number;\n  visibility_state: number;\n  effective_connection_type: number;\n}\n\ninterface PerfNetworkLog {\n  application_info: ApplicationInfo;\n  network_request_metric: NetworkRequestMetric;\n}\n\ninterface PerfTraceLog {\n  application_info: ApplicationInfo;\n  trace_metric: TraceMetric;\n}\n\ninterface NetworkRequestMetric {\n  url: string;\n  http_method: number;\n  http_response_code: number;\n  response_payload_bytes?: number;\n  client_start_time_us?: number;\n  time_to_response_initiated_us?: number;\n  time_to_response_completed_us?: number;\n}\n\ninterface TraceMetric {\n  name: string;\n  is_auto: boolean;\n  client_start_time_us: number;\n  duration_us: number;\n  counters?: { [key: string]: number };\n  custom_attributes?: { [key: string]: string };\n}\n\nlet logger: (\n  resource: NetworkRequest | Trace,\n  resourceType: ResourceType\n) => void | undefined;\n// This method is not called before initialization.\nfunction sendLog(\n  resource: NetworkRequest | Trace,\n  resourceType: ResourceType\n): void {\n  if (!logger) {\n    logger = transportHandler(serializer);\n  }\n  logger(resource, resourceType);\n}\n\nexport function logTrace(trace: Trace): void {\n  const settingsService = SettingsService.getInstance();\n  // Do not log if trace is auto generated and instrumentation is disabled.\n  if (!settingsService.instrumentationEnabled && trace.isAuto) {\n    return;\n  }\n  // Do not log if trace is custom and data collection is disabled.\n  if (!settingsService.dataCollectionEnabled && !trace.isAuto) {\n    return;\n  }\n  // Do not log if required apis are not available.\n  if (!Api.getInstance().requiredApisAvailable()) {\n    return;\n  }\n\n  // Only log the page load auto traces if page is visible.\n  if (trace.isAuto && getVisibilityState() !== VisibilityState.VISIBLE) {\n    return;\n  }\n\n  if (isPerfInitialized()) {\n    sendTraceLog(trace);\n  } else {\n    // Custom traces can be used before the initialization but logging\n    // should wait until after.\n    getInitializationPromise(trace.performanceController).then(\n      () => sendTraceLog(trace),\n      () => sendTraceLog(trace)\n    );\n  }\n}\n\nfunction sendTraceLog(trace: Trace): void {\n  if (!getIid()) {\n    return;\n  }\n\n  const settingsService = SettingsService.getInstance();\n  if (\n    !settingsService.loggingEnabled ||\n    !settingsService.logTraceAfterSampling\n  ) {\n    return;\n  }\n\n  setTimeout(() => sendLog(trace, ResourceType.Trace), 0);\n}\n\nexport function logNetworkRequest(networkRequest: NetworkRequest): void {\n  const settingsService = SettingsService.getInstance();\n  // Do not log network requests if instrumentation is disabled.\n  if (!settingsService.instrumentationEnabled) {\n    return;\n  }\n\n  // Do not log the js sdk's call to transport service domain to avoid unnecessary cycle.\n  // Need to blacklist both old and new endpoints to avoid migration gap.\n  const networkRequestUrl = networkRequest.url;\n\n  // Blacklist old log endpoint and new transport endpoint.\n  // Because Performance SDK doesn't instrument requests sent from SDK itself.\n  const logEndpointUrl = settingsService.logEndPointUrl.split('?')[0];\n  const flEndpointUrl = settingsService.flTransportEndpointUrl.split('?')[0];\n  if (\n    networkRequestUrl === logEndpointUrl ||\n    networkRequestUrl === flEndpointUrl\n  ) {\n    return;\n  }\n\n  if (\n    !settingsService.loggingEnabled ||\n    !settingsService.logNetworkAfterSampling\n  ) {\n    return;\n  }\n\n  setTimeout(() => sendLog(networkRequest, ResourceType.NetworkRequest), 0);\n}\n\nfunction serializer(\n  resource: NetworkRequest | Trace,\n  resourceType: ResourceType\n): string {\n  if (resourceType === ResourceType.NetworkRequest) {\n    return serializeNetworkRequest(resource as NetworkRequest);\n  }\n  return serializeTrace(resource as Trace);\n}\n\nfunction serializeNetworkRequest(networkRequest: NetworkRequest): string {\n  const networkRequestMetric: NetworkRequestMetric = {\n    url: networkRequest.url,\n    http_method: networkRequest.httpMethod || 0,\n    http_response_code: 200,\n    response_payload_bytes: networkRequest.responsePayloadBytes,\n    client_start_time_us: networkRequest.startTimeUs,\n    time_to_response_initiated_us: networkRequest.timeToResponseInitiatedUs,\n    time_to_response_completed_us: networkRequest.timeToResponseCompletedUs\n  };\n  const perfMetric: PerfNetworkLog = {\n    application_info: getApplicationInfo(\n      networkRequest.performanceController.app\n    ),\n    network_request_metric: networkRequestMetric\n  };\n  return JSON.stringify(perfMetric);\n}\n\nfunction serializeTrace(trace: Trace): string {\n  const traceMetric: TraceMetric = {\n    name: trace.name,\n    is_auto: trace.isAuto,\n    client_start_time_us: trace.startTimeUs,\n    duration_us: trace.durationUs\n  };\n\n  if (Object.keys(trace.counters).length !== 0) {\n    traceMetric.counters = trace.counters;\n  }\n  const customAttributes = trace.getAttributes();\n  if (Object.keys(customAttributes).length !== 0) {\n    traceMetric.custom_attributes = customAttributes;\n  }\n\n  const perfMetric: PerfTraceLog = {\n    application_info: getApplicationInfo(trace.performanceController.app),\n    trace_metric: traceMetric\n  };\n  return JSON.stringify(perfMetric);\n}\n\nfunction getApplicationInfo(firebaseApp: FirebaseApp): ApplicationInfo {\n  return {\n    google_app_id: getAppId(firebaseApp),\n    app_instance_id: getIid(),\n    web_app_info: {\n      sdk_version: SDK_VERSION,\n      page_url: Api.getInstance().getUrl(),\n      service_worker_status: getServiceWorkerStatus(),\n      visibility_state: getVisibilityState(),\n      effective_connection_type: getEffectiveConnectionType()\n    },\n    application_process_state: 0\n  };\n}\n\n/* eslint-enable camelcase */\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FIRST_PAINT_COUNTER_NAME,\n  FIRST_CONTENTFUL_PAINT_COUNTER_NAME,\n  FIRST_INPUT_DELAY_COUNTER_NAME,\n  OOB_TRACE_PAGE_LOAD_PREFIX\n} from '../constants';\nimport { consoleLogger } from '../utils/console_logger';\n\nconst MAX_METRIC_NAME_LENGTH = 100;\nconst RESERVED_AUTO_PREFIX = '_';\nconst oobMetrics = [\n  FIRST_PAINT_COUNTER_NAME,\n  FIRST_CONTENTFUL_PAINT_COUNTER_NAME,\n  FIRST_INPUT_DELAY_COUNTER_NAME\n];\n\n/**\n * Returns true if the metric is custom and does not start with reserved prefix, or if\n * the metric is one of out of the box page load trace metrics.\n */\nexport function isValidMetricName(name: string, traceName?: string): boolean {\n  if (name.length === 0 || name.length > MAX_METRIC_NAME_LENGTH) {\n    return false;\n  }\n  return (\n    (traceName &&\n      traceName.startsWith(OOB_TRACE_PAGE_LOAD_PREFIX) &&\n      oobMetrics.indexOf(name) > -1) ||\n    !name.startsWith(RESERVED_AUTO_PREFIX)\n  );\n}\n\n/**\n * Converts the provided value to an integer value to be used in case of a metric.\n * @param providedValue Provided number value of the metric that needs to be converted to an integer.\n *\n * @returns Converted integer number to be set for the metric.\n */\nexport function convertMetricValueToInteger(providedValue: number): number {\n  const valueAsInteger: number = Math.floor(providedValue);\n  if (valueAsInteger < providedValue) {\n    consoleLogger.info(\n      `Metric value should be an Integer, setting the value as : ${valueAsInteger}.`\n    );\n  }\n  return valueAsInteger;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  TRACE_START_MARK_PREFIX,\n  TRACE_STOP_MARK_PREFIX,\n  TRACE_MEASURE_PREFIX,\n  OOB_TRACE_PAGE_LOAD_PREFIX,\n  FIRST_PAINT_COUNTER_NAME,\n  FIRST_CONTENTFUL_PAINT_COUNTER_NAME,\n  FIRST_INPUT_DELAY_COUNTER_NAME\n} from '../constants';\nimport { Api } from '../services/api_service';\nimport { logTrace } from '../services/perf_logger';\nimport { ERROR_FACTORY, ErrorCode } from '../utils/errors';\nimport {\n  isValidCustomAttributeName,\n  isValidCustomAttributeValue\n} from '../utils/attributes_utils';\nimport {\n  isValidMetricName,\n  convertMetricValueToInteger\n} from '../utils/metric_utils';\nimport { PerformanceTrace } from '../public_types';\nimport { PerformanceController } from '../controllers/perf';\n\nconst enum TraceState {\n  UNINITIALIZED = 1,\n  RUNNING,\n  TERMINATED\n}\n\nexport class Trace implements PerformanceTrace {\n  private state: TraceState = TraceState.UNINITIALIZED;\n  startTimeUs!: number;\n  durationUs!: number;\n  private customAttributes: { [key: string]: string } = {};\n  counters: { [counterName: string]: number } = {};\n  private api = Api.getInstance();\n  private randomId = Math.floor(Math.random() * 1000000);\n  private traceStartMark!: string;\n  private traceStopMark!: string;\n  private traceMeasure!: string;\n\n  /**\n   * @param performanceController The performance controller running.\n   * @param name The name of the trace.\n   * @param isAuto If the trace is auto-instrumented.\n   * @param traceMeasureName The name of the measure marker in user timing specification. This field\n   * is only set when the trace is built for logging when the user directly uses the user timing\n   * api (performance.mark and performance.measure).\n   */\n  constructor(\n    readonly performanceController: PerformanceController,\n    readonly name: string,\n    readonly isAuto = false,\n    traceMeasureName?: string\n  ) {\n    if (!this.isAuto) {\n      this.traceStartMark = `${TRACE_START_MARK_PREFIX}-${this.randomId}-${this.name}`;\n      this.traceStopMark = `${TRACE_STOP_MARK_PREFIX}-${this.randomId}-${this.name}`;\n      this.traceMeasure =\n        traceMeasureName ||\n        `${TRACE_MEASURE_PREFIX}-${this.randomId}-${this.name}`;\n\n      if (traceMeasureName) {\n        // For the case of direct user timing traces, no start stop will happen. The measure object\n        // is already available.\n        this.calculateTraceMetrics();\n      }\n    }\n  }\n\n  /**\n   * Starts a trace. The measurement of the duration starts at this point.\n   */\n  start(): void {\n    if (this.state !== TraceState.UNINITIALIZED) {\n      throw ERROR_FACTORY.create(ErrorCode.TRACE_STARTED_BEFORE, {\n        traceName: this.name\n      });\n    }\n    this.api.mark(this.traceStartMark);\n    this.state = TraceState.RUNNING;\n  }\n\n  /**\n   * Stops the trace. The measurement of the duration of the trace stops at this point and trace\n   * is logged.\n   */\n  stop(): void {\n    if (this.state !== TraceState.RUNNING) {\n      throw ERROR_FACTORY.create(ErrorCode.TRACE_STOPPED_BEFORE, {\n        traceName: this.name\n      });\n    }\n    this.state = TraceState.TERMINATED;\n    this.api.mark(this.traceStopMark);\n    this.api.measure(\n      this.traceMeasure,\n      this.traceStartMark,\n      this.traceStopMark\n    );\n    this.calculateTraceMetrics();\n    logTrace(this);\n  }\n\n  /**\n   * Records a trace with predetermined values. If this method is used a trace is created and logged\n   * directly. No need to use start and stop methods.\n   * @param startTime Trace start time since epoch in millisec\n   * @param duration The duration of the trace in millisec\n   * @param options An object which can optionally hold maps of custom metrics and custom attributes\n   */\n  record(\n    startTime: number,\n    duration: number,\n    options?: {\n      metrics?: { [key: string]: number };\n      attributes?: { [key: string]: string };\n    }\n  ): void {\n    if (startTime <= 0) {\n      throw ERROR_FACTORY.create(ErrorCode.NONPOSITIVE_TRACE_START_TIME, {\n        traceName: this.name\n      });\n    }\n    if (duration <= 0) {\n      throw ERROR_FACTORY.create(ErrorCode.NONPOSITIVE_TRACE_DURATION, {\n        traceName: this.name\n      });\n    }\n\n    this.durationUs = Math.floor(duration * 1000);\n    this.startTimeUs = Math.floor(startTime * 1000);\n    if (options && options.attributes) {\n      this.customAttributes = { ...options.attributes };\n    }\n    if (options && options.metrics) {\n      for (const metricName of Object.keys(options.metrics)) {\n        if (!isNaN(Number(options.metrics[metricName]))) {\n          this.counters[metricName] = Math.floor(\n            Number(options.metrics[metricName])\n          );\n        }\n      }\n    }\n    logTrace(this);\n  }\n\n  /**\n   * Increments a custom metric by a certain number or 1 if number not specified. Will create a new\n   * custom metric if one with the given name does not exist. The value will be floored down to an\n   * integer.\n   * @param counter Name of the custom metric\n   * @param numAsInteger Increment by value\n   */\n  incrementMetric(counter: string, numAsInteger = 1): void {\n    if (this.counters[counter] === undefined) {\n      this.putMetric(counter, numAsInteger);\n    } else {\n      this.putMetric(counter, this.counters[counter] + numAsInteger);\n    }\n  }\n\n  /**\n   * Sets a custom metric to a specified value. Will create a new custom metric if one with the\n   * given name does not exist. The value will be floored down to an integer.\n   * @param counter Name of the custom metric\n   * @param numAsInteger Set custom metric to this value\n   */\n  putMetric(counter: string, numAsInteger: number): void {\n    if (isValidMetricName(counter, this.name)) {\n      this.counters[counter] = convertMetricValueToInteger(numAsInteger ?? 0);\n    } else {\n      throw ERROR_FACTORY.create(ErrorCode.INVALID_CUSTOM_METRIC_NAME, {\n        customMetricName: counter\n      });\n    }\n  }\n\n  /**\n   * Returns the value of the custom metric by that name. If a custom metric with that name does\n   * not exist will return zero.\n   * @param counter\n   */\n  getMetric(counter: string): number {\n    return this.counters[counter] || 0;\n  }\n\n  /**\n   * Sets a custom attribute of a trace to a certain value.\n   * @param attr\n   * @param value\n   */\n  putAttribute(attr: string, value: string): void {\n    const isValidName = isValidCustomAttributeName(attr);\n    const isValidValue = isValidCustomAttributeValue(value);\n    if (isValidName && isValidValue) {\n      this.customAttributes[attr] = value;\n      return;\n    }\n    // Throw appropriate error when the attribute name or value is invalid.\n    if (!isValidName) {\n      throw ERROR_FACTORY.create(ErrorCode.INVALID_ATTRIBUTE_NAME, {\n        attributeName: attr\n      });\n    }\n    if (!isValidValue) {\n      throw ERROR_FACTORY.create(ErrorCode.INVALID_ATTRIBUTE_VALUE, {\n        attributeValue: value\n      });\n    }\n  }\n\n  /**\n   * Retrieves the value a custom attribute of a trace is set to.\n   * @param attr\n   */\n  getAttribute(attr: string): string | undefined {\n    return this.customAttributes[attr];\n  }\n\n  removeAttribute(attr: string): void {\n    if (this.customAttributes[attr] === undefined) {\n      return;\n    }\n    delete this.customAttributes[attr];\n  }\n\n  getAttributes(): { [key: string]: string } {\n    return { ...this.customAttributes };\n  }\n\n  private setStartTime(startTime: number): void {\n    this.startTimeUs = startTime;\n  }\n\n  private setDuration(duration: number): void {\n    this.durationUs = duration;\n  }\n\n  /**\n   * Calculates and assigns the duration and start time of the trace using the measure performance\n   * entry.\n   */\n  private calculateTraceMetrics(): void {\n    const perfMeasureEntries = this.api.getEntriesByName(this.traceMeasure);\n    const perfMeasureEntry = perfMeasureEntries && perfMeasureEntries[0];\n    if (perfMeasureEntry) {\n      this.durationUs = Math.floor(perfMeasureEntry.duration * 1000);\n      this.startTimeUs = Math.floor(\n        (perfMeasureEntry.startTime + this.api.getTimeOrigin()) * 1000\n      );\n    }\n  }\n\n  /**\n   * @param navigationTimings A single element array which contains the navigationTIming object of\n   * the page load\n   * @param paintTimings A array which contains paintTiming object of the page load\n   * @param firstInputDelay First input delay in millisec\n   */\n  static createOobTrace(\n    performanceController: PerformanceController,\n    navigationTimings: PerformanceNavigationTiming[],\n    paintTimings: PerformanceEntry[],\n    firstInputDelay?: number\n  ): void {\n    const route = Api.getInstance().getUrl();\n    if (!route) {\n      return;\n    }\n    const trace = new Trace(\n      performanceController,\n      OOB_TRACE_PAGE_LOAD_PREFIX + route,\n      true\n    );\n    const timeOriginUs = Math.floor(Api.getInstance().getTimeOrigin() * 1000);\n    trace.setStartTime(timeOriginUs);\n\n    // navigationTimings includes only one element.\n    if (navigationTimings && navigationTimings[0]) {\n      trace.setDuration(Math.floor(navigationTimings[0].duration * 1000));\n      trace.putMetric(\n        'domInteractive',\n        Math.floor(navigationTimings[0].domInteractive * 1000)\n      );\n      trace.putMetric(\n        'domContentLoadedEventEnd',\n        Math.floor(navigationTimings[0].domContentLoadedEventEnd * 1000)\n      );\n      trace.putMetric(\n        'loadEventEnd',\n        Math.floor(navigationTimings[0].loadEventEnd * 1000)\n      );\n    }\n\n    const FIRST_PAINT = 'first-paint';\n    const FIRST_CONTENTFUL_PAINT = 'first-contentful-paint';\n    if (paintTimings) {\n      const firstPaint = paintTimings.find(\n        paintObject => paintObject.name === FIRST_PAINT\n      );\n      if (firstPaint && firstPaint.startTime) {\n        trace.putMetric(\n          FIRST_PAINT_COUNTER_NAME,\n          Math.floor(firstPaint.startTime * 1000)\n        );\n      }\n      const firstContentfulPaint = paintTimings.find(\n        paintObject => paintObject.name === FIRST_CONTENTFUL_PAINT\n      );\n      if (firstContentfulPaint && firstContentfulPaint.startTime) {\n        trace.putMetric(\n          FIRST_CONTENTFUL_PAINT_COUNTER_NAME,\n          Math.floor(firstContentfulPaint.startTime * 1000)\n        );\n      }\n\n      if (firstInputDelay) {\n        trace.putMetric(\n          FIRST_INPUT_DELAY_COUNTER_NAME,\n          Math.floor(firstInputDelay * 1000)\n        );\n      }\n    }\n\n    logTrace(trace);\n  }\n\n  static createUserTimingTrace(\n    performanceController: PerformanceController,\n    measureName: string\n  ): void {\n    const trace = new Trace(\n      performanceController,\n      measureName,\n      false,\n      measureName\n    );\n    logTrace(trace);\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Api } from '../services/api_service';\nimport { logNetworkRequest } from '../services/perf_logger';\nimport { PerformanceController } from '../controllers/perf';\n\n// The order of values of this enum should not be changed.\nexport const enum HttpMethod {\n  HTTP_METHOD_UNKNOWN = 0,\n  GET = 1,\n  PUT = 2,\n  POST = 3,\n  DELETE = 4,\n  HEAD = 5,\n  PATCH = 6,\n  OPTIONS = 7,\n  TRACE = 8,\n  CONNECT = 9\n}\n\n// Durations are in microseconds.\nexport interface NetworkRequest {\n  performanceController: PerformanceController;\n  url: string;\n  httpMethod?: HttpMethod;\n  requestPayloadBytes?: number;\n  responsePayloadBytes?: number;\n  httpResponseCode?: number;\n  responseContentType?: string;\n  startTimeUs?: number;\n  timeToRequestCompletedUs?: number;\n  timeToResponseInitiatedUs?: number;\n  timeToResponseCompletedUs?: number;\n}\n\nexport function createNetworkRequestEntry(\n  performanceController: PerformanceController,\n  entry: PerformanceEntry\n): void {\n  const performanceEntry = entry as PerformanceResourceTiming;\n  if (!performanceEntry || performanceEntry.responseStart === undefined) {\n    return;\n  }\n  const timeOrigin = Api.getInstance().getTimeOrigin();\n  const startTimeUs = Math.floor(\n    (performanceEntry.startTime + timeOrigin) * 1000\n  );\n  const timeToResponseInitiatedUs = performanceEntry.responseStart\n    ? Math.floor(\n        (performanceEntry.responseStart - performanceEntry.startTime) * 1000\n      )\n    : undefined;\n  const timeToResponseCompletedUs = Math.floor(\n    (performanceEntry.responseEnd - performanceEntry.startTime) * 1000\n  );\n  // Remove the query params from logged network request url.\n  const url = performanceEntry.name && performanceEntry.name.split('?')[0];\n  const networkRequest: NetworkRequest = {\n    performanceController,\n    url,\n    responsePayloadBytes: performanceEntry.transferSize,\n    startTimeUs,\n    timeToResponseInitiatedUs,\n    timeToResponseCompletedUs\n  };\n\n  logNetworkRequest(networkRequest);\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Api } from './api_service';\nimport { Trace } from '../resources/trace';\nimport { createNetworkRequestEntry } from '../resources/network_request';\nimport { TRACE_MEASURE_PREFIX } from '../constants';\nimport { getIid } from './iid_service';\nimport { PerformanceController } from '../controllers/perf';\n\nconst FID_WAIT_TIME_MS = 5000;\n\nexport function setupOobResources(\n  performanceController: PerformanceController\n): void {\n  // Do not initialize unless iid is available.\n  if (!getIid()) {\n    return;\n  }\n  // The load event might not have fired yet, and that means performance navigation timing\n  // object has a duration of 0. The setup should run after all current tasks in js queue.\n  setTimeout(() => setupOobTraces(performanceController), 0);\n  setTimeout(() => setupNetworkRequests(performanceController), 0);\n  setTimeout(() => setupUserTimingTraces(performanceController), 0);\n}\n\nfunction setupNetworkRequests(\n  performanceController: PerformanceController\n): void {\n  const api = Api.getInstance();\n  const resources = api.getEntriesByType('resource');\n  for (const resource of resources) {\n    createNetworkRequestEntry(performanceController, resource);\n  }\n  api.setupObserver('resource', entry =>\n    createNetworkRequestEntry(performanceController, entry)\n  );\n}\n\nfunction setupOobTraces(performanceController: PerformanceController): void {\n  const api = Api.getInstance();\n  const navigationTimings = api.getEntriesByType(\n    'navigation'\n  ) as PerformanceNavigationTiming[];\n  const paintTimings = api.getEntriesByType('paint');\n  // If First Input Delay polyfill is added to the page, report the fid value.\n  // https://github.com/GoogleChromeLabs/first-input-delay\n  if (api.onFirstInputDelay) {\n    // If the fid call back is not called for certain time, continue without it.\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    let timeoutId: any = setTimeout(() => {\n      Trace.createOobTrace(\n        performanceController,\n        navigationTimings,\n        paintTimings\n      );\n      timeoutId = undefined;\n    }, FID_WAIT_TIME_MS);\n    api.onFirstInputDelay((fid: number) => {\n      if (timeoutId) {\n        clearTimeout(timeoutId);\n        Trace.createOobTrace(\n          performanceController,\n          navigationTimings,\n          paintTimings,\n          fid\n        );\n      }\n    });\n  } else {\n    Trace.createOobTrace(\n      performanceController,\n      navigationTimings,\n      paintTimings\n    );\n  }\n}\n\nfunction setupUserTimingTraces(\n  performanceController: PerformanceController\n): void {\n  const api = Api.getInstance();\n  // Run through the measure performance entries collected up to this point.\n  const measures = api.getEntriesByType('measure');\n  for (const measure of measures) {\n    createUserTimingTrace(performanceController, measure);\n  }\n  // Setup an observer to capture the measures from this point on.\n  api.setupObserver('measure', entry =>\n    createUserTimingTrace(performanceController, entry)\n  );\n}\n\nfunction createUserTimingTrace(\n  performanceController: PerformanceController,\n  measure: PerformanceEntry\n): void {\n  const measureName = measure.name;\n  // Do not create a trace, if the user timing marks and measures are created by the sdk itself.\n  if (\n    measureName.substring(0, TRACE_MEASURE_PREFIX.length) ===\n    TRACE_MEASURE_PREFIX\n  ) {\n    return;\n  }\n  Trace.createUserTimingTrace(performanceController, measureName);\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { setupOobResources } from '../services/oob_resources_service';\nimport { SettingsService } from '../services/settings_service';\nimport { getInitializationPromise } from '../services/initialization_service';\nimport { Api } from '../services/api_service';\nimport { FirebaseApp } from '@firebase/app';\nimport { _FirebaseInstallationsInternal } from '@firebase/installations';\nimport { PerformanceSettings, FirebasePerformance } from '../public_types';\nimport { validateIndexedDBOpenable } from '@firebase/util';\nimport { setupTransportService } from '../services/transport_service';\nimport { consoleLogger } from '../utils/console_logger';\n\nexport class PerformanceController implements FirebasePerformance {\n  private initialized: boolean = false;\n\n  constructor(\n    readonly app: FirebaseApp,\n    readonly installations: _FirebaseInstallationsInternal\n  ) {}\n\n  /**\n   * This method *must* be called internally as part of creating a\n   * PerformanceController instance.\n   *\n   * Currently it's not possible to pass the settings object through the\n   * constructor using Components, so this method exists to be called with the\n   * desired settings, to ensure nothing is collected without the user's\n   * consent.\n   */\n  _init(settings?: PerformanceSettings): void {\n    if (this.initialized) {\n      return;\n    }\n\n    if (settings?.dataCollectionEnabled !== undefined) {\n      this.dataCollectionEnabled = settings.dataCollectionEnabled;\n    }\n    if (settings?.instrumentationEnabled !== undefined) {\n      this.instrumentationEnabled = settings.instrumentationEnabled;\n    }\n\n    if (Api.getInstance().requiredApisAvailable()) {\n      validateIndexedDBOpenable()\n        .then(isAvailable => {\n          if (isAvailable) {\n            setupTransportService();\n            getInitializationPromise(this).then(\n              () => setupOobResources(this),\n              () => setupOobResources(this)\n            );\n            this.initialized = true;\n          }\n        })\n        .catch(error => {\n          consoleLogger.info(`Environment doesn't support IndexedDB: ${error}`);\n        });\n    } else {\n      consoleLogger.info(\n        'Firebase Performance cannot start if the browser does not support ' +\n          '\"Fetch\" and \"Promise\", or cookies are disabled.'\n      );\n    }\n  }\n\n  set instrumentationEnabled(val: boolean) {\n    SettingsService.getInstance().instrumentationEnabled = val;\n  }\n  get instrumentationEnabled(): boolean {\n    return SettingsService.getInstance().instrumentationEnabled;\n  }\n\n  set dataCollectionEnabled(val: boolean) {\n    SettingsService.getInstance().dataCollectionEnabled = val;\n  }\n  get dataCollectionEnabled(): boolean {\n    return SettingsService.getInstance().dataCollectionEnabled;\n  }\n}\n", "/**\n * The Firebase Performance Monitoring Web SDK.\n * This SDK does not work in a Node.js environment.\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebasePerformance,\n  PerformanceSettings,\n  PerformanceTrace\n} from './public_types';\nimport { ERROR_FACTORY, ErrorCode } from './utils/errors';\nimport { setupApi } from './services/api_service';\nimport { PerformanceController } from './controllers/perf';\nimport {\n  _registerComponent,\n  _getProvider,\n  registerVersion,\n  FirebaseApp,\n  getApp\n} from '@firebase/app';\nimport {\n  InstanceFactory,\n  ComponentContainer,\n  Component,\n  ComponentType\n} from '@firebase/component';\nimport { name, version } from '../package.json';\nimport { Trace } from './resources/trace';\nimport '@firebase/installations';\nimport { deepEqual, getModularInstance } from '@firebase/util';\n\nconst DEFAULT_ENTRY_NAME = '[DEFAULT]';\n\n/**\n * Returns a {@link FirebasePerformance} instance for the given app.\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n * @public\n */\nexport function getPerformance(\n  app: FirebaseApp = getApp()\n): FirebasePerformance {\n  app = getModularInstance(app);\n  const provider = _getProvider(app, 'performance');\n  const perfInstance = provider.getImmediate() as PerformanceController;\n  return perfInstance;\n}\n\n/**\n * Returns a {@link FirebasePerformance} instance for the given app. Can only be called once.\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n * @param settings - Optional settings for the {@link FirebasePerformance} instance.\n * @public\n */\nexport function initializePerformance(\n  app: FirebaseApp,\n  settings?: PerformanceSettings\n): FirebasePerformance {\n  app = getModularInstance(app);\n  const provider = _getProvider(app, 'performance');\n\n  // throw if an instance was already created.\n  // It could happen if initializePerformance() is called more than once, or getPerformance() is called first.\n  if (provider.isInitialized()) {\n    const existingInstance = provider.getImmediate();\n    const initialSettings = provider.getOptions() as PerformanceSettings;\n    if (deepEqual(initialSettings, settings ?? {})) {\n      return existingInstance;\n    } else {\n      throw ERROR_FACTORY.create(ErrorCode.ALREADY_INITIALIZED);\n    }\n  }\n\n  const perfInstance = provider.initialize({\n    options: settings\n  }) as PerformanceController;\n  return perfInstance;\n}\n\n/**\n * Returns a new `PerformanceTrace` instance.\n * @param performance - The {@link FirebasePerformance} instance to use.\n * @param name - The name of the trace.\n * @public\n */\nexport function trace(\n  performance: FirebasePerformance,\n  name: string\n): PerformanceTrace {\n  performance = getModularInstance(performance);\n  return new Trace(performance as PerformanceController, name);\n}\n\nconst factory: InstanceFactory<'performance'> = (\n  container: ComponentContainer,\n  { options: settings }: { options?: PerformanceSettings }\n) => {\n  // Dependencies\n  const app = container.getProvider('app').getImmediate();\n  const installations = container\n    .getProvider('installations-internal')\n    .getImmediate();\n\n  if (app.name !== DEFAULT_ENTRY_NAME) {\n    throw ERROR_FACTORY.create(ErrorCode.FB_NOT_DEFAULT);\n  }\n  if (typeof window === 'undefined') {\n    throw ERROR_FACTORY.create(ErrorCode.NO_WINDOW);\n  }\n  setupApi(window);\n  const perfInstance = new PerformanceController(app, installations);\n  perfInstance._init(settings);\n\n  return perfInstance;\n};\n\nfunction registerPerformance(): void {\n  _registerComponent(\n    new Component('performance', factory, ComponentType.PUBLIC)\n  );\n  registerVersion(name, version);\n  // BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n\nregisterPerformance();\n\nexport { FirebasePerformance, PerformanceSettings, PerformanceTrace };\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  trace,\n  FirebasePerformance,\n  // The PerformanceTrace type has not changed between modular and non-modular packages.\n  PerformanceTrace\n} from '@firebase/performance';\nimport { FirebasePerformance as FirebasePerformanceCompat } from '@firebase/performance-types';\nimport { FirebaseApp, _FirebaseService } from '@firebase/app-compat';\n\nexport class PerformanceCompatImpl\n  implements FirebasePerformanceCompat, _FirebaseService\n{\n  constructor(\n    public app: FirebaseApp,\n    readonly _delegate: FirebasePerformance\n  ) {}\n\n  get instrumentationEnabled(): boolean {\n    return this._delegate.instrumentationEnabled;\n  }\n\n  set instrumentationEnabled(val: boolean) {\n    this._delegate.instrumentationEnabled = val;\n  }\n\n  get dataCollectionEnabled(): boolean {\n    return this._delegate.dataCollectionEnabled;\n  }\n\n  set dataCollectionEnabled(val: boolean) {\n    this._delegate.dataCollectionEnabled = val;\n  }\n\n  trace(traceName: string): PerformanceTrace {\n    return trace(this._delegate, traceName);\n  }\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface Compat<T> {\n  _delegate: T;\n}\n\nexport function getModularInstance<ExpService>(\n  service: Compat<ExpService> | ExpService\n): ExpService {\n  if (service && (service as Compat<ExpService>)._delegate) {\n    return (service as Compat<ExpService>)._delegate;\n  } else {\n    return service as ExpService;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport firebase, { _FirebaseNamespace } from '@firebase/app-compat';\nimport {\n  Component,\n  ComponentContainer,\n  ComponentType\n} from '@firebase/component';\nimport { PerformanceCompatImpl } from './performance';\nimport { name as packageName, version } from '../package.json';\nimport { FirebasePerformance as FirebasePerformanceCompat } from '@firebase/performance-types';\n\nfunction registerPerformanceCompat(firebaseInstance: _FirebaseNamespace): void {\n  firebaseInstance.INTERNAL.registerComponent(\n    new Component(\n      'performance-compat',\n      performanceFactory,\n      ComponentType.PUBLIC\n    )\n  );\n\n  firebaseInstance.registerVersion(packageName, version);\n}\n\nfunction performanceFactory(\n  container: ComponentContainer\n): PerformanceCompatImpl {\n  const app = container.getProvider('app-compat').getImmediate();\n  // The following call will always succeed.\n  const performance = container.getProvider('performance').getImmediate();\n\n  return new PerformanceCompatImpl(app, performance);\n}\n\nregisterPerformanceCompat(firebase as _FirebaseNamespace);\n\ndeclare module '@firebase/app-compat' {\n  interface FirebaseNamespace {\n    performance: {\n      (app?: FirebaseApp): FirebasePerformanceCompat;\n    };\n  }\n  interface FirebaseApp {\n    performance(): FirebasePerformanceCompat;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport firebase from './app';\nimport './performance';\nimport { name, version } from '../package.json';\n\nfirebase.registerVersion(name, version, 'compat-lite');\n\nexport default firebase;\n"], "names": ["extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "prototype", "hasOwnProperty", "call", "__extends", "TypeError", "String", "__", "this", "constructor", "create", "__assign", "assign", "t", "s", "i", "n", "arguments", "length", "apply", "__awaiter", "thisArg", "_arguments", "P", "generator", "Promise", "resolve", "reject", "fulfilled", "value", "step", "next", "e", "rejected", "result", "done", "then", "__generator", "body", "f", "y", "_", "label", "sent", "trys", "ops", "g", "verb", "throw", "return", "Symbol", "iterator", "v", "op", "pop", "push", "__values", "o", "m", "__read", "r", "ar", "error", "__spread<PERSON><PERSON>y", "to", "from", "pack", "l", "slice", "concat", "_super", "stringToByteArray", "str", "out", "c", "charCodeAt", "base64", "byteToCharMap_", "charToByteMap_", "byteToCharMapWebSafe_", "charToByteMapWebSafe_", "ENCODED_VALS_BASE", "ENCODED_VALS", "ENCODED_VALS_WEBSAFE", "HAS_NATIVE_SUPPORT", "atob", "encodeByteArray", "input", "webSafe", "isArray", "Error", "init_", "byteToCharMap", "output", "byte1", "haveByte2", "byte2", "haveByte3", "byte3", "outByte3", "outByte4", "join", "encodeString", "btoa", "decodeString", "bytes", "pos", "u", "c2", "c3", "c1", "fromCharCode", "byteArrayToString", "decodeStringToByteArray", "charToByteMap", "char<PERSON>t", "byte4", "DecodeBase64StringError", "_this", "name", "base64urlEncodeWithoutPadding", "utf8Bytes", "replace", "deepExtend", "target", "source", "Date", "getTime", "undefined", "prop", "getGlobal", "self", "window", "global", "getDefaultsFromCookie", "document", "match", "cookie", "decoded", "console", "base64Decode", "JSON", "parse", "getDefaults", "__FIREBASE_DEFAULTS__", "process", "env", "defaultsJsonString", "getDefaultsFromEnvVariable", "info", "getDefaultAppConfig", "_a", "config", "Deferred", "wrapCallback", "callback", "promise", "catch", "isWebWorker", "WorkerGlobalScope", "isIndexedDBAvailable", "indexedDB", "validateIndexedDBOpenable", "preExist_1", "DB_CHECK_NAME_1", "request_1", "open", "onsuccess", "close", "deleteDatabase", "onupgradeneeded", "onerror", "message", "FirebaseError", "code", "customData", "captureStackTrace", "ErrorFactory", "data", "_i", "fullCode", "service", "template", "errors", "PATTERN", "key", "fullMessage", "serviceName", "contains", "obj", "deepEqual", "a", "a<PERSON><PERSON><PERSON>", "keys", "b<PERSON><PERSON><PERSON>", "aKeys_1", "k", "includes", "aProp", "bProp", "isObject", "bKeys_1", "thing", "createSubscribe", "executor", "onNoObservers", "proxy", "ObserverProxy", "subscribe", "bind", "forEachObserver", "observer", "complete", "nextOrObserver", "methods", "methods_1", "method", "implementsAnyMethods", "noop", "unsub", "unsubscribeOne", "observers", "finalized", "task", "finalError", "observerCount", "fn", "sendOne", "err", "unsubscribes", "Component", "setInstantiationMode", "mode", "instantiationMode", "setMultipleInstances", "multipleInstances", "setServiceProps", "props", "serviceProps", "setInstanceCreatedCallback", "onInstanceCreated", "instanceFactory", "type", "DEFAULT_ENTRY_NAME", "Provider", "get", "identifier", "normalizedIdentifier", "normalizeInstanceIdentifier", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "has", "deferred", "set", "isInitialized", "shouldAutoInitialize", "instance", "getOrInitializeService", "instanceIdentifier", "getImmediate", "options", "optional", "getComponent", "component", "setComponent", "_b", "entries", "_c", "_d", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clearInstance", "delete", "instancesOptions", "instances", "services", "values", "all", "filter", "map", "INTERNAL", "_delete", "isComponentSet", "getOptions", "initialize", "opts", "_e", "onInit", "existingCallbacks", "onInitCallbacks", "Set", "add", "existingInstance", "invokeOnInitCallbacks", "callbacks", "callbacks_1", "callbacks_1_1", "container", "Map", "ComponentContainer", "addComponent", "provider", "get<PERSON><PERSON><PERSON>", "addOrOverwriteComponent", "providers", "getProviders", "LogLevel", "defaultLogHandler", "logType", "args", "logLevel", "now", "toISOString", "ConsoleMethod", "levelStringToEnum", "debug", "DEBUG", "verbose", "VERBOSE", "INFO", "warn", "WARN", "ERROR", "silent", "SILENT", "defaultLogLevel", "<PERSON><PERSON>", "defineProperty", "_logLevel", "val", "setLogLevel", "_log<PERSON><PERSON><PERSON>", "_userLogHandler", "log", "setUserLogHandler", "logCallback", "instances_1", "customLogLevel", "level", "userLogHandler", "arg", "toString", "stringify", "ignored", "toLowerCase", "_loop_1", "instanceOfAny", "object", "constructors", "some", "idbProxyableTypes", "cursorAdvanceMethods", "cursorRequestMap", "WeakMap", "transactionDoneMap", "transactionStoreNamesMap", "transformCache", "reverseTransformCache", "idbProxyTraps", "receiver", "IDBTransaction", "objectStoreNames", "objectStore", "wrap", "wrapFunction", "func", "IDBDatabase", "transaction", "IDBCursor", "advance", "continue", "continuePrimaryKey", "unwrap", "storeNames", "tx", "sort", "transformCachableValue", "unlisten", "removeEventListener", "DOMException", "addEventListener", "IDBObjectStore", "IDBIndex", "Proxy", "IDBRequest", "request", "success", "promisifyRequest", "newValue", "openDB", "version", "blocked", "upgrade", "blocking", "terminated", "openPromise", "event", "oldVersion", "newVersion", "db", "readMethods", "writeMethods", "cachedMethods", "getMethod", "targetFuncName", "useIndex", "isWrite", "async", "storeName", "store", "index", "shift", "oldTraps", "PlatformLoggerServiceImpl", "getPlatformInfoString", "library", "logString", "logger", "PLATFORM_LOG_STRING", "appName", "_apps", "_serverApps", "_components", "_addComponent", "app", "_addOrOverwriteComponent", "_registerComponent", "componentName", "_f", "_get<PERSON><PERSON><PERSON>", "heartbeatController", "triggerHeartbeat", "_isFirebaseApp", "ERRORS", "ERROR_FACTORY", "FirebaseAppImpl", "checkDestroyed", "_automaticDataCollectionEnabled", "_name", "_options", "_config", "_container", "_isDeleted", "isDeleted", "automaticDataCollectionEnabled", "FirebaseServerAppImpl", "toJSON", "_refCount", "incRefCount", "_finalizationRegistry", "register", "decRefCount", "automaticCleanup", "deleteApp", "_serverConfig", "serverConfig", "<PERSON><PERSON><PERSON><PERSON>", "FinalizationRegistry", "releaseOnDeref", "registerVersion", "packageName", "SDK_VERSION", "initializeApp", "rawConfig", "existingApp", "newApp", "cleanupProviders", "libraryKeyOrName", "variant", "libraryMismatch", "versionMismatch", "warning", "onLog", "for<PERSON>ach", "inst", "DB_NAME", "DB_VERSION", "STORE_NAME", "db<PERSON><PERSON><PERSON>", "getDbPromise", "createObjectStore", "originalErrorMessage", "writeHeartbeatsToIndexedDB", "heartbeatObject", "put", "computeKey", "e_2", "idbGetError", "appId", "HeartbeatServiceImpl", "platformLogger", "agent", "date_1", "getUTCDateString", "_heartbeatsCache", "heartbeats", "_heartbeatsCachePromise", "lastSentHeartbeatDate", "singleDateHeartbeat", "date", "hbTimestamp", "valueOf", "_storage", "overwrite", "e_1", "getHeartbeatsHeader", "heartbeatsCache", "maxSize", "heartbeatsToSend", "unsentEntries", "heartbeatsCache_1", "heartbeatsCache_1_1", "heartbeatEntry", "find", "hb", "dates", "countBytes", "extractHeartbeatsForHeader", "headerString", "HeartbeatStorageImpl", "read", "substring", "runIndexedDBEnvironmentCheck", "_canUseIndexedDBPromise", "readHeartbeatsFromIndexedDB", "idbHeartbeatObject", "heartbeatsObject", "existingHeartbeatsObject", "clear", "settings", "_serverAppConfig", "appOptions", "nameObj", "nameString", "reduce", "hash", "Math", "imul", "_delegate", "firebase", "removeApp", "_getService", "_DEFAULT_ENTRY_NAME", "_removeServiceInstance", "createFirebaseNamespaceCore", "firebaseAppImpl", "apps", "namespace", "__esModule", "modularAPIs.initializeApp", "appCompat", "modularAPIs.registerVersion", "modularAPIs.setLogLevel", "modularAPIs.onLog", "modularAPIs.SDK_VERSION", "registerComponent", "componentNameWithoutCompat", "serviceNamespace", "modularAPIs._registerComponent", "appArg", "useAsService", "modularAPIs", "modularAPIs._DEFAULT_ENTRY_NAME", "createFirebaseNamespace", "extendNamespace", "globals", "sdkVersion", "indexOf", "firebaseNamespace", "registerCoreComponents", "PENDING_TIMEOUT_MS", "PACKAGE_VERSION", "INTERNAL_AUTH_VERSION", "INSTALLATIONS_API_URL", "TOKEN_EXPIRATION_BUFFER", "ERROR_DESCRIPTION_MAP", "isServerError", "getInstallationsEndpoint", "projectId", "extractAuthTokenInfoFromResponse", "response", "token", "requestStatus", "expiresIn", "responseExpiresIn", "Number", "creationTime", "getErrorFromResponse", "requestName", "json", "responseJson", "errorData", "serverCode", "serverMessage", "serverStatus", "status", "getHeaders", "Headers", "Content-Type", "Accept", "x-goog-api-key", "getHeadersWithAuth", "appConfig", "refreshToken", "headers", "append", "retryIfServerError", "sleep", "ms", "setTimeout", "VALID_FID_PATTERN", "INVALID_FID", "generateFid", "fidByteArray", "Uint8Array", "crypto", "msCrypto", "getRandomValues", "fid", "array", "bufferToBase64UrlSafe", "substr", "test", "<PERSON><PERSON><PERSON>", "fidChangeCallbacks", "fidChanged", "callFidChangeCallbacks", "channel", "broadcastChannel", "BroadcastChannel", "onmessage", "getBroadcastChannel", "postMessage", "size", "broadcastFidChange", "OBJECT_STORE_NAME", "oldValue", "remove", "update", "updateFn", "getInstallationEntry", "installations", "oldEntry", "installationEntry", "clearTimedOutRequest", "registrationStatus", "entryWithPromise", "registrationPromise", "updateInstallationRequest", "entry", "waitUntilFidRegistration", "navigator", "onLine", "registrationPromiseWithError", "inProgressEntry", "registrationTime", "heartbeatServiceProvider", "endpoint", "heartbeatService", "heartbeatsHeader", "authVersion", "fetch", "ok", "responseValue", "authToken", "createInstallationRequest", "registeredInstallationEntry", "registerInstallation", "triggerRegistrationIfNecessary", "generateAuthTokenRequest", "installation", "refreshAuthToken", "forceRefresh", "isEntryRegistered", "oldAuthToken", "isAuthTokenExpired", "tokenPromise", "updateAuthTokenRequest", "waitUntilAuthTokenRequest", "inProgressAuthToken", "requestTime", "updatedInstallationEntry", "fetchAuthTokenFromServer", "getToken", "completeInstallationRegistration", "installationsImpl", "getMissingValueError", "valueName", "internalFactory", "INSTALLATIONS_NAME", "getId", "configKeys_1", "configKeys_1_1", "keyName", "extractAppConfig", "apiInstance", "windowInstance", "TRACE_MEASURE_PREFIX", "CONFIG_LOCAL_STORAGE_KEY", "CONFIG_EXPIRY_LOCAL_STORAGE_KEY", "SERVICE_NAME", "consoleLogger", "iid", "settingsServiceInstance", "Api", "getUrl", "windowLocation", "href", "split", "mark", "performance", "measure", "measureName", "mark1", "mark2", "getEntriesByType", "getEntriesByName", "getTime<PERSON>rigin", "<PERSON><PERSON><PERSON><PERSON>", "timing", "navigationStart", "requiredApisAvailable", "cookieEnabled", "setupObserver", "entryType", "PerformanceObserver", "list", "getEntries", "observe", "entryTypes", "getInstance", "location", "localStorage", "perfMetrics", "onFirstInputDelay", "mergeStrings", "part1", "part2", "sizeDiff", "resultArray", "VisibilityState", "SettingsService", "getFlTransportFullUrl", "flTransportEndpointUrl", "transportKey", "instrumentationEnabled", "dataCollectionEnabled", "loggingEnabled", "tracesSamplingRate", "networkRequestsSamplingRate", "logEndPointUrl", "logSource", "logTraceAfterSampling", "logNetworkAfterSampling", "configTimeToLive", "RESERVED_ATTRIBUTE_PREFIXES", "ATTRIBUTE_FORMAT_REGEX", "RegExp", "getVisibilityState", "visibilityState", "VISIBLE", "HIDDEN", "UNKNOWN", "getAppId", "firebaseApp", "REMOTE_CONFIG_SDK_VERSION", "DEFAULT_CONFIGS", "FIS_AUTH_PREFIX", "getConfig", "performanceController", "expiryString", "getItem", "expiry", "config<PERSON><PERSON><PERSON>", "configStringified", "getStoredConfig", "processConfig", "installationsService", "authTokenPromise", "authTokenVal", "getAuthTokenPromise", "getProjectId", "getApi<PERSON>ey", "configEndPoint", "Request", "Authorization", "app_instance_id", "app_instance_id_token", "app_id", "app_version", "sdk_version", "COULD_NOT_GET_CONFIG_MSG", "setItem", "fpr_enabled", "fpr_log_source", "fpr_log_endpoint_url", "fpr_log_transport_key", "fpr_vc_network_request_sampling_rate", "fpr_vc_trace_sampling_rate", "shouldLogAfterSampling", "samplingRate", "random", "initializationPromise", "initializationStatus", "getInitializationPromise", "handler_1", "readyState", "getDocumentReadyComplete", "iidPromise", "iidVal", "changeInitializationStatus", "DEFAULT_SEND_INTERVAL_MS", "DEFAULT_REMAINING_TRIES", "MAX_EVENT_COUNT_PER_REQUEST", "remainingTries", "queue", "isTransportSetup", "processQueue", "timeOffset", "staged", "log_event", "splice", "evt", "source_extension_json_proto3", "event_time_ms", "eventTime", "flTransportFullUrl", "postToFlEndpoint", "res", "transportWait", "nextRequestWaitMillis", "requestOffset", "isNaN", "max", "logResponseDetails", "responseAction", "sendEventsToFl", "request_time_ms", "client_info", "client_type", "js_client_info", "log_source", "transportHandler", "serializer", "addToQueue", "sendLog", "resource", "resourceType", "logTrace", "trace", "settingsService", "isAuto", "sendTraceLog", "networkRequestMetric", "url", "http_method", "httpMethod", "http_response_code", "response_payload_bytes", "responsePayloadBytes", "client_start_time_us", "startTimeUs", "time_to_response_initiated_us", "timeToResponseInitiatedUs", "time_to_response_completed_us", "timeToResponseCompletedUs", "perfMetric", "application_info", "getApplicationInfo", "network_request_metric", "traceMetric", "is_auto", "duration_us", "durationUs", "counters", "customAttributes", "getAttributes", "custom_attributes", "trace_metric", "serializeTrace", "google_app_id", "web_app_info", "page_url", "service_worker_status", "serviceWorker", "controller", "visibility_state", "effective_connection_type", "navigatorConnection", "connection", "effectiveType", "getEffectiveConnectionType", "application_process_state", "oobMetrics", "Trace", "start", "state", "traceName", "api", "traceStartMark", "stop", "traceStopMark", "traceMeasure", "calculateTraceMetrics", "record", "startTime", "duration", "floor", "attributes", "metrics", "metricName", "incrementMetric", "counter", "numAsInteger", "putMetric", "startsWith", "customMetricName", "valueAsInteger", "providedValue", "getMetric", "putAttribute", "attr", "isValidName", "prefix", "isValidValue", "attributeName", "attributeValue", "getAttribute", "removeAttribute", "setStartTime", "setDuration", "perfMeasureEntries", "perfMeasureEntry", "createOobTrace", "navigationTimings", "paintTimings", "firstInputDelay", "route", "timeOriginUs", "domInteractive", "domContentLoadedEventEnd", "loadEventEnd", "<PERSON><PERSON><PERSON><PERSON>", "paintObject", "firstContentful<PERSON><PERSON>t", "createUserTimingTrace", "traceMeasureName", "randomId", "createNetworkRequestEntry", "networkRequest", "networkRequestUrl", "logEndpointUrl", "performanceEntry", "responseStart", "responseEnd", "transferSize", "flEndpointUrl", "FID_WAIT_TIME_MS", "setupOobResources", "timeoutId_1", "clearTimeout", "setupOobTraces", "resources", "resources_1", "setupNetworkRequests", "measures", "measures_1", "setupUserTimingTraces", "PerformanceController", "_init", "initialized", "isAvailable", "perfInstance", "PerformanceCompatImpl", "firebaseInstance", "performanceFactory"], "mappings": "wOAgBA,IAAIA,EAAgB,SAASC,EAAGC,GAI5B,OAHAF,EAAgBG,OAAOC,gBAClB,CAAEC,UAAW,cAAgBC,OAAS,SAAUL,EAAGC,GAAKD,EAAEI,UAAYH,IACvE,SAAUD,EAAGC,GAAK,IAAK,IAAIK,KAAKL,EAAOC,OAAOK,UAAUC,eAAeC,KAAKR,EAAGK,KAAIN,EAAEM,GAAKL,EAAEK,MAC3EN,EAAGC,IAGrB,SAASS,EAAUV,EAAGC,GACzB,GAAiB,mBAANA,GAA0B,OAANA,EAC3B,MAAM,IAAIU,UAAU,uBAAyBC,OAAOX,GAAK,iCAE7D,SAASY,IAAOC,KAAKC,YAAcf,EADnCD,EAAcC,EAAGC,GAEjBD,EAAEO,UAAkB,OAANN,EAAaC,OAAOc,OAAOf,IAAMY,EAAGN,UAAYN,EAAEM,UAAW,IAAIM,GAG5E,IAAII,EAAW,WAQlB,OAPAA,EAAWf,OAAOgB,QAAU,SAAkBC,GAC1C,IAAK,IAAIC,EAAGC,EAAI,EAAGC,EAAIC,UAAUC,OAAQH,EAAIC,EAAGD,IAE5C,IAAK,IAAIf,KADTc,EAAIG,UAAUF,GACOnB,OAAOK,UAAUC,eAAeC,KAAKW,EAAGd,KAAIa,EAAEb,GAAKc,EAAEd,IAE9E,OAAOa,IAEKM,MAAMX,KAAMS,YA8BzB,SAASG,EAAUC,EAASC,EAAYC,EAAGC,GAE9C,OAAO,IAAWD,EAANA,GAAUE,SAAU,SAAUC,EAASC,GAC/C,SAASC,EAAUC,GAAS,IAAMC,EAAKN,EAAUO,KAAKF,IAAW,MAAOG,GAAKL,EAAOK,IACpF,SAASC,EAASJ,GAAS,IAAMC,EAAKN,EAAiB,MAAEK,IAAW,MAAOG,GAAKL,EAAOK,IACvF,SAASF,EAAKI,GAJlB,IAAeL,EAIaK,EAAOC,KAAOT,EAAQQ,EAAOL,SAJ1CA,EAIyDK,EAAOL,iBAJ/BN,EAAIM,EAAQ,IAAIN,EAAE,SAAUG,GAAWA,EAAQG,MAITO,KAAKR,EAAWK,GAClGH,GAAMN,EAAYA,EAAUL,MAAME,EAASC,GAAc,KAAKS,UAI/D,SAASM,EAAYhB,EAASiB,GACjC,IAAsGC,EAAGC,EAAG3B,EAAxG4B,EAAI,CAAEC,MAAO,EAAGC,KAAM,WAAa,GAAW,EAAP9B,EAAE,GAAQ,MAAMA,EAAE,GAAI,OAAOA,EAAE,IAAO+B,KAAM,GAAIC,IAAK,IACzFC,EAAI,CAAEf,KAAMgB,EAAK,GAAIC,MAASD,EAAK,GAAIE,OAAUF,EAAK,IAA7D,MAAqF,mBAAXG,SAA0BJ,EAAEI,OAAOC,UAAY,WAAa,OAAO3C,OAAUsC,EACvJ,SAASC,EAAK/B,GAAK,OAAO,SAAUoC,GAAK,OACzC,SAAcC,GACV,GAAId,EAAG,MAAM,IAAIlC,UAAU,mCAC3B,KAAOoC,GAAG,IACN,GAAIF,EAAI,EAAGC,IAAM3B,EAAY,EAARwC,EAAG,GAASb,EAAU,OAAIa,EAAG,GAAKb,EAAS,SAAO3B,EAAI2B,EAAU,SAAM3B,EAAEV,KAAKqC,GAAI,GAAKA,EAAET,SAAWlB,EAAIA,EAAEV,KAAKqC,EAAGa,EAAG,KAAKlB,KAAM,OAAOtB,EAE3J,OADI2B,EAAI,GAAMa,EAAHxC,EAAQ,CAAS,EAARwC,EAAG,GAAQxC,EAAEgB,OACzBwB,GAAG,IACP,KAAK,EAAG,KAAK,EAAGxC,EAAIwC,EAAI,MACxB,KAAK,EAAc,OAAXZ,EAAEC,QAAgB,CAAEb,MAAOwB,EAAG,GAAIlB,MAAM,GAChD,KAAK,EAAGM,EAAEC,QAASF,EAAIa,EAAG,GAAIA,EAAK,CAAC,GAAI,SACxC,KAAK,EAAGA,EAAKZ,EAAEI,IAAIS,MAAOb,EAAEG,KAAKU,MAAO,SACxC,QACI,KAAkBzC,EAAe,GAA3BA,EAAI4B,EAAEG,MAAY1B,QAAcL,EAAEA,EAAEK,OAAS,MAAkB,IAAVmC,EAAG,IAAsB,IAAVA,EAAG,IAAW,CAAEZ,EAAI,EAAG,SACjG,GAAc,IAAVY,EAAG,MAAcxC,GAAMwC,EAAG,GAAKxC,EAAE,IAAMwC,EAAG,GAAKxC,EAAE,IAAM,CAAE4B,EAAEC,MAAQW,EAAG,GAAI,MAC9E,GAAc,IAAVA,EAAG,IAAYZ,EAAEC,MAAQ7B,EAAE,GAAI,CAAE4B,EAAEC,MAAQ7B,EAAE,GAAIA,EAAIwC,EAAI,MAC7D,GAAIxC,GAAK4B,EAAEC,MAAQ7B,EAAE,GAAI,CAAE4B,EAAEC,MAAQ7B,EAAE,GAAI4B,EAAEI,IAAIU,KAAKF,GAAK,MACvDxC,EAAE,IAAI4B,EAAEI,IAAIS,MAChBb,EAAEG,KAAKU,MAAO,SAEtBD,EAAKf,EAAKnC,KAAKkB,EAASoB,GAC1B,MAAOT,GAAKqB,EAAK,CAAC,EAAGrB,GAAIQ,EAAI,EAAa,QAAED,EAAI1B,EAAI,EACtD,GAAY,EAARwC,EAAG,GAAQ,MAAMA,EAAG,GAAI,MAAO,CAAExB,MAAOwB,EAAG,GAAKA,EAAG,QAAK,EAAQlB,MAAM,GArB9BL,CAAK,CAACd,EAAGoC,MAqCtD,SAASI,EAASC,GACrB,IAAI3C,EAAsB,mBAAXoC,QAAyBA,OAAOC,SAAUO,EAAI5C,GAAK2C,EAAE3C,GAAIC,EAAI,EAC5E,GAAI2C,EAAG,OAAOA,EAAEvD,KAAKsD,GACrB,GAAIA,GAAyB,iBAAbA,EAAEvC,OAAqB,MAAO,CAC1Ca,KAAM,WAEF,MAAO,CAAEF,OADe4B,EAApBA,GAAK1C,GAAK0C,EAAEvC,YAAY,EACZuC,IAAKA,EAAE1C,KAAMoB,MAAOsB,KAG5C,MAAM,IAAIpD,UAAUS,EAAI,0BAA4B,mCAGjD,SAAS6C,EAAOF,EAAGzC,GACtB,IAAI0C,EAAsB,mBAAXR,QAAyBO,EAAEP,OAAOC,UACjD,IAAKO,EAAG,OAAOD,EACf,IAAmBG,EAAY5B,EAA3BjB,EAAI2C,EAAEvD,KAAKsD,GAAOI,EAAK,GAC3B,IACI,WAAc,IAAN7C,GAAsB,EAANA,QAAc4C,EAAI7C,EAAEgB,QAAQI,MAAM0B,EAAGN,KAAKK,EAAE/B,OAExE,MAAOiC,GAAS9B,EAAI,CAAE8B,MAAOA,GACrB,QACJ,IACQF,IAAMA,EAAEzB,OAASuB,EAAI3C,EAAU,SAAI2C,EAAEvD,KAAKY,GAE1C,QAAE,GAAIiB,EAAG,MAAMA,EAAE8B,OAE7B,OAAOD,EAmBJ,SAASE,EAAcC,EAAIC,EAAMC,GACpC,GAAIA,GAA6B,IAArBjD,UAAUC,OAAc,IAAK,IAA4B2C,EAAxB9C,EAAI,EAAGoD,EAAIF,EAAK/C,OAAYH,EAAIoD,EAAGpD,KACxE8C,GAAQ9C,KAAKkD,KACJJ,EAAJA,GAAS9D,MAAME,UAAUmE,MAAMjE,KAAK8D,EAAM,EAAGlD,IAC/CA,GAAKkD,EAAKlD,IAGrB,OAAOiD,EAAGK,OAAOR,GAAM9D,MAAME,UAAUmE,MAAMjE,KAAK8D,IC1JtD,IAiUAK,EAjUMC,EAAoB,SAAUC,GAIlC,IAFA,IAAMC,EAAgB,GAClBzE,EAAI,EACCe,EAAI,EAAGA,EAAIyD,EAAItD,OAAQH,IAAK,CACnC,IAAI2D,EAAIF,EAAIG,WAAW5D,GACnB2D,EAAI,IACND,EAAIzE,KAAO0E,GACFA,EAAI,KACbD,EAAIzE,KAAQ0E,GAAK,EAAK,KAGL,QAAZ,MAAJA,IACD3D,EAAI,EAAIyD,EAAItD,QACyB,QAAZ,MAAxBsD,EAAIG,WAAW5D,EAAI,KAGpB2D,EAAI,QAAgB,KAAJA,IAAe,KAA6B,KAAtBF,EAAIG,aAAa5D,IACvD0D,EAAIzE,KAAQ0E,GAAK,GAAM,IACvBD,EAAIzE,KAAS0E,GAAK,GAAM,GAAM,KAI9BD,EAAIzE,KAAQ0E,GAAK,GAAM,IAHvBD,EAAIzE,KAAS0E,GAAK,EAAK,GAAM,KAV7BD,EAAIzE,KAAY,GAAJ0E,EAAU,KAkB1B,OAAOD,GA6DIG,EAAiB,CAI5BC,eAAgB,KAKhBC,eAAgB,KAMhBC,sBAAuB,KAMvBC,sBAAuB,KAMvBC,kBACE,iEAKFC,mBACE,OAAO1E,KAAKyE,kBAAoB,OAMlCE,2BACE,OAAO3E,KAAKyE,kBAAoB,OAUlCG,mBAAoC,mBAATC,KAW3BC,gBAAA,SAAgBC,EAA8BC,GAC5C,IAAKzF,MAAM0F,QAAQF,GACjB,MAAMG,MAAM,iDAGdlF,KAAKmF,QAQL,IANA,IAAMC,EAAgBJ,EAClBhF,KAAKuE,sBACLvE,KAAKqE,eAEHgB,EAAS,GAEN9E,EAAI,EAAGA,EAAIwE,EAAMrE,OAAQH,GAAK,EAAG,CACxC,IAAM+E,EAAQP,EAAMxE,GACdgF,EAAYhF,EAAI,EAAIwE,EAAMrE,OAC1B8E,EAAQD,EAAYR,EAAMxE,EAAI,GAAK,EACnCkF,EAAYlF,EAAI,EAAIwE,EAAMrE,OAC1BgF,EAAQD,EAAYV,EAAMxE,EAAI,GAAK,EAIrCoF,GAAqB,GAARH,IAAiB,EAAME,GAAS,EAC7CE,EAAmB,GAARF,EAEVD,IACHG,EAAW,GAENL,IACHI,EAAW,KAIfN,EAAOtC,KACLqC,EAdeE,GAAS,GAexBF,GAdyB,EAARE,IAAiB,EAAME,GAAS,GAejDJ,EAAcO,GACdP,EAAcQ,IAIlB,OAAOP,EAAOQ,KAAK,KAWrBC,aAAA,SAAaf,EAAeC,GAG1B,OAAIhF,KAAK4E,qBAAuBI,EACvBe,KAAKhB,GAEP/E,KAAK8E,gBAAgBf,EAAkBgB,GAAQC,IAWxDgB,aAAA,SAAajB,EAAeC,GAG1B,OAAIhF,KAAK4E,qBAAuBI,EACvBH,KAAKE,GA3LQ,SAAUkB,GAKlC,IAHA,IAAMhC,EAAgB,GAClBiC,EAAM,EACRhC,EAAI,EACCgC,EAAMD,EAAMvF,QAAQ,CACzB,IAWQyF,EAMAC,EACAC,EAlBFC,EAAKL,EAAMC,KACbI,EAAK,IACPrC,EAAIC,KAAOpE,OAAOyG,aAAaD,GACjB,IAALA,GAAYA,EAAK,KACpBF,EAAKH,EAAMC,KACjBjC,EAAIC,KAAOpE,OAAOyG,cAAoB,GAALD,IAAY,EAAW,GAALF,IACrC,IAALE,GAAYA,EAAK,KAKpBH,IACI,EAALG,IAAW,IAAa,IAJvBF,EAAKH,EAAMC,QAImB,IAAa,IAH3CG,EAAKJ,EAAMC,QAGuC,EAAW,GAFxDD,EAAMC,MAGf,MACFjC,EAAIC,KAAOpE,OAAOyG,aAAa,OAAUJ,GAAK,KAC9ClC,EAAIC,KAAOpE,OAAOyG,aAAa,OAAc,KAAJJ,MAEnCC,EAAKH,EAAMC,KACXG,EAAKJ,EAAMC,KACjBjC,EAAIC,KAAOpE,OAAOyG,cACT,GAALD,IAAY,IAAa,GAALF,IAAY,EAAW,GAALC,IAI9C,OAAOpC,EAAI4B,KAAK,IA+JPW,CAAkBxG,KAAKyG,wBAAwB1B,EAAOC,KAkB/DyB,wBAAA,SAAwB1B,EAAeC,GACrChF,KAAKmF,QAQL,IANA,IAAMuB,EAAgB1B,EAClBhF,KAAKwE,sBACLxE,KAAKsE,eAEHe,EAAmB,GAEhB9E,EAAI,EAAGA,EAAIwE,EAAMrE,QAAU,CAClC,IAAM4E,EAAQoB,EAAc3B,EAAM4B,OAAOpG,MAGnCiF,EADYjF,EAAIwE,EAAMrE,OACFgG,EAAc3B,EAAM4B,OAAOpG,IAAM,EAIrDmF,IAHJnF,EAEoBwE,EAAMrE,OACFgG,EAAc3B,EAAM4B,OAAOpG,IAAM,GAIrDqG,IAHJrG,EAEoBwE,EAAMrE,OACFgG,EAAc3B,EAAM4B,OAAOpG,IAAM,GAG3D,KAFEA,EAEW,MAAT+E,GAA0B,MAATE,GAA0B,MAATE,GAA0B,MAATkB,EACrD,MAAM,IAAIC,EAIZxB,EAAOtC,KADWuC,GAAS,EAAME,GAAS,GAG5B,KAAVE,IAEFL,EAAOtC,KADYyC,GAAS,EAAK,IAASE,GAAS,GAGrC,KAAVkB,GAEFvB,EAAOtC,KADY2C,GAAS,EAAK,IAAQkB,IAM/C,OAAOvB,GAQTF,MAAK,WACH,IAAKnF,KAAKqE,eAAgB,CACxBrE,KAAKqE,eAAiB,GACtBrE,KAAKsE,eAAiB,GACtBtE,KAAKuE,sBAAwB,GAC7BvE,KAAKwE,sBAAwB,GAG7B,IAAK,IAAIjE,EAAI,EAAGA,EAAIP,KAAK0E,aAAahE,OAAQH,IAC5CP,KAAKqE,eAAe9D,GAAKP,KAAK0E,aAAaiC,OAAOpG,GAClDP,KAAKsE,eAAetE,KAAKqE,eAAe9D,IAAMA,EAC9CP,KAAKuE,sBAAsBhE,GAAKP,KAAK2E,qBAAqBgC,OAAOpG,IACjEP,KAAKwE,sBAAsBxE,KAAKuE,sBAAsBhE,IAAMA,IAGnDP,KAAKyE,kBAAkB/D,SAC9BV,KAAKsE,eAAetE,KAAK2E,qBAAqBgC,OAAOpG,IAAMA,EAC3DP,KAAKwE,sBAAsBxE,KAAK0E,aAAaiC,OAAOpG,IAAMA,MAUpEsG,GAA6CjH,EAAKiH,EAAlD/C,EAA6CoB,OAE5C2B,GAFD,SAAAA,IAAA,IAECC,EAAA,OAAAhD,GAAAA,EAAAnD,MAAAX,KAAAS,YAAAT,YADU8G,EAAIC,KAAG,4BAMX,IASMC,EAAgC,SAAUhD,GAErD,OAVMiD,EAAYlD,EAUEC,GATbI,EAAOU,gBAAgBmC,GAAW,GAShBC,QAAQ,MAAO,IAXd,IACpBD,GCpTQ,SAAAE,EAAWC,EAAiBC,GAC1C,KAAMA,aAAkBjI,QACtB,OAAOiI,EAGT,OAAQA,EAAOpH,aACb,KAAKqH,KAIH,OAAO,IAAIA,KADOD,EACQE,WAE5B,KAAKnI,YACYoI,IAAXJ,IACFA,EAAS,IAEX,MACF,KAAK7H,MAEH6H,EAAS,GACT,MAEF,QAEE,OAAOC,EAGX,IAAK,IAAMI,KAAQJ,EAEZA,EAAO3H,eAAe+H,IAad,cAbmCA,IAG/CL,EAAmCK,GAAQN,EACzCC,EAAmCK,GACnCJ,EAAmCI,KAIxC,OAAOL,ECtDO,SAAAM,IACd,GAAoB,oBAATC,KACT,OAAOA,KAET,GAAsB,oBAAXC,OACT,OAAOA,OAET,GAAsB,oBAAXC,OACT,OAAOA,OAET,MAAM,IAAI3C,MAAM,mCC4CY,SAAxB4C,IACJ,GAAwB,oBAAbC,SAAX,CAIA,IACEC,EAAQD,SAASE,OAAOD,MAAM,iCAC9B,MAAOxG,GAGP,OAEF,IAAM0G,EAAUF,GHwRU,SAAUhE,GACpC,IACE,OAAOI,EAAO4B,aAAahC,GAAK,GAChC,MAAOxC,GACP2G,QAAQ7E,MAAM,wBAAyB9B,GAEzC,OAAO,KG9RkB4G,CAAaJ,EAAM,IAC5C,OAAOE,GAAWG,KAAKC,MAAMJ,IAlC/B,IA4CaK,EAAc,WACzB,IACE,OA7CFb,IAAYc,uBAUqB,WACjC,GAAuB,oBAAZC,cAAkD,IAAhBA,QAAQC,IAArD,CAGA,IAAMC,EAAqBF,QAAQC,IAAIF,sBACvC,OAAIG,EACKN,KAAKC,MAAMK,QADpB,GAgCIC,IACAd,IAEF,MAAOtG,GAQP,YADA2G,QAAQU,KAAK,sDAA+CrH,MA8CnDsH,EAAsB,WACjC,IAAAC,EAAA,OAAa,QAAbA,EAAAR,WAAa,IAAAQ,OAAA,EAAAA,EAAEC,QC/IjBC,GAgBEA,EAAYxJ,UAAAyJ,aAAZ,SACEC,GADF,IAuBCrC,EAAA9G,KApBC,OAAO,SAACsD,EAAOjC,GACTiC,EACFwD,EAAK3F,OAAOmC,GAEZwD,EAAK5F,QAAQG,GAES,mBAAb8H,IAGTrC,EAAKsC,QAAQC,MAAM,cAIK,IAApBF,EAASzI,OACXyI,EAAS7F,GAET6F,EAAS7F,EAAOjC,MAKzB4H,GApCC,SAAAA,IAAA,IAKCnC,EAAA9G,KAPDA,KAAMmB,OAA8B,aACpCnB,KAAOkB,QAA8B,aAEnClB,KAAKoJ,QAAU,IAAInI,QAAQ,SAACC,EAASC,GACnC2F,EAAK5F,QAAUA,EACf4F,EAAK3F,OAASA,ICqEJ,SAAAmI,IACd,MAC+B,oBAAtBC,mBACS,oBAAT5B,MACPA,gBAAgB4B,kBAkFJ,SAAAC,IACd,IACE,MAA4B,iBAAdC,UACd,MAAOjI,GACP,QAWY,SAAAkI,IACd,OAAO,IAAIzI,QAAQ,SAACC,EAASC,GAC3B,IACE,IAAIwI,GAAoB,EAClBC,EACJ,0DACIC,EAAUlC,KAAK8B,UAAUK,KAAKF,GACpCC,EAAQE,UAAY,WAClBF,EAAQnI,OAAOsI,QAEVL,GACHhC,KAAK8B,UAAUQ,eAAeL,GAEhC1I,GAAQ,IAEV2I,EAAQK,gBAAkB,WACxBP,GAAW,GAGbE,EAAQM,QAAU,iBAChBhJ,GAAoB,QAAb4H,EAAAc,EAAQvG,aAAK,IAAAyF,OAAA,EAAAA,EAAEqB,UAAW,KAEnC,MAAO9G,GACPnC,EAAOmC,MC5Jb,IAYAQ,EAAAuG,GAAmCzK,EAAKyK,EAAxCvG,EAAmCoB,OAuBlCmF,GAnBC,SAAAA,EAEWC,EACTF,EAEOG,GALT,IAOEzD,EAAAhD,EAAAnE,KAAAK,KAAMoK,IAWPpK,YAhBU8G,EAAIwD,KAAJA,EAGFxD,EAAUyD,WAAVA,EAPAzD,EAAIC,KAdI,gBA2Bf3H,OAAOC,eAAeyH,EAAMuD,EAAc5K,WAItCyF,MAAMsF,mBACRtF,MAAMsF,kBAAkB1D,EAAM2D,EAAahL,UAAUS,UAK3D,IAAAuK,GAUEA,EAAMhL,UAAAS,OAAN,SACEoK,GACA,IAAA,IAA4DI,EAAA,GAAAC,EAAA,EAA5DA,EAA4DlK,UAAAC,OAA5DiK,IAAAD,EAA4DC,EAAA,GAAAlK,UAAAkK,GAE5D,IAcuCD,EAdjCH,EAAcG,EAAK,IAAoB,GACvCE,EAAW,GAAG/G,OAAA7D,KAAK6K,QAAO,KAAAhH,OAAIyG,GAC9BQ,EAAW9K,KAAK+K,OAAOT,GAEvBF,EAAUU,GAUuBJ,EAVcH,EAAVO,EAW7B5D,QAAQ8D,EAAS,SAAC/I,EAAGgJ,GACnC,IAAM5J,EAAQqJ,EAAKO,GACnB,OAAgB,MAAT5J,EAAgBvB,OAAOuB,GAAS,IAAIwC,OAAAoH,WAbwB,QAE7DC,EAAc,GAAArH,OAAG7D,KAAKmL,YAAW,MAAAtH,OAAKuG,EAAO,MAAAvG,OAAK+G,EAAQ,MAIhE,OAFc,IAAIP,EAAcO,EAAUM,EAAaX,IAI1DE,GAtBC,SAAAA,EACmBI,EACAM,EACAJ,GAFA/K,KAAO6K,QAAPA,EACA7K,KAAWmL,YAAXA,EACAnL,KAAM+K,OAANA,EA4BrB,IAAMC,EAAU,gBCpHA,SAAAI,EAA2BC,EAAQJ,GACjD,OAAO7L,OAAOK,UAAUC,eAAeC,KAAK0L,EAAKJ,GAwCnC,SAAAK,EAAUC,EAAWpM,GACnC,GAAIoM,IAAMpM,EACR,OAAO,EAKT,IAFA,IAAMqM,EAAQpM,OAAOqM,KAAKF,GACpBG,EAAQtM,OAAOqM,KAAKtM,GACVwL,EAAA,EAAAgB,EAAAH,EAAAb,EAAKgB,EAAAjL,OAALiK,IAAO,CAAlB,IAAMiB,EAACD,EAAAhB,GACV,IAAKe,EAAMG,SAASD,GAClB,OAGF,IAAME,EAASP,EAA8BK,GACvCG,EAAS5M,EAA8ByM,GAC7C,GAAII,EAASF,IAAUE,EAASD,IAC9B,IAAKT,EAAUQ,EAAOC,GACpB,YAEG,GAAID,IAAUC,EACnB,OAIJ,IAAgB,IAAAhD,EAAA,EAAAkD,EAAAP,EAAA3C,EAAKkD,EAAAvL,OAALqI,IAAO,CAAZ6C,EAACK,EAAAlD,GACV,IAAKyC,EAAMK,SAASD,GAClB,OAGJ,OAAO,EAGT,SAASI,EAASE,GAChB,OAAiB,OAAVA,GAAmC,iBAAVA,EC9BlB,SAAAC,EACdC,EACAC,GAEA,IAAMC,EAAQ,IAAIC,EAAiBH,EAAUC,GAC7C,OAAOC,EAAME,UAAUC,KAAKH,GAO9B,IAAAC,GA6BEA,EAAI9M,UAAA8B,KAAJ,SAAKF,GACHrB,KAAK0M,gBAAgB,SAACC,GACpBA,EAASpL,KAAKF,MAIlBkL,EAAK9M,UAAA6D,MAAL,SAAMA,GACJtD,KAAK0M,gBAAgB,SAACC,GACpBA,EAASrJ,MAAMA,KAEjBtD,KAAKgK,MAAM1G,IAGbiJ,EAAA9M,UAAAmN,SAAA,WACE5M,KAAK0M,gBAAgB,SAACC,GACpBA,EAASC,aAEX5M,KAAKgK,SASPuC,EAAA9M,UAAA+M,UAAA,SACEK,EACAvJ,EACAsJ,GAHF,IAKMD,EA6DL7F,EAAA9G,KA3DC,QACqBwH,IAAnBqF,QACUrF,IAAVlE,QACakE,IAAboF,EAEA,MAAM,IAAI1H,MAAM,0BAoBIsC,KAPpBmF,EAiIN,SACEtB,EACAyB,GAEA,GAAmB,iBAARzB,GAA4B,OAARA,EAC7B,OAAO,EAGT,IAAqB,IAAAV,EAAA,EAAAoC,EAAAD,EAAAnC,EAAOoC,EAAArM,OAAPiK,IAAS,CAAzB,IAAMqC,EAAMD,EAAApC,GACf,GAAIqC,KAAU3B,GAA8B,mBAAhBA,EAAI2B,GAC9B,OAAO,EAIX,OAAO,EAvJHC,CAAqBJ,EAA8C,CACjE,OACA,QACA,aAGSA,EAEA,CACTtL,KAAMsL,EACNvJ,MAAKA,EACLsJ,SAAQA,IAICrL,OACXoL,EAASpL,KAAO2L,QAEK1F,IAAnBmF,EAASrJ,QACXqJ,EAASrJ,MAAQ4J,QAEO1F,IAAtBmF,EAASC,WACXD,EAASC,SAAWM,GAGtB,IAAMC,EAAQnN,KAAKoN,eAAeX,KAAKzM,KAAMA,KAAKqN,UAAW3M,QAuB7D,OAlBIV,KAAKsN,WAEPtN,KAAKuN,KAAK3L,KAAK,WACb,IACMkF,EAAK0G,WACPb,EAASrJ,MAAMwD,EAAK0G,YAEpBb,EAASC,WAEX,MAAOpL,OAObxB,KAAKqN,UAAWtK,KAAK4J,GAEdQ,GAKDZ,EAAc9M,UAAA2N,eAAtB,SAAuB7M,QACEiH,IAAnBxH,KAAKqN,gBAAiD7F,IAAtBxH,KAAKqN,UAAU9M,YAI5CP,KAAKqN,UAAU9M,KAEtBP,KAAKyN,cACsB,IAAvBzN,KAAKyN,oBAA8CjG,IAAvBxH,KAAKqM,eACnCrM,KAAKqM,cAAcrM,QAIfuM,EAAe9M,UAAAiN,gBAAvB,SAAwBgB,GACtB,IAAI1N,KAAKsN,UAOT,IAAK,IAAI/M,EAAI,EAAGA,EAAIP,KAAKqN,UAAW3M,OAAQH,IAC1CP,KAAK2N,QAAQpN,EAAGmN,IAOZnB,EAAA9M,UAAAkO,QAAR,SAAgBpN,EAAWmN,GAA3B,IAiBC5G,EAAA9G,KAdCA,KAAKuN,KAAK3L,KAAK,WACb,QAAuB4F,IAAnBV,EAAKuG,gBAAiD7F,IAAtBV,EAAKuG,UAAU9M,GACjD,IACEmN,EAAG5G,EAAKuG,UAAU9M,IAClB,MAAOiB,GAIgB,oBAAZ2G,SAA2BA,QAAQ7E,OAC5C6E,QAAQ7E,MAAM9B,OAOhB+K,EAAK9M,UAAAuK,MAAb,SAAc4D,GAAd,IAcC9G,EAAA9G,KAbKA,KAAKsN,YAGTtN,KAAKsN,WAAY,OACL9F,IAARoG,IACF5N,KAAKwN,WAAaI,GAIpB5N,KAAKuN,KAAK3L,KAAK,WACbkF,EAAKuG,eAAY7F,EACjBV,EAAKuF,mBAAgB7E,MAG1B+E,GA7KC,SAAYA,EAAAH,EAAuBC,GAAnC,IAYCvF,EAAA9G,KA1BOA,KAASqN,UAAmC,GAC5CrN,KAAY6N,aAAkB,GAE9B7N,KAAayN,cAAG,EAEhBzN,KAAAuN,KAAOtM,QAAQC,UACflB,KAASsN,WAAG,EASlBtN,KAAKqM,cAAgBA,EAIrBrM,KAAKuN,KACF3L,KAAK,WACJwK,EAAStF,KAEVuC,MAAM,SAAA7H,GACLsF,EAAKxD,MAAM9B,KAyMnB,SAAS0L,KC9QT,IAAAY,GAuBEA,EAAoBrO,UAAAsO,qBAApB,SAAqBC,GAEnB,OADAhO,KAAKiO,kBAAoBD,EAClBhO,MAGT8N,EAAoBrO,UAAAyO,qBAApB,SAAqBC,GAEnB,OADAnO,KAAKmO,kBAAoBA,EAClBnO,MAGT8N,EAAerO,UAAA2O,gBAAf,SAAgBC,GAEd,OADArO,KAAKsO,aAAeD,EACbrO,MAGT8N,EAA0BrO,UAAA8O,2BAA1B,SAA2BpF,GAEzB,OADAnJ,KAAKwO,kBAAoBrF,EAClBnJ,MAEV8N,GAzBC,SAAAA,EACW/G,EACA0H,EACAC,GAFA1O,KAAI+G,KAAJA,EACA/G,KAAeyO,gBAAfA,EACAzO,KAAI0O,KAAJA,EAnBX1O,KAAiBmO,mBAAG,EAIpBnO,KAAYsO,aAAe,GAE3BtO,KAAAiO,kBAA2C,OAE3CjO,KAAiBwO,kBAAwC,KCpBpD,IAAMG,EAAqB,YCgBlCC,GAoBEA,EAAGnP,UAAAoP,IAAH,SAAIC,GAEF,IAAMC,EAAuB/O,KAAKgP,4BAA4BF,GAE9D,IAAK9O,KAAKiP,kBAAkBC,IAAIH,GAAuB,CACrD,IAAMI,EAAW,IAAIlG,EAGrB,GAFAjJ,KAAKiP,kBAAkBG,IAAIL,EAAsBI,GAG/CnP,KAAKqP,cAAcN,IACnB/O,KAAKsP,uBAGL,IACE,IAAMC,EAAWvP,KAAKwP,uBAAuB,CAC3CC,mBAAoBV,IAElBQ,GACFJ,EAASjO,QAAQqO,GAEnB,MAAO/N,KAOb,OAAOxB,KAAKiP,kBAAkBJ,IAAIE,GAAuB3F,SAmB3DwF,EAAYnP,UAAAiQ,aAAZ,SAAaC,OAKLZ,EAAuB/O,KAAKgP,4BAChCW,MAAAA,OAAA,EAAAA,EAASb,YAELc,EAAgC,QAArB7G,EAAA4G,MAAAA,OAAA,EAAAA,EAASC,gBAAY,IAAA7G,GAAAA,EAEtC,IACE/I,KAAKqP,cAAcN,KACnB/O,KAAKsP,uBAaA,CAEL,GAAIM,EACF,OAAO,KAEP,MAAM1K,MAAM,WAAWrB,OAAA7D,KAAK+G,KAAI,sBAhBlC,IACE,OAAO/G,KAAKwP,uBAAuB,CACjCC,mBAAoBV,IAEtB,MAAOvN,GACP,GAAIoO,EACF,OAAO,KAEP,MAAMpO,IAadoN,EAAAnP,UAAAoQ,aAAA,WACE,OAAO7P,KAAK8P,WAGdlB,EAAYnP,UAAAsQ,aAAZ,SAAaD,WACX,GAAIA,EAAU/I,OAAS/G,KAAK+G,KAC1B,MAAM7B,MACJ,yBAAyBrB,OAAAiM,EAAU/I,KAAqB,kBAAAlD,OAAA7D,KAAK+G,KAAO,MAIxE,GAAI/G,KAAK8P,UACP,MAAM5K,MAAM,iBAAiBrB,OAAA7D,KAAK+G,KAAI,+BAMxC,GAHA/G,KAAK8P,UAAYA,EAGZ9P,KAAKsP,uBAAV,CAKA,GA2NgC,UA3NXQ,EA2NN7B,kBA1Nb,IACEjO,KAAKwP,uBAAuB,CAAEC,mBAAoBd,IAClD,MAAOnN,QAWX,IAGK,IAAAwO,EAAAhN,EAAAhD,KAAKiP,kBAAkBgB,WAASC,EAAAF,EAAAzO,QAAE2O,EAAAvO,KAAAuO,EAAAF,EAAAzO,OAAA,CAH5B,IAAA4O,EAAAhN,EAGV+M,EAAA7O,MAAA,GAFCoO,EAAkBU,EAAA,GAClBC,EAAgBD,EAAA,GAEVpB,EACJ/O,KAAKgP,4BAA4BS,GAEnC,IAEE,IAAMF,EAAWvP,KAAKwP,uBAAuB,CAC3CC,mBAAoBV,IAEtBqB,EAAiBlP,QAAQqO,GACzB,MAAO/N,0GAOboN,EAAanP,UAAA4Q,cAAb,SAAcvB,GACZ9O,KAAKiP,kBAAkBqB,OADXxB,OAAA,IAAAA,EAAuCH,EACrBG,GAC9B9O,KAAKuQ,iBAAiBD,OAAOxB,GAC7B9O,KAAKwQ,UAAUF,OAAOxB,IAKlBF,EAAAnP,UAAA6Q,OAAN,yGAGE,OAFMG,EAAWlR,MAAMkE,KAAKzD,KAAKwQ,UAAUE,UAE3C,CAAA,EAAMzP,QAAQ0P,IAAGpN,EAAAA,EAAA,GAAAJ,EACZsN,EACAG,OAAO,SAAA/F,GAAW,MAAA,aAAcA,IAEhCgG,IAAI,SAAAhG,GAAW,OAACA,EAAgBiG,SAAUR,kBAC1CG,EACAG,OAAO,SAAA/F,GAAW,MAAA,YAAaA,IAE/BgG,IAAI,SAAAhG,GAAW,OAACA,EAAgBkG,cAAU,mBAR/ChI,EAAA5G,iBAYFyM,EAAAnP,UAAAuR,eAAA,WACE,OAAyB,MAAlBhR,KAAK8P,WAGdlB,EAAanP,UAAA4P,cAAb,SAAcP,GACZ,OAAO9O,KAAKwQ,UAAUtB,IADVJ,OAAA,IAAAA,EAAuCH,EACzBG,IAG5BF,EAAUnP,UAAAwR,WAAV,SAAWnC,GACT,OAAO9O,KAAKuQ,iBAAiB1B,IADpBC,OAAA,IAAAA,EAAuCH,EACfG,IAAe,IAGlDF,EAAUnP,UAAAyR,WAAV,SAAWC,WACDnB,GADCmB,OAAA,IAAAA,EAA4B,GACZA,GAALxB,QAAZA,OAAU,IAAAK,EAAA,KACZjB,EAAuB/O,KAAKgP,4BAChCmC,EAAK1B,oBAEP,GAAIzP,KAAKqP,cAAcN,GACrB,MAAM7J,MACJ,GAAArB,OAAG7D,KAAK+G,KAAQ,KAAAlD,OAAAkL,EAAoD,mCAIxE,IAAK/O,KAAKgR,iBACR,MAAM9L,MAAM,aAAarB,OAAA7D,KAAK+G,KAAI,iCAGpC,IAAMwI,EAAWvP,KAAKwP,uBAAuB,CAC3CC,mBAAoBV,EACpBY,QAAOA,QAIT,IAGK,IAAAO,EAAAlN,EAAAhD,KAAKiP,kBAAkBgB,WAASE,EAAAD,EAAA3O,QAAE4O,EAAAxO,KAAAwO,EAAAD,EAAA3O,OAAA,CAH5B,IAAA6P,EAAAjO,EAGVgN,EAAA9O,MAAA,GAFCoO,EAAkB2B,EAAA,GAClBhB,EAAgBgB,EAAA,GAIZrC,IADF/O,KAAKgP,4BAA4BS,IAEjCW,EAAiBlP,QAAQqO,qGAI7B,OAAOA,GAWTX,EAAAnP,UAAA4R,OAAA,SAAOlI,EAA6B2F,OAC5BC,EAAuB/O,KAAKgP,4BAA4BF,GACxDwC,EAC0C,QAA9CvI,EAAA/I,KAAKuR,gBAAgB1C,IAAIE,UAAqB,IAAAhG,EAAAA,EAC9C,IAAIyI,IACNF,EAAkBG,IAAItI,GACtBnJ,KAAKuR,gBAAgBnC,IAAIL,EAAsBuC,GAE/C,IAAMI,EAAmB1R,KAAKwQ,UAAU3B,IAAIE,GAK5C,OAJI2C,GACFvI,EAASuI,EAAkB3C,GAGtB,WACLuC,EAAkBhB,OAAOnH,KAQrByF,EAAAnP,UAAAkS,sBAAR,SACEpC,EACAT,WAEM8C,EAAY5R,KAAKuR,gBAAgB1C,IAAIC,GAC3C,GAAK8C,MAGL,IAAuB,IAAAC,EAAA7O,EAAA4O,GAASE,EAAAD,EAAAtQ,QAAEuQ,EAAAnQ,KAAAmQ,EAAAD,EAAAtQ,OAAA,CAA7B,IAAM4H,EAAQ2I,EAAAzQ,MACjB,IACE8H,EAASoG,EAAUT,GACnB,MAAMkB,yGAMJpB,EAAsBnP,UAAA+P,uBAA9B,SAA+BzG,GAC7B,IAAA0G,EAAkB1G,EAAA0G,mBAClBO,EAAAjH,EAAA4G,QAAAA,OAAO,IAAAK,EAAG,GAAEA,EAKRT,EAAWvP,KAAKwQ,UAAU3B,IAAIY,GAClC,IAAKF,GAAYvP,KAAK8P,YACpBP,EAAWvP,KAAK8P,UAAUrB,gBAAgBzO,KAAK+R,UAAW,CACxDtC,oBAqD+BX,EArDmBW,KAsDlCd,OAAqBnH,EAAYsH,EArDjDa,QAAOA,IAET3P,KAAKwQ,UAAUpB,IAAIK,EAAoBF,GACvCvP,KAAKuQ,iBAAiBnB,IAAIK,EAAoBE,GAO9C3P,KAAK2R,sBAAsBpC,EAAUE,GAOjCzP,KAAK8P,UAAUtB,mBACjB,IACExO,KAAK8P,UAAUtB,kBACbxO,KAAK+R,UACLtC,EACAF,GAEF,MAAMW,IAMZ,OAAOX,GAAY,MAGbX,EAA2BnP,UAAAuP,4BAAnC,SACEF,GAEA,YAFA,IAAAA,IAAAA,EAAuCH,IAEnC3O,KAAK8P,WACA9P,KAAK8P,UAAU3B,kBAEfW,EAFgDH,GAMnDC,EAAAnP,UAAA6P,qBAAR,WACE,QACItP,KAAK8P,WACyB,aAAhC9P,KAAK8P,UAAU7B,mBAGpBW,GApUC,SACmBA,EAAA7H,EACAgL,GADA/R,KAAI+G,KAAJA,EACA/G,KAAS+R,UAATA,EAZX/R,KAAS8P,UAAwB,KACxB9P,KAAAwQ,UAAgD,IAAIwB,IACpDhS,KAAAiP,kBAGb,IAAI+C,IACShS,KAAAuQ,iBACf,IAAIyB,IACEhS,KAAAuR,gBAAuD,IAAIS,IClBrE,IAAAC,GAcEA,EAAYxS,UAAAyS,aAAZ,SAA6BpC,GAC3B,IAAMqC,EAAWnS,KAAKoS,YAAYtC,EAAU/I,MAC5C,GAAIoL,EAASnB,iBACX,MAAM,IAAI9L,MACR,aAAArB,OAAaiM,EAAU/I,KAAI,sCAAAlD,OAAqC7D,KAAK+G,OAIzEoL,EAASpC,aAAaD,IAGxBmC,EAAuBxS,UAAA4S,wBAAvB,SAAwCvC,GACrB9P,KAAKoS,YAAYtC,EAAU/I,MAC/BiK,kBAEXhR,KAAKsS,UAAUhC,OAAOR,EAAU/I,MAGlC/G,KAAKkS,aAAapC,IAUpBmC,EAAWxS,UAAA2S,YAAX,SAA4BrL,GAC1B,GAAI/G,KAAKsS,UAAUpD,IAAInI,GACrB,OAAO/G,KAAKsS,UAAUzD,IAAI9H,GAI5B,IAAMoL,EAAW,IAAIvD,EAAY7H,EAAM/G,MAGvC,OAFAA,KAAKsS,UAAUlD,IAAIrI,EAAMoL,GAElBA,GAGTF,EAAAxS,UAAA8S,aAAA,WACE,OAAOhT,MAAMkE,KAAKzD,KAAKsS,UAAU5B,WAEpCuB,GAtDC,SAAAA,EAA6BlL,GAAA/G,KAAI+G,KAAJA,EAFZ/G,KAAAsS,UAAY,IAAIN,ICgB5B,IAaKQ,EAbChC,EAAsB,IAavBgC,GAAAA,EAAAA,GAOX,IANCA,GAAA,MAAA,GAAA,QACAA,GAAAA,GAAA,QAAA,GAAA,UACAA,GAAAA,GAAA,KAAA,GAAA,OACAA,GAAAA,GAAA,KAAA,GAAA,OACAA,GAAAA,GAAA,MAAA,GAAA,QACAA,GAAAA,GAAA,OAAA,GAAA,SA+CoC,SAAhCC,EAAiClD,EAAUmD,GAAS,IAAA,IAAOC,EAAA,GAAAhI,EAAA,EAAPA,EAAOlK,UAAAC,OAAPiK,IAAAgI,EAAOhI,EAAA,GAAAlK,UAAAkK,GAC/D,KAAI+H,EAAUnD,EAASqD,UAAvB,CAGA,IAAMC,GAAM,IAAIvL,MAAOwL,cACjB9F,EAAS+F,EAAcL,GAC7B,IAAI1F,EAMF,MAAM,IAAI9H,MACR,8DAAArB,OAA8D6O,EAAO,MANvEvK,QAAQ6E,GAA4CrM,MAApDwH,QACE5E,EAAA,CAAA,IAAAM,OAAIgP,EAAG,OAAAhP,OAAM0L,EAASxI,KAAO,MAC1B4L,GACH,KAtDN,ICyBa,EDzBPK,EAA2D,CAC/DC,MAAST,EAASU,MAClBC,QAAWX,EAASY,QACpBvK,KAAQ2J,EAASa,KACjBC,KAAQd,EAASe,KACjBjQ,MAASkP,EAASgB,MAClBC,OAAUjB,EAASkB,QAMfC,EAA4BnB,EAASa,KAmBrCN,IAAahK,GAAA,IAChByJ,EAASU,OAAQ,MAClBnK,GAACyJ,EAASY,SAAU,MACpBrK,GAACyJ,EAASa,MAAO,OACjBtK,GAACyJ,EAASe,MAAO,OACjBxK,GAACyJ,EAASgB,OAAQ,YA0BpBI,GAmBExU,OAAAyU,eAAID,EAAQnU,UAAA,WAAA,CAAZoP,IAAA,WACE,OAAO7O,KAAK8T,WAGd1E,IAAA,SAAa2E,GACX,KAAMA,KAAOvB,GACX,MAAM,IAAI3S,UAAU,kBAAAgE,OAAkBkQ,EAAG,6BAE3C/T,KAAK8T,UAAYC,mCAInBH,EAAWnU,UAAAuU,YAAX,SAAYD,GACV/T,KAAK8T,UAA2B,iBAARC,EAAmBf,EAAkBe,GAAOA,GAQtE3U,OAAAyU,eAAID,EAAUnU,UAAA,aAAA,CAAdoP,IAAA,WACE,OAAO7O,KAAKiU,aAEd7E,IAAA,SAAe2E,GACb,GAAmB,mBAARA,EACT,MAAM,IAAIlU,UAAU,qDAEtBG,KAAKiU,YAAcF,mCAOrB3U,OAAAyU,eAAID,EAAcnU,UAAA,iBAAA,CAAlBoP,IAAA,WACE,OAAO7O,KAAKkU,iBAEd9E,IAAA,SAAmB2E,GACjB/T,KAAKkU,gBAAkBH,mCAOzBH,EAAAnU,UAAAwT,MAAA,WAAM,IAAA,IAAkBN,EAAA,GAAAhI,EAAA,EAAlBA,EAAkBlK,UAAAC,OAAlBiK,IAAAgI,EAAkBhI,GAAAlK,UAAAkK,GACtB3K,KAAKkU,iBAAmBlU,KAAKkU,gBAALvT,MAAAX,KAAqBuD,EAAA,CAAAvD,KAAMwS,EAASU,OAAUP,GAAI,IAC1E3S,KAAKiU,YAALtT,MAAAX,KAAiBuD,EAAA,CAAAvD,KAAMwS,EAASU,OAAUP,GAAM,KAElDiB,EAAAnU,UAAA0U,IAAA,WAAI,IAAA,IAAkBxB,EAAA,GAAAhI,EAAA,EAAlBA,EAAkBlK,UAAAC,OAAlBiK,IAAAgI,EAAkBhI,GAAAlK,UAAAkK,GACpB3K,KAAKkU,iBACHlU,KAAKkU,gBAALvT,MAAAX,KAAqBuD,EAAA,CAAAvD,KAAMwS,EAASY,SAAYT,GAAI,IACtD3S,KAAKiU,YAALtT,MAAAX,KAAiBuD,EAAA,CAAAvD,KAAMwS,EAASY,SAAYT,GAAM,KAEpDiB,EAAAnU,UAAAoJ,KAAA,WAAK,IAAA,IAAkB8J,EAAA,GAAAhI,EAAA,EAAlBA,EAAkBlK,UAAAC,OAAlBiK,IAAAgI,EAAkBhI,GAAAlK,UAAAkK,GACrB3K,KAAKkU,iBAAmBlU,KAAKkU,gBAALvT,MAAAX,KAAqBuD,EAAA,CAAAvD,KAAMwS,EAASa,MAASV,GAAI,IACzE3S,KAAKiU,YAALtT,MAAAX,KAAiBuD,EAAA,CAAAvD,KAAMwS,EAASa,MAASV,GAAM,KAEjDiB,EAAAnU,UAAA6T,KAAA,WAAK,IAAA,IAAkBX,EAAA,GAAAhI,EAAA,EAAlBA,EAAkBlK,UAAAC,OAAlBiK,IAAAgI,EAAkBhI,GAAAlK,UAAAkK,GACrB3K,KAAKkU,iBAAmBlU,KAAKkU,gBAALvT,MAAAX,KAAqBuD,EAAA,CAAAvD,KAAMwS,EAASe,MAASZ,GAAI,IACzE3S,KAAKiU,YAALtT,MAAAX,KAAiBuD,EAAA,CAAAvD,KAAMwS,EAASe,MAASZ,GAAM,KAEjDiB,EAAAnU,UAAA6D,MAAA,WAAM,IAAA,IAAkBqP,EAAA,GAAAhI,EAAA,EAAlBA,EAAkBlK,UAAAC,OAAlBiK,IAAAgI,EAAkBhI,GAAAlK,UAAAkK,GACtB3K,KAAKkU,iBAAmBlU,KAAKkU,gBAALvT,MAAAX,KAAqBuD,EAAA,CAAAvD,KAAMwS,EAASgB,OAAUb,GAAI,IAC1E3S,KAAKiU,YAALtT,MAAAX,KAAiBuD,EAAA,CAAAvD,KAAMwS,EAASgB,OAAUb,GAAM,KAEnDiB,GA/EC,SAAAA,EAAmB7M,GAAA/G,KAAI+G,KAAJA,EAUX/G,KAAS8T,UAAGH,EAsBZ3T,KAAWiU,YAAexB,EAc1BzS,KAAekU,gBAAsB,KA1C3C1D,EAAUzN,KAAK/C,MAmFH,SAAAoU,EACdC,EACA1E,GAEA,IAAW,IAAYhF,EAAA,EAAA2J,EAAA9D,EAAA7F,EAAA2J,EAAA5T,OAAAiK,KAAZ,SAAA4E,GACT,IAAIgF,EAAkC,KAClC5E,GAAWA,EAAQ6E,QACrBD,EAAiBvB,EAAkBrD,EAAQ6E,QAG3CjF,EAASkF,eADS,OAAhBJ,EACwB,KAEA,SACxB9E,EACAiF,GACA,IAAA,IAAkB7B,EAAA,GAAAhI,EAAA,EAAlBA,EAAkBlK,UAAAC,OAAlBiK,IAAAgI,EAAkBhI,EAAA,GAAAlK,UAAAkK,GAElB,IAAMP,EAAUuI,EACb9B,IAAI,SAAA6D,GACH,GAAW,MAAPA,EACF,OAAO,KACF,GAAmB,iBAARA,EAChB,OAAOA,EACF,GAAmB,iBAARA,GAAmC,kBAARA,EAC3C,OAAOA,EAAIC,WACN,GAAID,aAAexP,MACxB,OAAOwP,EAAItK,QAEX,IACE,OAAO/B,KAAKuM,UAAUF,GACtB,MAAOG,GACP,OAAO,QAIZjE,OAAO,SAAA8D,GAAO,OAAAA,IACd7O,KAAK,KACJ2O,IAAUD,MAAAA,EAAAA,EAAkBhF,EAASqD,WACvCyB,EAAY,CACVG,MAAOhC,EAASgC,GAAOM,cACvB1K,QAAOA,EACPuI,KAAIA,EACJjE,KAAMa,EAASxI,QAtCdgO,CAAQT,EAAA3J,IE/NrB,MAAMqK,GAAgB,CAACC,EAAQC,IAAiBA,EAAaC,KAAK,GAAOF,aAAkB/Q,GAE3F,IAAIkR,GACAC,GAqBJ,MAAMC,GAAmB,IAAIC,QACvBC,GAAqB,IAAID,QACzBE,GAA2B,IAAIF,QAC/BG,GAAiB,IAAIH,QACrBI,GAAwB,IAAIJ,QA0DlC,IAAIK,GAAgB,CAChB/G,IAAIzH,EAAQK,EAAMoO,GACd,GAAIzO,aAAkB0O,eAAgB,CAElC,GAAa,SAATrO,EACA,OAAO+N,GAAmB3G,IAAIzH,GAElC,GAAa,qBAATK,EACA,OAAOL,EAAO2O,kBAAoBN,GAAyB5G,IAAIzH,GAGnE,GAAa,UAATK,EACA,OAAOoO,EAASE,iBAAiB,QAC3BvO,EACAqO,EAASG,YAAYH,EAASE,iBAAiB,IAI7D,OAAOE,GAAK7O,EAAOK,KAEvB2H,IAAIhI,EAAQK,EAAMpG,GAEd,OADA+F,EAAOK,GAAQpG,GACR,GAEX6N,IAAI9H,EAAQK,GACR,OAAIL,aAAkB0O,iBACR,SAATrO,GAA4B,UAATA,IAGjBA,KAAQL,IAMvB,SAAS8O,GAAaC,GAIlB,OAAIA,IAASC,YAAY3W,UAAU4W,aAC7B,qBAAsBP,eAAerW,WA5GtC4V,GADGA,IACoB,CACpBiB,UAAU7W,UAAU8W,QACpBD,UAAU7W,UAAU+W,SACpBF,UAAU7W,UAAUgX,qBAqHE5K,SAASsK,GAC5B,YAAaxD,GAIhB,OADAwD,EAAKxV,MAAM+V,GAAO1W,MAAO2S,GAClBsD,GAAKX,GAAiBzG,IAAI7O,QAGlC,YAAa2S,GAGhB,OAAOsD,GAAKE,EAAKxV,MAAM+V,GAAO1W,MAAO2S,KAtB9B,SAAUgE,KAAehE,GAC5B,IAAMiE,EAAKT,EAAKxW,KAAK+W,GAAO1W,MAAO2W,KAAehE,GAElD,OADA8C,GAAyBrG,IAAIwH,EAAID,EAAWE,KAAOF,EAAWE,OAAS,CAACF,IACjEV,GAAKW,IAsBxB,SAASE,GAAuBzV,GAC5B,MAAqB,mBAAVA,EACA6U,GAAa7U,IAGpBA,aAAiByU,iBAhGec,EAiGDvV,EA/F/BmU,GAAmBtG,IAAI0H,KAErBjV,EAAO,IAAIV,QAAQ,CAACC,EAASC,KAC/B,MAAM4V,EAAW,KACbH,EAAGI,oBAAoB,WAAYpK,GACnCgK,EAAGI,oBAAoB,QAAS1T,GAChCsT,EAAGI,oBAAoB,QAAS1T,IAE9BsJ,EAAW,KACb1L,IACA6V,KAEEzT,EAAQ,KACVnC,EAAOyV,EAAGtT,OAAS,IAAI2T,aAAa,aAAc,eAClDF,KAEJH,EAAGM,iBAAiB,WAAYtK,GAChCgK,EAAGM,iBAAiB,QAAS5T,GAC7BsT,EAAGM,iBAAiB,QAAS5T,KAGjCkS,GAAmBpG,IAAIwH,EAAIjV,KA2EvBqT,GAAc3T,EAxJb+T,GADGA,IACiB,CACjBgB,YACAe,eACAC,SACAd,UACAR,iBAoJG,IAAIuB,MAAMhW,EAAOuU,IAErBvU,GArGX,IAAwCuV,EAI9BjV,EAmGV,SAASsU,GAAK5U,GAGV,GAAIA,aAAiBiW,WACjB,OA3IR,SAA0BC,GACtB,MAAMnO,EAAU,IAAInI,QAAQ,CAACC,EAASC,KAClC,MAAM4V,EAAW,KACbQ,EAAQP,oBAAoB,UAAWQ,GACvCD,EAAQP,oBAAoB,QAAS1T,IAEnCkU,EAAU,KACZtW,EAAQ+U,GAAKsB,EAAQ7V,SACrBqV,KAEEzT,EAAQ,KACVnC,EAAOoW,EAAQjU,OACfyT,KAEJQ,EAAQL,iBAAiB,UAAWM,GACpCD,EAAQL,iBAAiB,QAAS5T,KAetC,OAbA8F,EACKxH,KAAK,IAGFP,aAAiBiV,WACjBhB,GAAiBlG,IAAI/N,EAAOkW,KAI/BlO,MAAM,QAGXsM,GAAsBvG,IAAIhG,EAASmO,GAC5BnO,EA6GIqO,CAAiBpW,GAG5B,GAAIqU,GAAexG,IAAI7N,GACnB,OAAOqU,GAAe7G,IAAIxN,GAC9B,IAAMqW,EAAWZ,GAAuBzV,GAOxC,OAJIqW,IAAarW,IACbqU,GAAetG,IAAI/N,EAAOqW,GAC1B/B,GAAsBvG,IAAIsI,EAAUrW,IAEjCqW,EAEX,MAAMhB,GAAS,GAAWf,GAAsB9G,IAAIxN,GD5KpD,SAASsW,GAAO5Q,EAAM6Q,EAAS,CAAEC,QAAAA,EAASC,QAAAA,EAASC,SAAAA,EAAUC,WAAAA,GAAe,IACxE,MAAMT,EAAU9N,UAAUK,KAAK/C,EAAM6Q,GAC/BK,EAAchC,GAAKsB,GAoBzB,OAnBIO,GACAP,EAAQL,iBAAiB,gBAAiB,IACtCY,EAAQ7B,GAAKsB,EAAQ7V,QAASwW,EAAMC,WAAYD,EAAME,WAAYnC,GAAKsB,EAAQlB,aAAc6B,KAGjGL,GACAN,EAAQL,iBAAiB,UAAW,GAAWW,EAE/CK,EAAMC,WAAYD,EAAME,WAAYF,IAExCD,EACKrW,KAAK,IACFoW,GACAK,EAAGnB,iBAAiB,QAAS,IAAMc,KACnCD,GACAM,EAAGnB,iBAAiB,gBAAiB,GAAWa,EAASG,EAAMC,WAAYD,EAAME,WAAYF,MAGhG7O,MAAM,QACJ4O,EAiBX,MAAMK,GAAc,CAAC,MAAO,SAAU,SAAU,aAAc,SACxDC,GAAe,CAAC,MAAO,MAAO,SAAU,SACxCC,GAAgB,IAAIxG,IAC1B,SAASyG,GAAUrR,EAAQK,GACvB,GAAML,aAAkBgP,eAClB3O,KAAQL,IACM,iBAATK,EAFX,CAKA,GAAI+Q,GAAc3J,IAAIpH,GAClB,OAAO+Q,GAAc3J,IAAIpH,GAC7B,MAAMiR,EAAiBjR,EAAKP,QAAQ,aAAc,IAC5CyR,EAAWlR,IAASiR,EACpBE,EAAUL,GAAa1M,SAAS6M,GACtC,GAEEA,KAAmBC,EAAWvB,SAAWD,gBAAgB1X,YACrDmZ,GAAWN,GAAYzM,SAAS6M,IAHtC,CAMA,IAAM1L,EAAS6L,eAAgBC,KAAcnG,GAEzC,IAAMiE,EAAK5W,KAAKqW,YAAYyC,EAAWF,EAAU,YAAc,YAC/D,IAAIxR,EAASwP,EAAGmC,MAQhB,OAPIJ,IACAvR,EAASA,EAAO4R,MAAMrG,EAAKsG,iBAMjBhY,QAAQ0P,IAAI,CACtBvJ,EAAOsR,MAAmB/F,GAC1BiG,GAAWhC,EAAGjV,QACd,IAGR,OADA6W,GAAcpJ,IAAI3H,EAAMuF,GACjBA,ICiCP4I,GD/BwB,IAAf,EC+BgBA,GD7BzB/G,IAAK,CAACzH,EAAQK,EAAMoO,IAAa4C,GAAUrR,EAAQK,IAASyR,EAASrK,IAAIzH,EAAQK,EAAMoO,GACvF3G,IAAK,CAAC9H,EAAQK,MAAWgR,GAAUrR,EAAQK,IAASyR,EAAShK,IAAI9H,EAAQK,IElE7E,IAAA0R,IAIEA,GAAA1Z,UAAA2Z,sBAAA,WAIE,OAHkBpZ,KAAK+R,UAAUQ,eAI9B1B,IAAI,SAAAsB,GACH,GAqBgB,aAAfrC,OADDA,EApB6BqC,EAoBRtC,qBACpB,EAAAC,EAAWpB,MAjBV,OAAO,KAHP,IAmBFoB,EAnBQjF,EAAUsH,EAASzC,eACzB,MAAO,GAAA7L,OAAGgH,EAAQwO,QAAO,KAAAxV,OAAIgH,EAAQ+M,WAKxChH,OAAO,SAAA0I,GAAa,OAAAA,IACpBzT,KAAK,MAEXsT,IAnBC,SAAAA,GAA6BpH,GAAA/R,KAAS+R,UAATA,sCCPlBwH,GAAS,IAAI3F,EAAO,iBC+BpBjF,GAAqB,YAErB6K,KAAsBzQ,GAAA,IAChC0Q,IAAU,YACX1Q,2BAAiB,mBACjBA,0BAAiB,iBACjBA,iCAAuB,wBACvBA,0BAAgB,iBAChBA,iCAAsB,wBACtBA,qBAAY,YACZA,4BAAkB,mBAClBA,yBAAgB,YAChBA,6BAAmB,oBACnBA,gCAAsB,mBACtBA,0BAAiB,UACjBA,iCAAuB,iBACvBA,8BAAqB,WACrBA,qCAA2B,kBAC3BA,0BAAiB,WACjBA,iCAAuB,kBACvBA,4BAAmB,YACnBA,mCAAyB,mBACzBA,8BAAoB,UACpBA,qCAA0B,iBAC1BA,wBAAe,WACfA,+BAAqB,kBACrBA,0BAAiB,WACjBA,iCAAuB,kBACvBA,iCAAc,cACdA,GAAA,WAAW,UACXA,GAAY,SAAG,cACPA,IClDG2Q,GAAQ,IAAI1H,IAKZ2H,GAAc,IAAI3H,IAQlB4H,GAAc,IAAI5H,IAOf,SAAA6H,GACdC,EACAhK,GAEA,IACGgK,EAAwB/H,UAAUG,aAAapC,GAChD,MAAOtO,GACP+X,GAAOtG,MACL,aAAApP,OAAaiM,EAAU/I,KAA4C,yCAAAlD,OAAAiW,EAAI/S,MACvEvF,IASU,SAAAuY,GACdD,EACAhK,GAECgK,EAAwB/H,UAAUM,wBAAwBvC,GAUvD,SAAUkK,GACdlK,eAEMmK,EAAgBnK,EAAU/I,KAChC,GAAI6S,GAAY1K,IAAI+K,GAKlB,OAJAV,GAAOtG,MACL,sDAAApP,OAAsDoW,EAAa,OAG9D,EAGTL,GAAYxK,IAAI6K,EAAenK,OAG/B,IAAkB,IAAAI,EAAAlN,EAAA0W,GAAMhJ,UAAQP,EAAAD,EAAA3O,QAAE4O,EAAAxO,KAAAwO,EAAAD,EAAA3O,OAChCsY,GADY1J,EAAA9O,MAC0ByO,wGAGxC,IAAwB,IAAAsB,EAAApO,EAAA2W,GAAYjJ,UAAQwJ,EAAA9I,EAAA7P,QAAE2Y,EAAAvY,KAAAuY,EAAA9I,EAAA7P,OAC5CsY,GADkBK,EAAA7Y,MACgCyO,oGAGpD,OAAO,EAYO,SAAAqK,GACdL,EACA/S,GAEA,IAAMqT,EAAuBN,EAAwB/H,UAClDK,YAAY,aACZ1C,aAAa,CAAEE,UAAU,IAI5B,OAHIwK,GACGA,EAAoBC,mBAEnBP,EAAwB/H,UAAUK,YAAYrL,GA2BlD,SAAUuT,GACdjP,GAEA,YAAwC7D,IAAhC6D,EAAoBsE,QC1H9B,IAAM4K,KAAMxR,GAAA,IACV,UACE,6EAEFA,GAAA,gBAAyB,iCACzBA,GAAA,iBACE,kFACFA,GAAA,eAAwB,kDACxBA,GAAA,sBAA+B,uCAC/BA,GAAA,cACE,0EACFA,GAAA,wBACE,6EAEFA,GAAA,wBACE,wDACFA,GAAA,YACE,gFACFA,GAAA,WACE,qFACFA,GAAA,WACE,mFACFA,GAAA,cACE,sFACFA,GAAA,uCACE,0GACFA,GAAA,kCACE,gEAgBSyR,GAAgB,IAAI/P,EAC/B,MACA,WACA8P,ICrDFE,IA8BErb,OAAAyU,eAAI4G,GAA8Bhb,UAAA,iCAAA,CAAlCoP,IAAA,WAEE,OADA7O,KAAK0a,iBACE1a,KAAK2a,iCAGdvL,IAAA,SAAmC2E,GACjC/T,KAAK0a,iBACL1a,KAAK2a,gCAAkC5G,mCAGzC3U,OAAAyU,eAAI4G,GAAIhb,UAAA,OAAA,CAARoP,IAAA,WAEE,OADA7O,KAAK0a,iBACE1a,KAAK4a,uCAGdxb,OAAAyU,eAAI4G,GAAOhb,UAAA,UAAA,CAAXoP,IAAA,WAEE,OADA7O,KAAK0a,iBACE1a,KAAK6a,0CAGdzb,OAAAyU,eAAI4G,GAAMhb,UAAA,SAAA,CAAVoP,IAAA,WAEE,OADA7O,KAAK0a,iBACE1a,KAAK8a,yCAGd1b,OAAAyU,eAAI4G,GAAShb,UAAA,YAAA,CAAboP,IAAA,WACE,OAAO7O,KAAK+a,4CAGd3b,OAAAyU,eAAI4G,GAAShb,UAAA,YAAA,CAAboP,IAAA,WACE,OAAO7O,KAAKgb,YAGd5L,IAAA,SAAc2E,GACZ/T,KAAKgb,WAAajH,mCAOV0G,GAAAhb,UAAAib,eAAV,WACE,GAAI1a,KAAKib,UACP,MAAMT,GAActa,OAAM,cAAuB,CAAEuZ,QAASzZ,KAAK4a,SAGtEH,IA9DC,SAAAA,GACE9K,EACA3G,EACA+I,GAHF,IAcCjL,EAAA9G,KAjBSA,KAAUgb,YAAG,EAQrBhb,KAAK6a,SAAgB1a,EAAA,GAAAwP,GACrB3P,KAAK8a,QAAe3a,EAAA,GAAA6I,GACpBhJ,KAAK4a,MAAQ5R,EAAOjC,KACpB/G,KAAK2a,gCACH3R,EAAOkS,+BACTlb,KAAK+a,WAAahJ,EAClB/R,KAAK+R,UAAUG,aACb,IAAIpE,EAAU,MAAO,WAAM,OAAAhH,GAAI,WC1BrC,IAAAhD,GAAAqX,IACUvb,EAAeub,GADzBrX,GACU2W,IAyDRU,GAAA1b,UAAA2b,OAAA,aAIAhc,OAAAyU,eAAIsH,GAAQ1b,UAAA,WAAA,CAAZoP,IAAA,WACE,OAAO7O,KAAKqb,2CAKdF,GAAW1b,UAAA6b,YAAX,SAAYjQ,GACNrL,KAAKib,YAGTjb,KAAKqb,iBACO7T,IAAR6D,GAAoD,OAA/BrL,KAAKub,uBAC5Bvb,KAAKub,sBAAsBC,SAASnQ,EAAKrL,QAK7Cmb,GAAA1b,UAAAgc,YAAA,WACE,OAAIzb,KAAKib,UACA,IAEAjb,KAAKqb,WAMRF,GAAA1b,UAAAic,iBAAR,WACOC,GAAU3b,OAGjBZ,OAAAyU,eAAIsH,GAAQ1b,UAAA,WAAA,CAAZoP,IAAA,WAEE,OADA7O,KAAK0a,iBACE1a,KAAK4b,+CAOJT,GAAA1b,UAAAib,eAAV,WACE,GAAI1a,KAAKib,UACP,MAAMT,GAActa,OAAM,uBAG/Bib,IAnGC,SAAAA,GACExL,EACAkM,EACA9U,EACAgL,GAJF,IAgDCjL,EAAA9G,KAzCOkb,OAC4C1T,IAAhDqU,EAAaX,gCACTW,EAAaX,+BAIblS,EAAwC,CAC5CjC,KAAIA,EACJmU,+BAA8BA,UAK9BpU,OAF0CU,IAAvCmI,EAA4BmM,OAE/BhY,GAAAnE,KAAAK,KAAM2P,EAA4B3G,EAAQ+I,IAAW/R,KAGrD8D,GAAAnE,KAAAK,KADiC2P,EACnBA,QAAS3G,EAAQ+I,IAAW/R,MAIvC4b,cACHzb,EAAA,CAAA+a,+BAA8BA,GAC3BW,GAGL/U,EAAKyU,sBAAwB,KACO,oBAAzBQ,uBACTjV,EAAKyU,sBAAwB,IAAIQ,qBAAqB,WACpDjV,EAAK4U,sBAIT5U,EAAKuU,UAAY,EACjBvU,EAAKwU,YAAYxU,EAAK8U,cAAcI,gBAIpClV,EAAK8U,cAAcI,oBAAiBxU,EACpCqU,EAAaG,oBAAiBxU,EAE9ByU,GAAgBC,GAAatE,GAAS,eCpBnC,IAAMuE,aAoEG,SAAAC,GACdvB,EACAwB,WAEI1M,EAAUkL,EAOR7R,EAAM7I,EAAA,CACV4G,KAAM4H,GACNuM,gCAAgC,GALhCmB,EAFuB,iBAJzBA,OAAA,IAAAA,EAAc,GAIHA,GAEG,CAAEtV,KADDsV,GAOVA,GAECtV,EAAOiC,EAAOjC,KAEpB,GAAoB,iBAATA,IAAsBA,EAC/B,MAAMyT,GAActa,OAA8B,eAAA,CAChDuZ,QAAS3Z,OAAOiH,KAMpB,KAFA4I,EAAAA,GAAY7G,KAGV,MAAM0R,GAActa,OAAM,cAG5B,IAAMoc,EAAc5C,GAAM7K,IAAI9H,GAC9B,GAAIuV,EAAa,CAEf,GACEhR,EAAUqE,EAAS2M,EAAY3M,UAC/BrE,EAAUtC,EAAQsT,EAAYtT,QAE9B,OAAOsT,EAEP,MAAM9B,GAActa,OAA+B,gBAAA,CAAEuZ,QAAS1S,IAIlE,IAAMgL,EAAY,IAAIE,EAAmBlL,OACzC,IAAwB,IAAAiJ,EAAAhN,EAAA4W,GAAYlJ,UAAQR,EAAAF,EAAAzO,QAAE2O,EAAAvO,KAAAuO,EAAAF,EAAAzO,OAAA,CAAzC,IAAMuO,EAASI,EAAA7O,MAClB0Q,EAAUG,aAAapC,qGAGnByM,EAAS,IAAI9B,GAAgB9K,EAAS3G,EAAQ+I,GAIpD,OAFA2H,GAAMtK,IAAIrI,EAAMwV,GAETA,EA6LH,SAAgBZ,GAAU7B,mGAc1B,OAbA0C,GAAmB,EACjBzV,EAAO+S,EAAI/S,KACb2S,GAAMxK,IAAInI,IACZyV,GAAmB,EACnB9C,GAAMpJ,OAAOvJ,IACJ4S,GAAYzK,IAAInI,IACC+S,EACJ2B,eAAiB,IACrC9B,GAAYrJ,OAAOvJ,GACnByV,GAAmB,GAInBA,EACF,CAAA,EAAMvb,QAAQ0P,IACXmJ,EAAwB/H,UACtBQ,eACA1B,IAAI,SAAAsB,GAAY,OAAAA,EAAS7B,aAJZ,CAAA,EAAA,UAClBvH,EAAA5G,OAKC2X,EAAwBmB,WAAY,kCAYzB,SAAAgB,GACdQ,EACA7E,EACA8E,OAIIrD,EAAmD,QAAzCtQ,EAAAyQ,GAAoBiD,UAAqB,IAAA1T,EAAAA,EAAA0T,EACnDC,IACFrD,GAAW,IAAAxV,OAAI6Y,IAEjB,IAAMC,EAAkBtD,EAAQrR,MAAM,SAChC4U,EAAkBhF,EAAQ5P,MAAM,SACtC,GAAI2U,GAAmBC,EAAiB,CACtC,IAAMC,EAAU,CACd,+BAA+BhZ,OAAAwV,EAA0B,oBAAAxV,OAAA+T,EAAW,OAgBtE,OAdI+E,GACFE,EAAQ9Z,KACN,iBAAAc,OAAiBwV,EAAO,sDAGxBsD,GAAmBC,GACrBC,EAAQ9Z,KAAK,OAEX6Z,GACFC,EAAQ9Z,KACN,iBAAAc,OAAiB+T,EAAO,2DAG5B2B,GAAOjG,KAAKuJ,EAAQhX,KAAK,MAG3BmU,GACE,IAAIlM,EACF,GAAGjK,OAAAwV,EAAO,YACV,WAAM,MAAC,CAAEA,QAAOA,EAAEzB,QAAOA,IAE1B,YAWW,SAAAkF,GACdzI,EACA1E,GAEA,GAAoB,OAAhB0E,GAA+C,mBAAhBA,EACjC,MAAMmG,GAActa,OAAM,wBAE5BkU,EAAkBC,EAAa1E,GAY3B,SAAUqE,GAAYpB,GVpQtB,IAAsB4B,EAAAA,EUqQV5B,EVpQhBpC,EAAUuM,QAAQ,SAAAC,GAChBA,EAAKhJ,YAAYQ,KW/LrB,IAAMyI,GAAU,8BACVC,GAAa,EACbC,GAAa,2BASfC,GAAiD,KACrD,SAASC,KA2BP,OAzBED,GADGA,IACSzF,GAAcsF,GAASC,GAAY,CAC7CpF,QAAS,SAACO,EAAIF,GAMZ,GACO,IADCA,EAEJ,IACEE,EAAGiF,kBAAkBH,IACrB,MAAO3b,GAIP2G,QAAQmL,KAAK9R,OAIpB6H,MAAM,SAAA7H,GACP,MAAMgZ,GAActa,OAA0B,WAAA,CAC5Cqd,qBAAsB/b,EAAE4I,YA8BV,SAAAoT,GACpB1D,EACA2D,mGAGa,6BAAM,CAAA,EAAAJ,aAGjB,OAHMhF,EAAKtP,EAAoB5G,OACzByU,EAAKyB,EAAGhC,YAAY8G,GAAY,aAEhC,CAAA,EADcvG,EAAGZ,YAAYmH,IACjBO,IAAID,EAAiBE,GAAW7D,YAClD,OADA/Q,EAAA5G,OACM,CAAA,EAAAyU,EAAGjV,oBAAToH,EAAA5G,iDAEiBkI,EACfkP,GAAOjG,KAAKsK,EAAExT,UAERyT,EAAcrD,GAActa,OAA2B,UAAA,CAC3Dqd,qBAAuBK,MAAAA,OAAA,EAAAA,EAAaxT,UAEtCmP,GAAOjG,KAAKuK,EAAYzT,sCAK9B,SAASuT,GAAW7D,GAClB,MAAO,GAAAjW,OAAGiW,EAAI/S,KAAI,KAAAlD,OAAIiW,EAAInK,QAAQmO,OC3EpC,IAIAC,IAyCQA,GAAAte,UAAA4a,iBAAN,qHAUQ,6BARE2D,EAAiBhe,KAAK+R,UACzBK,YAAY,mBACZ1C,eAIGuO,EAAQD,EAAe5E,wBACvB8E,EAAOC,KAC4B,OAAd,QAAvBpV,EAAA/I,KAAKoe,wBAAkB,IAAArV,OAAA,EAAAA,EAAAsV,aAAkB,CAAA,EAAA,GACb,CAAA,GAA9BnO,EAAAlQ,MAAmCse,gCAEnC,GAFApO,EAAKkO,iBAAmBjO,EAAAhO,OAEiB,OAAd,UAAvBnC,KAAKoe,wBAAkB,IAAApO,OAAA,EAAAA,EAAAqO,YACzB,MAAO,CAAA,oBAKX,OACEre,KAAKoe,iBAAiBG,wBAA0BL,GAChDle,KAAKoe,iBAAiBC,WAAWlJ,KAC/B,SAAAqJ,GAAuB,OAAAA,EAAoBC,OAASP,IAG/C,CAAA,IAGPle,KAAKoe,iBAAiBC,WAAWtb,KAAK,CAAE0b,OAAMR,MAAKA,IAGrDje,KAAKoe,iBAAiBC,WACpBre,KAAKoe,iBAAiBC,WAAWzN,OAAO,SAAA4N,GACtC,IAAME,EAAc,IAAIpX,KAAKkX,EAAoBC,MAAME,UAEvD,OADYrX,KAAKuL,MACJ6L,GA9EuB,SAgFjC,CAAA,EAAA1e,KAAK4e,SAASC,UAAU7e,KAAKoe,6CAEpC7E,GAAOjG,KAAKwL,gCAWVf,GAAAte,UAAAsf,oBAAN,mHAEQ,6BAA0B,OAA1B/e,KAAKoe,iBAAyB,CAAA,EAAA,GAC1B,CAAA,EAAApe,KAAKse,gCAAXpO,EAAA/N,wBAGF,OACuC,OAAd,UAAvBnC,KAAKoe,wBAAkB,IAAArV,OAAA,EAAAA,EAAAsV,aACqB,IAA5Cre,KAAKoe,iBAAiBC,WAAW3d,OAEjC,CAAA,EAAO,KAEH+d,EAAON,KAEPnO,EAkCI,SACdgP,EACAC,gBAAA,IAAAA,IAAAA,EAjJuB,MAwJvB,IAAMC,EAA4C,GAE9CC,EAAgBH,EAAgBpb,YACpC,IAAkC,IAAAwb,EAAApc,EAAAgc,GAAeK,EAAAD,EAAA7d,QAAA8d,EAAA1d,KAAA0d,EAAAD,EAAA7d,oBAAtC,SAAAid,GAET,IAAMc,EAAiBJ,EAAiBK,KACtC,SAAAC,GAAM,OAAAA,EAAGvB,QAAUO,EAAoBP,QAEzC,GAAKqB,GAgBH,GAHAA,EAAeG,MAAM1c,KAAKyb,EAAoBC,MAG1CiB,GAAWR,GAAoBD,SACjCK,EAAeG,MAAM3c,mBAXvB,GAJAoc,EAAiBnc,KAAK,CACpBkb,MAAOO,EAAoBP,MAC3BwB,MAAO,CAACjB,EAAoBC,QAE1BiB,GAAWR,GAAoBD,SAGjCC,EAAiBpc,cAcrBqc,EAAgBA,EAAcvb,MAAM,GA5B3BmR,CAAmBsK,EAAAhe,8GA8B9B,MAAO,CACL6d,iBAAgBA,EAChBC,cAAaA,GA9EiCQ,CAC1C3f,KAAKoe,iBAAiBC,YADhBa,EAAgBlP,EAAAkP,iBAAEC,kBAGpBS,EAAe5Y,EACnBqB,KAAKuM,UAAU,CAAEgD,QAAS,EAAGyG,WAAYa,KAG3Clf,KAAKoe,iBAAiBG,sBAAwBE,EACnB,EAAvBU,EAAcze,QAEhBV,KAAKoe,iBAAiBC,WAAac,EAI7B,CAAA,EAAAnf,KAAK4e,SAASC,UAAU7e,KAAKoe,oBANT,CAAA,EAAA,kBAM1BlO,EAAA/N,oBAEAnC,KAAKoe,iBAAiBC,WAAa,GAE9Bre,KAAK4e,SAASC,UAAU7e,KAAKoe,4BAEpC,KAAA,EAAA,MAAA,CAAA,EAAOwB,UAGP,kBADArG,GAAOjG,KAAKsK,GACZ,CAAA,EAAO,2BAGZG,IA1GC,SAAAA,GAA6BhM,GAA7B,IAOCjL,EAAA9G,KAP4BA,KAAS+R,UAATA,EAT7B/R,KAAgBoe,iBAAiC,KAU/C,IAAMtE,EAAM9Z,KAAK+R,UAAUK,YAAY,OAAO1C,eAC9C1P,KAAK4e,SAAW,IAAIiB,GAAqB/F,GACzC9Z,KAAKse,wBAA0Bte,KAAK4e,SAASkB,OAAOle,KAAK,SAAAF,GAEvD,OADAoF,EAAKsX,iBAAmB1c,IAwG9B,SAASyc,KAGP,OAFc,IAAI7W,MAELwL,cAAciN,UAAU,EAAG,IAmD1C,IC5MuCrD,GD4MvCmD,IAKQA,GAAApgB,UAAAugB,6BAAN,4EACE,OAAKxW,IAGH,CAAA,EAAOE,IACJ9H,KAAK,WAAM,OAAA,IACXyH,MAAM,WAAM,OAAA,KAJf,CAAA,GAAO,QAULwW,GAAApgB,UAAAqgB,KAAN,kGAC0B,KAAA,EAAA,MAAM,CAAA,EAAA9f,KAAKigB,uCAAXlX,EAAkC5G,OACtC,CAAA,EAAA,GAClB,CAAA,EAAO,CAAEkc,WAAY,KAEM,KAAA,EAAA,MAAA,CAAA,EDxL3B,SACJvE,qGAGa,6BAAM,CAAA,EAAAuD,aAEF,OAFThF,EAAKtP,EAAoB5G,OAEhB,CAAA,GADTyU,EAAKyB,EAAGhC,YAAY8G,KACFnH,YAAYmH,IAAYtO,IAAI8O,GAAW7D,YAG/D,OAHMpY,EAASqH,EAAqD5G,OAG9D,CAAA,EAAAyU,EAAGjV,aACT,OADAoH,EAAA5G,OACA,CAAA,EAAOT,uCAEU2I,EACfkP,GAAOjG,KAAKwL,EAAE1U,UAERyT,EAAcrD,GAActa,OAAyB,UAAA,CACzDqd,qBAAuBuB,MAAAA,OAAA,EAAAA,EAAa1U,UAEtCmP,GAAOjG,KAAKuK,EAAYzT,sCCsKS8V,CAA4BlgB,KAAK8Z,aAClE,OAAIqG,OADEA,EAAqBpX,EAA2C5G,SAClEge,EAAoB9B,WACtB,CAAA,EAAO8B,GAEP,CAAA,EAAO,CAAE9B,WAAY,4BAKrBwB,GAASpgB,UAAAof,UAAf,SAAgBuB,gGACU,KAAA,EAAA,MAAM,CAAA,EAAApgB,KAAKigB,uCAAXjQ,EAAkC7N,OACtC,CAAA,EAAA,GACX,CAAA,GAE0B,KAAA,EAAA,MAAA,CAAA,EAAMnC,KAAK8f,eAC5C,OADMO,EAA2BrQ,EAAiB7N,OAClD,CAAA,EAAOqb,GAA2Bxd,KAAK8Z,IAAK,CAC1CyE,sBAEE,QADAxV,EAAAqX,EAAiB7B,6BACjB,IAAAxV,EAAAA,EAAAsX,EAAyB9B,sBAC3BF,WAAY+B,EAAiB/B,oBAK7BwB,GAAGpgB,UAAAgS,IAAT,SAAU2O,gGACgB,KAAA,EAAA,MAAM,CAAA,EAAApgB,KAAKigB,uCAAXjQ,EAAkC7N,OACtC,CAAA,EAAA,GACX,CAAA,GAE0B,KAAA,EAAA,MAAA,CAAA,EAAMnC,KAAK8f,eAC5C,OADMO,EAA2BrQ,EAAiB7N,OAClD,CAAA,EAAOqb,GAA2Bxd,KAAK8Z,IAAK,CAC1CyE,sBAEE,QADAxV,EAAAqX,EAAiB7B,6BACjB,IAAAxV,EAAAA,EAAAsX,EAAyB9B,sBAC3BF,WAAU9a,EAAAA,EAAA,GAAAJ,EACLkd,EAAyBhC,kBACzB+B,EAAiB/B,aACrB,YAIRwB,IA7DC,SAAAA,GAAmB/F,GAAA9Z,KAAG8Z,IAAHA,EACjB9Z,KAAKigB,wBAA0BjgB,KAAKggB,+BAmElC,SAAUN,GAAWV,GAEzB,OAAOhY,EAELqB,KAAKuM,UAAU,CAAEgD,QAAS,EAAGyG,WAAYW,KACzCte,OCvRmCgc,GCMhB,GDLrB1C,GACE,IAAIlM,EACF,kBACA,SAAAiE,GAAa,OAAA,IAAIoH,GAA0BpH,IAAU,YAIzDiI,GACE,IAAIlM,EACF,YACA,SAAAiE,GAAa,OAAA,IAAIgM,GAAqBhM,IAAU,YAMpDkK,GAAgBlV,GAAM6Q,GAAS8E,IAE/BT,GAAgBlV,GAAM6Q,GAAS,QAE/BqE,GAAgB,UAAW,sJPuIb,WACdrC,GAAY0G,+EAZR,SACJjV,GAEA,YAA+C7D,IAAvC6D,EAA0BkV,uDAjCpB,SACdzG,EACA/S,EACA0I,QAAA,IAAAA,IAAAA,EAA+Cd,IAE/CwL,GAAaL,EAAK/S,GAAMsJ,cAAcZ,uCIgMlC,SAAiB1I,GACrB,IAAM+S,EAAMJ,GAAM7K,IADG9H,OAAA,IAAAA,EAAiC4H,GAChC5H,GACtB,IAAK+S,GAAO/S,IAAS4H,IAAsB7F,IACzC,OAAOsT,KAET,IAAKtC,EACH,MAAMU,GAActa,OAAwB,SAAA,CAAEuZ,QAAS1S,IAGzD,OAAO+S,WAOO,WACd,OAAOva,MAAMkE,KAAKiW,GAAMhJ,gDAxHV,SACdmK,EACA2F,WAEA,IlBtJyB,oBAAX5Y,QAA0B0B,OkBsJpBA,IAElB,MAAMkR,GAActa,OAAM,uCAG4BsH,IAApDgZ,EAAiBtF,iCACnBsF,EAAiBtF,gCAAiC,GAWpD,IANEuF,EADEnG,GAAeO,GACJA,EAASlL,QAETkL,EAIT6F,EACDvgB,EAAAA,EAAA,GAAAqgB,GACAC,GAgBL,QAX+BjZ,IAA3BkZ,EAAQ1E,uBACH0E,EAAQ1E,oBAUuBxU,IAApCgZ,EAAiBxE,gBACiB,oBAAzBD,qBACT,MAAMvB,GAActa,OAElB,sCAAA,IAKN,IAAMygB,EAAa,GAfVpd,EAAI,GAAAJ,EAeoBkF,KAAKuM,UAAU8L,KAfhC,GAAAE,OACZ,SAACC,EAAM3c,GAAM,OAAC4c,KAAKC,KAAK,GAAIF,GAAQ3c,EAAEC,WAAW,GAAM,GACvD,GAcEmY,EAAc3C,GAAY9K,IAAI8R,GACpC,GAAIrE,EAIF,OAHCA,EAAsChB,YACrCkF,EAAiBxE,gBAEZM,EAGT,IAAMvK,EAAY,IAAIE,EAAmB0O,OACzC,IAAwB,IAAA3Q,EAAAhN,EAAA4W,GAAYlJ,UAAQR,EAAAF,EAAAzO,QAAE2O,EAAAvO,KAAAuO,EAAAF,EAAAzO,OAAA,CAAzC,IAAMuO,EAASI,EAAA7O,MAClB0Q,EAAUG,aAAapC,qGAYzB,OATMyM,EAAS,IAAIpB,GACjBsF,EACAD,EACAG,EACA5O,GAGF4H,GAAYvK,IAAIuR,EAAYpE,GAErBA,gEK/OT9B,IAgBErb,OAAAyU,eAAI4G,GAA8Bhb,UAAA,iCAAA,CAAlCoP,IAAA,WACE,OAAO7O,KAAKghB,UAAU9F,gCAGxB9L,IAAA,SAAmC2E,GACjC/T,KAAKghB,UAAU9F,+BAAiCnH,mCAGlD3U,OAAAyU,eAAI4G,GAAIhb,UAAA,OAAA,CAARoP,IAAA,WACE,OAAO7O,KAAKghB,UAAUja,sCAGxB3H,OAAAyU,eAAI4G,GAAOhb,UAAA,UAAA,CAAXoP,IAAA,WACE,OAAO7O,KAAKghB,UAAUrR,yCAGxB8K,GAAAhb,UAAA6Q,OAAA,WAAA,IAQCxJ,EAAA9G,KAPC,OAAO,IAAIiB,QAAc,SAAAC,GACvB4F,EAAKka,UAAUtG,iBACfxZ,MACCU,KAAK,WAEN,OADAkF,EAAKma,SAASnQ,SAASoQ,UAAUpa,EAAKC,MAC/B4U,GAAU7U,EAAKka,cAkB1BvG,GAAAhb,UAAA0hB,YAAA,SACEpa,EACA0I,cAAA,IAAAA,IAAAA,EAAgD2R,IAEhDphB,KAAKghB,UAAUtG,iBAGf,IAAMvI,EAAWnS,KAAKghB,UAAUjP,UAAUK,YAAYrL,GAStD,OAPGoL,EAAS9C,iBAEV,cADyB,QAAzBtG,EAAAoJ,EAAStC,sBAAgB,IAAA9G,OAAA,EAAAA,EAAAkF,oBAEzBkE,EAASjB,aAIJiB,EAASzC,aAAa,CAC3BZ,WAAYW,KAchBgL,GAAAhb,UAAA4hB,uBAAA,SACEta,EACA0I,QAAA,IAAAA,IAAAA,EAAgD2R,IAEhDphB,KAAKghB,UAAUjP,UAEZK,YAAYrL,GACZsJ,cAAcZ,IAOnBgL,GAAahb,UAAAoa,cAAb,SAAc/J,GACZ+J,GAAc7Z,KAAKghB,UAAWlR,IAGhC2K,GAAwBhb,UAAAsa,yBAAxB,SAAyBjK,GACvBiK,GAAyB/Z,KAAKghB,UAAWlR,IAG3C2K,GAAAhb,UAAA2b,OAAA,WACE,MAAO,CACLrU,KAAM/G,KAAK+G,KACXmU,+BAAgClb,KAAKkb,+BACrCvL,QAAS3P,KAAK2P,UAGnB8K,IAjHC,SACWA,GAAAuG,EACQC,GAFnB,IAWCna,EAAA9G,KAVUA,KAASghB,UAATA,EACQhhB,KAAQihB,SAARA,EAGjBpH,GACEmH,EACA,IAAIlT,EAAU,aAAc,WAAM,OAAAhH,GAAI,WAGxC9G,KAAK+R,UAAYiP,EAAUjP,UCtD/B,IAAMwI,KAAMxR,GAAA,IACV,UACE,oFAEFA,GAAA,wBACE,iFAMSyR,GAAgB,IAAI/P,EAC/B,aACA,WACA8P,ICCI,SAAU+G,GACdC,GAEA,IAAMC,EAAwC,GAKxCC,EAAgC,CAIpCC,YAAY,EACZtF,cA8DF,SACEzM,EACA0M,QAAA,IAAAA,IAAAA,EAAc,IAEd,IAAMvC,EAAM6H,GACVhS,EACA0M,GAGF,GAAIjR,EAASoW,EAAM1H,EAAI/S,MACrB,OAAOya,EAAK1H,EAAI/S,MAGlB,IAAM6a,EAAY,IAAIL,EAAgBzH,EAAK2H,GAE3C,OADAD,EAAK1H,EAAI/S,MAAQ6a,GA1EjB9H,IAAGA,EACHmC,gBAAiB4F,GACjB7N,YAAa8N,GACbhF,MAAOiF,GAEPP,KAAM,KACNrF,YAAa6F,GACblR,SAAU,CACRmR,kBA8EJ,SACEnS,GAEA,IAAMmK,EAAgBnK,EAAU/I,KAC1Bmb,EAA6BjI,EAAc/S,QAAQ,UAAW,IACpE,CAAA,IAMQib,EALNC,GAA+BtS,IACjB,WAAdA,EAAUpB,OAIJyT,EAAmB,SACvBE,GAGA,GAA2D,mBAH3DA,OAAA,IAAAA,EAAsBvI,IAGVuI,GAAeH,GAGzB,MAAM1H,GAActa,OAAsC,uBAAA,CACxDuZ,QAASQ,IAMb,OAAQoI,EAAeH,WAIM1a,IAA3BsI,EAAUxB,cACZnH,EAAWgb,EAAkBrS,EAAUxB,cAIxCmT,EAAkBS,GAA8BC,EAIhDZ,EAAgB9hB,UAAkByiB,GAIjC,WAAU,IAAA,IAAYvP,EAAA,GAAAhI,EAAA,EAAZA,EAAYlK,UAAAC,OAAZiK,IAAAgI,EAAYhI,GAAAlK,UAAAkK,GAEpB,OADmB3K,KAAKmhB,YAAY1U,KAAKzM,KAAMia,GAC7BtZ,MAChBX,KACA8P,EAAU3B,kBAAoBwE,EAAO,MAK7C,MAA8C,WAAvC7C,EAAUpB,KAEZ+S,EAAkBS,GACnB,MAnIFhB,UA4BJ,SAAmBna,UACVya,EAAKza,IA5BVub,aAuIJ,SAAsBxI,EAAkB/S,GACtC,MAAa,eAATA,EAIeA,EAHV,MAxIPwb,YAAWA,KAiCf,SAASzI,EAAI/S,GAEX,IAAKqE,EAASoW,EADdza,EAAOA,GAAQyb,IAEb,MAAMhI,GAActa,OAAwB,SAAA,CAAEuZ,QAAS1S,IAEzD,OAAOya,EAAKza,GA0Gd,OAjIC0a,EAA2B,QAAIA,EAGhCriB,OAAOyU,eAAe4N,EAAW,OAAQ,CACvC5S,IAmDF,WAEE,OAAOzP,OAAOqM,KAAK+V,GAAM3Q,IAAI,SAAA9J,GAAQ,OAAAya,EAAKza,QA9B5C+S,EAAS,IAAIyH,EAsGNE,EC7JF,IAAMR,GAvBG,SAAAwB,IACd,IAAMhB,EAAYH,GAA4B7G,IAmB9C,OAlBAgH,EAAU3Q,SACL3Q,EAAAA,EAAA,GAAAshB,EAAU3Q,UAAQ,CACrB2R,wBAAuBA,EACvBC,gBAWF,SAAyBrU,GACvBlH,EAAWsa,EAAWpT,IAXtBlC,gBAAeA,EACf1B,aAAYA,EACZtD,WAAUA,IAYLsa,EAGegB,GCjCXlJ,GAAS,IAAI3F,EAAO,wBCUjC,IACE,IAAM+O,GAAUjb,SAGkBF,IAA7Bmb,GAAgB1B,WACnB1H,GAAOjG,KAAK,0IAMNsP,GAAeD,GAAgB1B,SAClC9E,cAC6C,GAA9ByG,GAAWC,QAAQ,SACnCtJ,GAAOjG,KAAK,mOAMhB,MAAMvK,KAIF,IAAAkY,GAAW6B,GC9Bf7G,wCDgCF8G,GEnCA9B,GAAShF,qCAA+B,0DCD3B+G,GAAqB,IAErBC,GAAkB,KAAKpf,OAAA+T,IACvBsL,GAAwB,SAExBC,GACX,kDAEWC,GAA0B,KCEjCC,KAAqBta,GAAA,IACzB,6BACE,kDACFA,GAAA,kBAA4B,2CAC5BA,GAAA,0BAAoC,mCACpCA,GAAA,kBACE,6FACFA,GAAA,eAAyB,kDACzBA,GAAA,+BACE,+EAaSyR,GAAgB,IAAI/P,EDtBV,gBACK,gBCwB1B4Y,IAYI,SAAUC,GAAchgB,GAC5B,OACEA,aAAiB+G,GACjB/G,EAAMgH,KAAKuB,SAAQ,kBCtCjB,SAAU0X,GAAyBxa,GAAE,IAAAya,EAASza,EAAAya,UAClD,MAAO,GAAG3f,OAAAsf,GAAkC,cAAAtf,OAAA2f,oBAGxC,SAAUC,GACdC,GAEA,MAAO,CACLC,MAAOD,EAASC,MAChBC,cAAsC,EACtCC,WA8DuCC,EA9DMJ,EAASG,UAgEjDE,OAAOD,EAAkB5c,QAAQ,IAAK,SA/D3C8c,aAAc1c,KAAKuL,OAID,SAAAoR,GACpBC,EACAR,0FAEoC,KAAA,EAAA,MAAA,CAAA,EAAMA,EAASS,eAEnD,OAFMC,EAA8Brb,EAAqB5G,OACnDkiB,EAAYD,EAAa9gB,MACxB,CAAA,EAAAkX,GAActa,OAAiC,iBAAA,CACpDgkB,YAAWA,EACXI,WAAYD,EAAU/Z,KACtBia,cAAeF,EAAUja,QACzBoa,aAAcH,EAAUI,eAItB,SAAUC,GAAW3b,GAAE,IAAA+S,EAAM/S,EAAA+S,OACjC,OAAO,IAAI6I,QAAQ,CACjBC,eAAgB,mBAChBC,OAAQ,mBACRC,iBAAkBhJ,IAIN,SAAAiJ,GACdC,EACAjc,GAAE,IAAAkc,EAAYlc,EAAAkc,aAERC,EAAUR,GAAWM,GAE3B,OADAE,EAAQC,OAAO,iBAmCeF,EAnCyBA,EAoChD,GAAGphB,OAAAqf,GAAyB,KAAArf,OAAAohB,KAnC5BC,EAgBH,SAAgBE,GACpB1X,0FAEe,KAAA,EAAA,MAAM,CAAA,EAAAA,YAErB,OAAqB,MAFfhM,EAASqH,EAAU5G,QAEdsiB,QAAiB/iB,EAAO+iB,OAAS,IAEnC,CAAA,EAAA/W,KAGT,CAAA,EAAOhM,QClFH,SAAU2jB,GAAMC,GACpB,OAAO,IAAIrkB,QAAc,SAAAC,GACvBqkB,WAAWrkB,EAASokB,KCDjB,IAAME,GAAoB,oBACpBC,GAAc,GAMX,SAAAC,KACd,IAGE,IAAMC,EAAe,IAAIC,WAAW,KAElCje,KAAKke,QAAWle,KAAyCme,UACpDC,gBAAgBJ,GAGvBA,EAAa,GAAK,IAAcA,EAAa,GAAK,GAElD,IAAMK,ECrBJ,SAAgCC,GAEpC,OADYlgB,KAAKjG,OAAOyG,aAAY5F,MAAnBb,OAAMyD,EAAA,GAAAJ,EAAiB8iB,IAAK,KAClC/e,QAAQ,MAAO,KAAKA,QAAQ,MAAO,KD8B5Bgf,CAXGP,GAeJQ,OAAO,EAAG,IAbzB,OAAOX,GAAkBY,KAAKJ,GAAOA,EAAMP,GAC3C,MAAM1c,GAEN,OAAO0c,IEvBL,SAAUY,GAAOrB,GACrB,MAAO,GAAAnhB,OAAGmhB,EAAUvL,QAAO,KAAA5V,OAAImhB,EAAUlH,OCA3C,IAAMwI,GAA2D,IAAItU,IAMrD,SAAAuU,GAAWvB,EAAsBgB,GAC/C,IAAM/a,EAAMob,GAAOrB,GAEnBwB,GAAuBvb,EAAK+a,GAsD9B,SAA4B/a,EAAa+a,GACvC,IAAMS,EASR,YACOC,IAAoB,qBAAsB/e,QAC7C+e,GAAmB,IAAIC,iBAAiB,0BACvBC,UAAY,SAAAplB,GAC3BglB,GAAuBhlB,EAAEkJ,KAAKO,IAAKzJ,EAAEkJ,KAAKsb,OAG9C,OAAOU,GAhBSG,GACZJ,GACFA,EAAQK,YAAY,CAAE7b,IAAGA,EAAE+a,IAAGA,IAkBA,IAA5BM,GAAmBS,MAAcL,KACnCA,GAAiB1c,QACjB0c,GAAmB,MA5ErBM,CAAmB/b,EAAK+a,GA0C1B,SAASQ,GAAuBvb,EAAa+a,WACrCpU,EAAY0U,GAAmBzX,IAAI5D,GACzC,GAAK2G,MAIL,IAAuB,IAAAC,EAAA7O,EAAA4O,GAASE,EAAAD,EAAAtQ,QAAEuQ,EAAAnQ,KAAAmQ,EAAAD,EAAAtQ,QAChC4H,EADiB2I,EAAAzQ,OACR2kB,qGAYb,IAAIU,GAA4C,KCrEhD,IAEMO,GAAoB,+BAStB7J,GAA2D,KAC/D,SAASC,KAgBP,OAdED,GADGA,IACSzF,GAdM,kCACG,EAa+B,CAClDG,QAAS,SAACO,EAAIF,GAOL,IADCA,GAEJE,EAAGiF,kBAAkB2J,OAqBX,SAAA7X,GACpB4V,EACA3jB,uGAGW,OADL4J,EAAMob,GAAOrB,GACF,CAAA,EAAA3H,aAGC,OAHZhF,EAAKtP,EAAoB5G,OACzByU,EAAKyB,EAAGhC,YAAY4Q,GAAmB,aAE3B,CAAA,GADZjR,EAAcY,EAAGZ,YAAYiR,KACCpY,IAAI5D,WACxC,OADMic,EAAYne,EAAA5G,OACZ,CAAA,EAAA6T,EAAY0H,IAAIrc,EAAO4J,WAC7B,OADAlC,EAAA5G,OACM,CAAA,EAAAyU,EAAGjV,aAMT,OANAoH,EAAA5G,OAEK+kB,GAAYA,EAASlB,MAAQ3kB,EAAM2kB,KACtCO,GAAWvB,EAAW3jB,EAAM2kB,KAG9B,CAAA,EAAO3kB,QAIH,SAAgB8lB,GAAOnC,mGAEhB,OADL/Z,EAAMob,GAAOrB,GACF,CAAA,EAAA3H,aAEjB,OAFMhF,EAAKtP,EAAoB5G,OAEzB,CAAA,GADAyU,EAAKyB,EAAGhC,YAAY4Q,GAAmB,cACpCjR,YAAYiR,IAAmB3W,OAAOrF,WAC/C,OADAlC,EAAA5G,OACM,CAAA,EAAAyU,EAAGjV,oBAAToH,EAAA5G,gBASoB,SAAAilB,GACpBpC,EACAqC,yGAGW,OADLpc,EAAMob,GAAOrB,GACF,CAAA,EAAA3H,aAGgC,OAH3ChF,EAAKtP,EAAoB5G,OACzByU,EAAKyB,EAAGhC,YAAY4Q,GAAmB,aAEI,CAAA,GAD3ClO,EAAQnC,EAAGZ,YAAYiR,KACgCpY,IAC3D5D,WAIE,OALEic,EAA2Cne,EAAA5G,YAKhCqF,KAFXkQ,EAAW2P,EAASH,IAEA,CAAA,EAAA,GACxB,CAAA,EAAMnO,EAAMzI,OAAOrF,kBAAnBlC,EAAA5G,aAEA,KAAA,EAAA,MAAM,CAAA,EAAA4W,EAAM2E,IAAIhG,EAAUzM,WAA1BlC,EAAA5G,iBAEF,KAAA,EAAA,MAAM,CAAA,EAAAyU,EAAGjV,aAMT,OANAoH,EAAA5G,QAEIuV,GAAcwP,GAAYA,EAASlB,MAAQtO,EAASsO,KACtDO,GAAWvB,EAAWtN,EAASsO,KAGjC,CAAA,EAAOtO,QCjFH,SAAgB4P,GACpBC,8FAI0B,KAAA,EAAA,MAAA,CAAA,EAAMH,GAAOG,EAAcvC,UAAW,SAAAwC,GAC9D,IAAMC,EAgCDC,GAhCqDF,GA2Bf,CAC3CxB,IAAKN,KACLiC,mBAA6C,IA5BvCC,EAyCV,SACEL,EACAE,GAEA,CAAA,GAAwC,IAApCA,EAAkBE,mBAuBf,OAC+B,IAApCF,EAAkBE,mBAEX,CACLF,kBAAiBA,EACjBI,oBAmCN,SACEN,8FAM+B,KAAA,EAAA,MAAA,CAAA,EAAMO,GACnCP,EAAcvC,mBADZ+C,EAA2B/X,EAE9B7N,wBACM,OAAwB,IAAxB4lB,EAAMJ,mBAAgD,CAAA,EAAA,GAE3D,CAAA,EAAMtC,GAAM,aAEJ,OAFRrV,EAAA7N,OAEQ,CAAA,EAAM2lB,GAA0BP,EAAcvC,0BAAtD+C,EAAQ/X,sBAGN,OAAsD,IAAtD+X,EAAMJ,mBAAgD,CAAA,EAAA,GAGtD,CAAA,EAAML,GAAqBC,WAE7B,OAHMxe,EACJiH,EAAyC7N,OADnCslB,EAAiB1e,EAAA0e,mBAAEI,EAAmB9e,EAAA8e,qBAI5C,CAAA,EAAOA,GAGP,CAAA,EAAOJ,GAIX,KAAA,EAAA,MAAA,CAAA,EAAOM,QAjEkBC,CAAyBT,IAGzC,CAAEE,kBAAiBA,GA9B1B,IAAKQ,UAAUC,OAAQ,CAErB,IAAMC,EAA+BlnB,QAAQE,OAC3CqZ,GAActa,OAA6B,gBAE7C,MAAO,CACLunB,kBAAiBA,EACjBI,oBAAqBM,GAKzB,IAAMC,EAA+C,CACnDpC,IAAKyB,EAAkBzB,IACvB2B,mBAA6C,EAC7CU,iBAAkB/gB,KAAKuL,OAEnBgV,EAkBV,SACEN,EACAE,mGAGsC,6BAAA,CAAA,ECxGlB,SACpB1e,EACAiH,OADEgV,EAASjc,EAAAic,UAAEsD,EAAwBvf,EAAAuf,yBACnCtC,EAAGhW,EAAAgW,4GAUD,OAREuC,EAAWhF,GAAyByB,GAEpCE,EAAUR,GAAWM,IAGrBwD,EAAmBF,EAAyB5Y,aAAa,CAC7DE,UAAU,KAGe,CAAA,EAAM4Y,EAAiBzJ,uBAD9B,CAAA,EAAA,WACZ0J,EAAmBvY,EAA4C/N,SAEnE+iB,EAAQC,OAAO,oBAAqBsD,oBAiBvB,OAbX3mB,EAAO,CACXkkB,IAAGA,EACH0C,YAAaxF,GACbpF,MAAOkH,EAAUlH,MACjB8E,WAAYK,IAGR1L,EAAuB,CAC3BvK,OAAQ,OACRkY,QAAOA,EACPpjB,KAAMuG,KAAKuM,UAAU9S,IAGN,CAAA,EAAMsjB,GAAmB,WAAM,OAAAuD,MAAMJ,EAAUhR,oBAA1DmM,EAAWxT,EAAwD/N,QAC5DymB,GACuC,CAAA,EAAMlF,EAASS,QADpD,CAAA,EAAA,UAQb,OAPM0E,EAA4C3Y,EAAqB/N,OAOvE,CAAA,EANiE,CAC/D6jB,IAAK6C,EAAc7C,KAAOA,EAC1B2B,mBAA2C,EAC3C1C,aAAc4D,EAAc5D,aAC5B6D,UAAWrF,GAAiCoF,EAAcC,aAItD,KAAA,EAAA,MAAA,CAAA,EAAM7E,GAAqB,sBAAuBP,IAAxD,KAAA,EAAA,MAAMxT,cD6DoC6Y,CACxCxB,EACAE,WAEF,OAJMuB,EAA8BjgB,EAGnC5G,OACM,CAAA,EAAAiN,GAAImY,EAAcvC,UAAWgE,WAEhC,OAAA1F,gBAAgD,MAA5BxE,EAAEvU,WAAW+Z,WAGnC,CAAA,EAAM6C,GAAOI,EAAcvC,YAH0B,CAAA,EAAA,iBAGrDjc,EAAA5G,oBAGA,MAAA,CAAA,EAAMiN,GAAImY,EAAcvC,UAAW,CACjCgB,IAAKyB,EAAkBzB,IACvB2B,mBAA6C,YAF/C5e,EAAA5G,iBAKF,KAAA,EAAA,MAAM2c,wBAxCsBmK,CAC1B1B,EACAa,GAEF,MAAO,CAAEX,kBAAmBW,EAAiBP,oBAAmBA,IAnEvCqB,CACvB3B,EACAE,GAGF,OADAI,EAAsBD,EAAiBC,oBAChCD,EAAiBH,4BAGtB,OAVEA,EAAoBzX,EAQxB7N,QAEoB6jB,MAAQP,GAAW,CAAA,EAAA,SAEX,CAAA,EAAMoC,WAAlC,MAAS,CAAA,GAAA9e,EAAA0e,kBAAmBzX,EAAA7N,OAA6B4G,WAG3D,MAAO,CAAA,EAAA,CACL0e,kBAAiBA,EACjBI,oBAAmBA,SAsIvB,SAASC,GACP9C,GAEA,OAAOoC,GAAOpC,EAAW,SAAAwC,GACvB,IAAKA,EACH,MAAMhN,GAActa,OAAM,0BAE5B,OAAOwnB,GAAqBF,KAIhC,SAASE,GAAqBK,GAC5B,OAcoE,KAHpEN,EAXmCM,GAcfJ,oBAClBF,EAAkBY,iBAAmBrF,GAAqB1b,KAAKuL,MAdxD,CACLmT,IAAK+B,EAAM/B,IACX2B,mBAA6C,GAI1CI,EAGT,IACEN,EE5LoB,SAAA0B,GACpBpgB,EACA0e,OADEzC,EAASjc,EAAAic,UAAEsD,EAAwBvf,EAAAuf,iIAWjC,OAiCFtC,EAzCuDyB,EAyCpDzB,IAzCCuC,EA2CC,GAAA1kB,OAAG0f,GA3CoCyB,GA2CG,KAAAnhB,OAAAmiB,0BAzC3Cd,EAAUH,GAAmBC,EAAWyC,IAGxCe,EAAmBF,EAAyB5Y,aAAa,CAC7DE,UAAU,KAGe,CAAA,EAAM4Y,EAAiBzJ,uBAD9B,CAAA,EAAA,WACZ0J,EAAmBzY,EAA4C7N,SAEnE+iB,EAAQC,OAAO,oBAAqBsD,oBAiBvB,OAbX3mB,EAAO,CACXsnB,aAAc,CACZxG,WAAYK,GACZnF,MAAOkH,EAAUlH,QAIfvG,EAAuB,CAC3BvK,OAAQ,OACRkY,QAAOA,EACPpjB,KAAMuG,KAAKuM,UAAU9S,IAGN,CAAA,EAAMsjB,GAAmB,WAAM,OAAAuD,MAAMJ,EAAUhR,oBAA1DmM,EAAW1T,EAAwD7N,QAC5DymB,GACsC,CAAA,EAAMlF,EAASS,QADnD,CAAA,EAAA,UAIb,OAHM0E,EAA2C7Y,EAAqB7N,OAGtE,CAAA,EADEshB,GAAiCoF,IAG7B,KAAA,EAAA,MAAA,CAAA,EAAM5E,GAAqB,sBAAuBP,IAAxD,KAAA,EAAA,MAAM1T,SAIV,IAEIgW,MCvCkB,SAAAqD,GACpB9B,EACA+B,eAAA,IAAAA,IAAAA,GAAoB,uFAGN,KAAA,EAAA,MAAA,CAAA,EAAMlC,GAAOG,EAAcvC,UAAW,SAAAwC,GAClD,IAAK+B,GAAkB/B,GACrB,MAAMhN,GAActa,OAAM,kBAG5B,IAgIsB4oB,EAhIhBU,EAAehC,EAASsB,UAC9B,GAAKQ,GAiI8C,KAF7BR,EA/HgBU,GAiI5B5F,eAKd,SAA4BkF,GAC1B,IAAMjW,EAAMvL,KAAKuL,MACjB,OACEA,EAAMiW,EAAU9E,cAChB8E,EAAU9E,aAAe8E,EAAUjF,UAAYhR,EAAMuQ,GARpDqG,CAAmBX,GA/Hb,CAAA,GAA8B,IAA1BU,EAAa5F,cAGtB,OADA8F,EA0BN,SACEnC,EACA+B,4FAMY,KAAA,EAAA,MAAA,CAAA,EAAMK,GAAuBpC,EAAcvC,mBAAnD+C,EAAQhf,EAAqD5G,wBAC1D,OAA2D,IAA3D4lB,EAAMe,UAAUlF,cAA2C,CAAA,EAAA,GAEhE,CAAA,EAAMyB,GAAM,aAEJ,OAFRtc,EAAA5G,OAEQ,CAAA,EAAMwnB,GAAuBpC,EAAcvC,0BAAnD+C,EAAQhf,sBAIV,OAA2B,KADrB+f,EAAYf,EAAMe,WACVlF,cAEZ,CAAA,EAAOyF,GAAiB9B,EAAe+B,IAEvC,CAAA,EAAOR,QA/CUc,CAA0BrC,EAAe+B,GACjD9B,EAGP,IAAKS,UAAUC,OACb,MAAM1N,GAActa,OAAM,eAGtBkoB,GAmIVZ,EAnIgEA,EAqI1DqC,EAA2C,CAC/CjG,cAAwC,EACxCkG,YAAaxiB,KAAKuL,OAEpB1S,EAAAA,EAAA,GACKqnB,GAAQ,CACXsB,UAAWe,KAzIT,OADAH,EAsEN,SACEnC,EACAE,qGAGoB,6BAAA,CAAA,EAAM0B,GACtB5B,EACAE,WAMF,OARMqB,EAAY/f,EAGjB5G,OACK4nB,EACD5pB,EAAAA,EAAA,GAAAsnB,GAAiB,CACpBqB,UAASA,IAEL,CAAA,EAAA1Z,GAAImY,EAAcvC,UAAW+E,WACnC,OADAhhB,EAAA5G,OACA,CAAA,EAAO2mB,iBAGLxF,gBAC6B,MAA5BxE,EAAEvU,WAAW+Z,YAAkD,MAA5BxF,EAAEvU,WAAW+Z,WAAmB,CAAA,EAAA,GAIpE,CAAA,EAAM6C,GAAOI,EAAcvC,0BAA3Bjc,EAAA5G,oBAMA,OAJM4nB,EAAwB5pB,EAAAA,EAAA,GACzBsnB,GAAiB,CACpBqB,UAAW,CAAElF,cAAa,KAEtB,CAAA,EAAAxU,GAAImY,EAAcvC,UAAW+E,WAAnChhB,EAAA5G,iBAEF,KAAA,EAAA,MAAM2c,wBApGWkL,CAAyBzC,EAAea,GAChDA,EAbP,OAAOZ,YAiBO,OAzBZO,EAAQ/X,EAuBZ7N,OAEgBunB,EACd,CAAA,EAAMA,GADoB,CAAA,EAAA,iBAC1B3gB,EAAAiH,EAAA7N,oBACA4G,EAACgf,EAAMe,2BACX,MAAA,CAAA,EAD2C/f,QA2C7C,SAAS4gB,GACP3E,GAEA,OAAOoC,GAAOpC,EAAW,SAAAwC,GACvB,IAAK+B,GAAkB/B,GACrB,MAAMhN,GAActa,OAAM,kBAG5B,IAoFiC4oB,EApF3BU,EAAehC,EAASsB,UAC9B,OAqFqD,KAFpBA,EAnFDU,GAqFtB5F,eACVkF,EAAUgB,YAAc9G,GAAqB1b,KAAKuL,MApF3C1S,EAAAA,EAAA,GAAAqnB,GAAQ,CACXsB,UAAW,CAAElF,cAAa,KAIvB4D,IAsCX,SAAS+B,GACP9B,GAEA,YACwBjgB,IAAtBigB,GACgE,IAAhEA,EAAkBE,mBCjJA,SAAAsC,GACpB1C,EACA+B,eAAA,IAAAA,IAAAA,GAAoB,0FAGpB,MAAA,CAAA,EAQF,SACE/B,0FAEgC,KAAA,EAAA,MAAA,CAAA,EAAMD,GAAqBC,WAEvD,OAFIM,EAAwB9e,EAAyC5G,OAA9C0lB,qBAIzB,CAAA,EAAMA,GAFe,CAAA,EAAA,UAErB9e,EAAA5G,uCAfI+nB,CADAC,EAAoB5C,WAKR,OAJlBxe,EAAA5G,OAIkB,CAAA,EAAMknB,GAAiBc,EAAmBb,WAC5D,MAAO,CAAA,EADWvgB,EAAuD5G,OACxDwhB,YCYnB,SAASyG,GAAqBC,GAC5B,OAAO7P,GAActa,OAA4C,4BAAA,CAC/DmqB,UAASA,ICLsD,SAA7DC,GACJvY,GAEA,IAEMwV,EAAgBpN,GAFVpI,EAAUK,YAAY,OAAO1C,eAED6a,IAAoB7a,eAM5D,MAJ8D,CAC5D8a,MAAO,WAAM,OC5BX,SAAsBjD,qGAEyB,MAAA,CAAA,EAAMD,GADnD6C,EAAoB5C,WAa1B,OAZMxe,EAA6CiH,EAElD7N,OAFOslB,EAAiB1e,EAAA0e,mBAAqB1e,EAAA8e,qBAS5CwB,GAAiBc,IAJG9gB,MAAMlB,QAAQ7E,OAO7B,CAAA,EAAAmkB,EAAkBzB,UDcVwE,CAAMjD,IACnB0C,SAAU,SAACX,GAA2B,OAAAW,GAAS1C,EAAe+B,KA7BlE,IAAMiB,GAAqB,gBAmCzBvQ,GACE,IAAIlM,EAAUyc,GAjCsC,SACtDxY,GAEA,IAAM+H,EAAM/H,EAAUK,YAAY,OAAO1C,eAWzC,MANqD,CACnDoK,IAAGA,EACHkL,UDpBE,SAA2BlL,WAC/B,IAAKA,IAAQA,EAAInK,QACf,MAAMya,GAAqB,qBAG7B,IAAKtQ,EAAI/S,KACP,MAAMqjB,GAAqB,gBAU7B,IAAsB,IAAAK,EAAAznB,EAN2B,CAC/C,YACA,SACA,UAG8B0nB,EAAAD,EAAAlpB,QAAEmpB,EAAA/oB,KAAA+oB,EAAAD,EAAAlpB,OAAA,CAA7B,IAAMopB,EAAOD,EAAArpB,MAChB,IAAKyY,EAAInK,QAAQgb,GACf,MAAMP,GAAqBO,qGAI/B,MAAO,CACLlR,QAASK,EAAI/S,KACbyc,UAAW1J,EAAInK,QAAQ6T,UACvB1H,OAAQhC,EAAInK,QAAQmM,OACpBgC,MAAOhE,EAAInK,QAAQmO,OCXH8M,CAAiB9Q,GAMjCwO,yBAL+BnO,GAAaL,EAAK,aAMjD/I,QAAS,WAAM,OAAA9P,QAAQC,aAqB+C,WAExE8Y,GACE,IAAIlM,EAtC4B,yBAwC9Bwc,GAED,YExCLrO,GAAgBlV,GAAM6Q,IAEtBqE,GAAgBlV,GAAM6Q,GAAS,eCN3BiT,GACAC,yCCVS3O,GAAcvE,GAMdmT,GAAuB,wBAUvBC,GAA2B,+BAE3BC,GACX,qCAGWC,GAAe,cCFtB7H,KAAqBta,GAAA,IACzB,iBAAkC,yCAClCA,GAAA,iBAAkC,qCAClCA,GAAA,+BACE,mDACFA,GAAA,8BACE,kDACFA,GAAA,aAAuB,2BACvBA,GAAA,aAAuB,2BACvBA,GAAA,iBAA2B,+BAC3BA,GAAA,cAAwB,4BACxBA,GAAA,kBAA4B,sCAC5BA,GAAA,kBACE,4EACFA,GAAA,sBAAuB,wBACvBA,GAAA,0BACE,8CACFA,GAAA,2BACE,gDACFA,GAAA,8BACE,oDACFA,GAAA,+BACE,uEACFA,GAAA,uBACE,6PAgBSyR,GAAgB,IAAI/P,EDvCV,cCyCrBygB,GACA7H,IC9DW8H,GAAgB,IAAIvX,EAAOsX,IACxCC,GAAcvY,SAAWJ,EAASa,KHsBlC,IIxBI+X,GCAAC,GLwBJC,IA6BEA,GAAA7rB,UAAA8rB,OAAA,WAEE,OAAOvrB,KAAKwrB,eAAeC,KAAKC,MAAM,KAAK,IAG7CJ,GAAI7rB,UAAAksB,KAAJ,SAAK5kB,GACE/G,KAAK4rB,aAAgB5rB,KAAK4rB,YAAYD,MAG3C3rB,KAAK4rB,YAAYD,KAAK5kB,IAGxBukB,GAAA7rB,UAAAosB,QAAA,SAAQC,EAAqBC,EAAeC,GACrChsB,KAAK4rB,aAAgB5rB,KAAK4rB,YAAYC,SAG3C7rB,KAAK4rB,YAAYC,QAAQC,EAAaC,EAAOC,IAG/CV,GAAgB7rB,UAAAwsB,iBAAhB,SAAiBvd,GACf,OAAK1O,KAAK4rB,aAAgB5rB,KAAK4rB,YAAYK,iBAGpCjsB,KAAK4rB,YAAYK,iBAAiBvd,GAFhC,IAKX4c,GAAgB7rB,UAAAysB,iBAAhB,SAAiBnlB,GACf,OAAK/G,KAAK4rB,aAAgB5rB,KAAK4rB,YAAYM,iBAGpClsB,KAAK4rB,YAAYM,iBAAiBnlB,GAFhC,IAKXukB,GAAA7rB,UAAA0sB,cAAA,WAEE,OACEnsB,KAAK4rB,cACJ5rB,KAAK4rB,YAAYQ,YAAcpsB,KAAK4rB,YAAYS,OAAOC,kBAI5DhB,GAAA7rB,UAAA8sB,sBAAA,WACE,OAAK5D,OAAU1nB,SjDkHQ,oBAAdgnB,WAA8BA,UAAUuE,gBiD3G5ChjB,MACH2hB,GAActiB,KAAK,kDACZ,IARPsiB,GAActiB,KACZ,2GAEK,IAUXyiB,GAAA7rB,UAAAgtB,cAAA,SACEC,EACAvjB,GAEKnJ,KAAK2sB,qBAGO,IAAI3sB,KAAK2sB,oBAAoB,SAAAC,GAC5C,IAAoB,IAAAjiB,EAAA,EAAA5B,EAAA6jB,EAAKC,aAALliB,EAAA5B,EAAArI,OAAAiK,IAAmB,CAAlC,IAAMod,EAAKhf,EAAA4B,GAEdxB,EAAS4e,MAKJ+E,QAAQ,CAAEC,WAAY,CAACL,MAG3BpB,GAAA0B,YAAP,WAIE,OAFEnC,QADkBrjB,IAAhBqjB,GACY,IAAIS,GAAIR,IAEjBD,IAEVS,IAnGC,SAAAA,GAAqB1jB,GACnB,KADmB5H,KAAM4H,OAANA,GAEjB,MAAM4S,GAActa,OAAM,aAE5BF,KAAK4rB,YAAchkB,EAAOgkB,YAC1B5rB,KAAK2sB,oBAAsB/kB,EAAO+kB,oBAClC3sB,KAAKwrB,eAAiB5jB,EAAOqlB,SAC7BjtB,KAAKioB,UAAYrgB,EAAOqgB,UACxBjoB,KAAK+H,SAAWH,EAAOG,SACnB/H,KAAKioB,WAAajoB,KAAKioB,UAAUuE,gBAGnCxsB,KAAKktB,aAAetlB,EAAOslB,cAEzBtlB,EAAOulB,aAAevlB,EAAOulB,YAAYC,oBAC3CptB,KAAKotB,kBAAoBxlB,EAAOulB,YAAYC,mBMjDlC,SAAAC,GAAaC,EAAeC,GAC1C,IAAMC,EAAWF,EAAM5sB,OAAS6sB,EAAM7sB,OACtC,GAAI8sB,EAAW,GAAgB,EAAXA,EAClB,MAAMhT,GAActa,OAAM,+BAI5B,IADA,IAAMutB,EAAc,GACXltB,EAAI,EAAGA,EAAI+sB,EAAM5sB,OAAQH,IAChCktB,EAAY1qB,KAAKuqB,EAAM3mB,OAAOpG,IAC1BgtB,EAAM7sB,OAASH,GACjBktB,EAAY1qB,KAAKwqB,EAAM5mB,OAAOpG,IAIlC,OAAOktB,EAAY5nB,KAAK,IDZ1B,IEMY6nB,GAAAA,GFNZC,IAmCEA,GAAAluB,UAAAmuB,sBAAA,WACE,OAAO5tB,KAAK6tB,uBAAuBhqB,OAAO,QAAS7D,KAAK8tB,eAGnDH,GAAAX,YAAP,WAIE,OAFE3B,QAD8B7jB,IAA5B6jB,GACwB,IAAIsC,GAEzBtC,IAEVsC,IA7CD,SAAAA,KAEE3tB,KAAsB+tB,wBAAG,EAGzB/tB,KAAqBguB,uBAAG,EAGxBhuB,KAAciuB,gBAAG,EAEjBjuB,KAAkBkuB,mBAAG,EACrBluB,KAA2BmuB,4BAAG,EAG9BnuB,KAAcouB,eACZ,oEAGFpuB,KAAA6tB,uBAAyBR,GACvB,mCACA,mCAGFrtB,KAAA8tB,aAAeT,GAAa,uBAAwB,uBAGpDrtB,KAASquB,UAAG,IAGZruB,KAAqBsuB,uBAAG,EACxBtuB,KAAuBuuB,yBAAG,EAG1BvuB,KAAgBwuB,iBAAG,IE3BTd,GAAAA,GAAAA,IAIX,IAHCA,GAAA,QAAA,GAAA,UACAA,GAAAA,GAAA,QAAA,GAAA,UACAA,GAAAA,GAAA,OAAA,GAAA,SA0BF,IAAMe,GAA8B,CAAC,YAAa,UAAW,OACvDC,GAAyB,IAAIC,OAAO,kBAiB1B,SAAAC,KAGd,OAFiBtD,GAAI0B,cAAcjlB,SACF8mB,iBAE/B,IAAK,UACH,OAAOnB,GAAgBoB,QACzB,IAAK,SACH,OAAOpB,GAAgBqB,OACzB,QACE,OAAOrB,GAAgBsB,SC/DvB,SAAUC,GAASC,OACjBpR,EAA2B,QAAnB/U,EAAAmmB,EAAYvf,eAAO,IAAA5G,OAAA,EAAAA,EAAE+U,MACnC,IAAKA,EACH,MAAMtD,GAActa,OAAM,aAE5B,OAAO4d,ECMT,IAAMqR,GAA4B,QAa5BC,GAAmC,CACvCnB,gBAAgB,GAqBZoB,GAAkB,8BAER,SAAAC,GACdC,EACAnE,GAEA,IAyDAmE,EACAnE,EA1DMpiB,EAeR,WACE,IAAMkkB,EAAe5B,GAAI0B,cAAcE,aACvC,GAAKA,EAAL,CAGA,IAAMsC,EAAetC,EAAauC,QAAQxE,IAC1C,GAAKuE,GA4IP,SAAqBE,GACnB,OAAO3L,OAAO2L,GAAUpoB,KAAKuL,MA7IP8c,CAAYH,GAAlC,CAIMI,EAAoB1C,EAAauC,QAAQzE,IAC/C,GAAK4E,EAGL,IAEE,OAD6CvnB,KAAKC,MAAMsnB,GAExD,MAAM7mB,GACN,UAjCa8mB,GACf,OAAI7mB,GACF8mB,GAAc9mB,GACP/H,QAAQC,YAuDjBkqB,EApD8CA,ELxC1C,SACJ2E,GAEA,IAAMC,EAAmBD,EAAqB9F,WAK9C,OAHA+F,EAAiBpuB,KAAK,SAACquB,MAGhBD,EKuFAE,EAJPX,EAnDuBA,GAuD0BhI,eAC9C3lB,KAAK,SAAAknB,GACJ,IAAMtF,ED3GN,SAAuB0L,OACrB1L,EAA+B,QAAnBza,EAAAmmB,EAAYvf,eAAO,IAAA5G,OAAA,EAAAA,EAAEya,UACvC,IAAKA,EACH,MAAMhJ,GAActa,OAAM,iBAE5B,OAAOsjB,ECsGe2M,CAAaZ,EAAsBzV,KAC/CgC,EDpGN,SAAoBoT,OAClBpT,EAA4B,QAAnB/S,EAAAmmB,EAAYvf,eAAO,IAAA5G,OAAA,EAAAA,EAAE+S,OACpC,IAAKA,EACH,MAAMtB,GAActa,OAAM,cAE5B,OAAO4b,EC+FYsU,CAAUb,EAAsBzV,KACzCuW,EAAiB,2DAAAxsB,OAA2D2f,EAA2C,mCAAA3f,OAAAiY,GACvHvE,EAAU,IAAI+Y,QAAQD,EAAgB,CAC1CrjB,OAAQ,OACRkY,QAAS,CAAEqL,cAAe,UAAGlB,GAAe,KAAAxrB,OAAIilB,IAEhDhnB,KAAMuG,KAAKuM,UAAU,CACnB4b,gBAAiBpF,EACjBqF,sBAAuB3H,EACvB4H,OAAQzB,GAASM,EAAsBzV,KACvC6W,YAAaxU,GACbyU,YAAazB,OAIjB,OAAOxG,MAAMpR,GAAS3V,KAAK,SAAA8hB,GACzB,GAAIA,EAASkF,GACX,OAAOlF,EAASS,OAGlB,MAAM3J,GAActa,OAAM,0BAG7BmJ,MAAM,WACL8hB,GAActiB,KAAKgoB,MAjFpBjvB,KAAKkuB,IACLluB,KACC,SAAAoH,GA4BN,IACQkkB,EADalkB,EA5BOA,EA6BpBkkB,EAAe5B,GAAI0B,cAAcE,aAClClkB,GAAWkkB,IAIhBA,EAAa4D,QAAQ9F,GAA0B3iB,KAAKuM,UAAU5L,IAC9DkkB,EAAa4D,QACX7F,GACAnrB,OACEwH,KAAKuL,MAC8C,GAAjD8a,GAAgBX,cAAcwB,iBAAwB,GAAK,QArC7D,eA0CN,IAAMqC,GACJ,mDA4CF,SAASf,GACP9mB,GAEA,IAAKA,EACH,OAAOA,EAET,IAAMqiB,EAA0BsC,GAAgBX,cAC1C/c,EAAUjH,EAAOiH,SAAW,GAqDlC,YApD4BzI,IAAxByI,EAAQ8gB,YAGV1F,EAAwB4C,eACU,SAAhCnuB,OAAOmQ,EAAQ8gB,aAIjB1F,EAAwB4C,eAAiBmB,GAAgBnB,eAEvDhe,EAAQ+gB,eACV3F,EAAwBgD,UAAYtK,OAAO9T,EAAQ+gB,gBAC1C5B,GAAgBf,YACzBhD,EAAwBgD,UAAYe,GAAgBf,WAGlDpe,EAAQghB,qBACV5F,EAAwB+C,eAAiBne,EAAQghB,qBACxC7B,GAAgBhB,iBACzB/C,EAAwB+C,eAAiBgB,GAAgBhB,gBAIvDne,EAAQihB,sBACV7F,EAAwByC,aAAe7d,EAAQihB,sBACtC9B,GAAgBtB,eACzBzC,EAAwByC,aAAesB,GAAgBtB,mBAGJtmB,IAAjDyI,EAAQkhB,qCACV9F,EAAwB8C,4BAA8BpK,OACpD9T,EAAQkhB,2CAE+C3pB,IAAhD4nB,GAAgBjB,8BACzB9C,EAAwB8C,4BACtBiB,GAAgBjB,kCAEuB3mB,IAAvCyI,EAAQmhB,2BACV/F,EAAwB6C,mBAAqBnK,OAC3C9T,EAAQmhB,iCAEsC5pB,IAAvC4nB,GAAgBlB,qBACzB7C,EAAwB6C,mBACtBkB,GAAgBlB,oBAGpB7C,EAAwBiD,sBAAwB+C,GAC9ChG,EAAwB6C,oBAE1B7C,EAAwBkD,wBAA0B8C,GAChDhG,EAAwB8C,6BAEnBnlB,EAOT,SAASqoB,GAAuBC,GAC9B,OAAOxQ,KAAKyQ,UAAYD,EClN1B,IAEIE,GAFAC,GAA2D,EAIzD,SAAUC,GACdnC,GAcF,IACEA,EARA,OALAkC,GAAkE,EAElED,GACEA,KAUFjC,EAV0CA,EAyB5C,WACE,IAAMxnB,EAAWujB,GAAI0B,cAAcjlB,SACnC,OAAO,IAAI9G,QAAQ,SAAAC,GACjB,IACQywB,EADJ5pB,GAAoC,aAAxBA,EAAS6pB,YACjBD,EAAU,WACc,aAAxB5pB,EAAS6pB,aACX7pB,EAASiP,oBAAoB,mBAAoB2a,GACjDzwB,MAGJ6G,EAASmP,iBAAiB,mBAAoBya,IAE9CzwB,MAzBG2wB,GACJjwB,KAAK,WAAM,ON5BdmuB,EM4B4BR,EAAsBhI,eN1B5CuK,EAAa/B,EAAqBvF,SAE7B5oB,KAAK,SAACmwB,GACf3G,GAAM2G,IAEDD,EARH,IACJ/B,EAEM+B,IM2BHlwB,KAAK,SAAAwpB,GAAO,OAAAkE,GAAUC,EAAuBnE,KAC7CxpB,KACOowB,GACAA,KAyBZ,SAASA,KACPP,GAAwD,EC5D1D,ICkEIlY,GDlEE0Y,GAA2B,IAG3BC,GAA0B,EAC1BC,GAA8B,IAChCC,GAAiBF,GA+BjBG,GAAsB,GAEtBC,IAA4B,EAiBhC,SAASC,GAAaC,GACpBjN,WAAW,WAeb,IAIQkN,EAIAC,EArBJ,GAAuB,IAAnBN,GAKJ,OAAKC,GAAM3xB,QAYP+xB,EAASJ,GAAMM,OAAO,EAAGR,IAIzBO,EAAmBD,EAAO5hB,IAAI,SAAA+hB,GAAO,MAAC,CAC1CC,6BAA8BD,EAAIxoB,QAClC0oB,cAAehzB,OAAO8yB,EAAIG,mBAwB9B,SACEroB,EACA+nB,GAEA,OAiCF,SAA0B/nB,GACxB,IAAMsoB,EACJrF,GAAgBX,cAAcY,wBAChC,OAAOjF,MAAMqK,EAAoB,CAC/BhmB,OAAQ,OACRlL,KAAMuG,KAAKuM,UAAUlK,KAtChBuoB,CAAiBvoB,GACrB9I,KAAK,SAAAsxB,GAIJ,OAHKA,EAAItK,IACPuC,GAActiB,KAAK,oCAEdqqB,EAAI/O,SAEZviB,KAAK,SAAAsxB,GAEJ,IAAMC,EAAgBpP,OAAOmP,EAAIE,uBAC7BC,EAAgBpB,GACfqB,MAAMH,KACTE,EAAgBvS,KAAKyS,IAAIJ,EAAeE,IAKpCG,EAA2CN,EAAIM,mBAEnDj0B,MAAM0F,QAAQuuB,IACc,EAA5BA,EAAmB9yB,QACsB,wBAAzC8yB,EAAmB,GAAGC,iBAEtBpB,GAAY9uB,EAAAA,EAAA,GAAAkvB,GAAW,GAAAJ,OACvBlH,GAActiB,KAAK,mCAGrBupB,GAAiBF,GAEjBK,GAAac,KA3CjBK,CAXsC,CACpCC,gBAAiB7zB,OAAOwH,KAAKuL,OAC7B+gB,YAAa,CACXC,YAAa,EACbC,eAAgB,IAElBC,WAAYpG,GAAgBX,cAAcqB,UAC1CqE,UAASA,GAIUD,GAAQppB,MAAM,WAGjCgpB,GAAY9uB,EAAAA,EAAA,GAAAkvB,GAAW,GAAAJ,OACvBD,KACAjH,GAActiB,KAAK,eAAAhF,OAAeuuB,GAAc,MAChDG,GAAaN,OArCJM,GAAaN,KAIrBO,YA4FWwB,GAEdC,GAEA,OAAO,WAAC,IAAA,IAAOthB,EAAA,GAAAhI,EAAA,EAAPA,EAAOlK,UAAAC,OAAPiK,IAAAgI,EAAOhI,GAAAlK,UAAAkK,IAbjB,SAAoBioB,GAClB,IAAKA,EAAIG,YAAcH,EAAIxoB,QACzB,MAAMoQ,GAActa,OAAM,kBAG5BmyB,GAAY9uB,EAAAA,EAAA,GAAA8uB,IAAO,GAAA,CAAAO,OAUjBsB,CAAW,CACT9pB,QAFc6pB,EAActzB,WAAA,EAAAgS,GAG5BogB,UAAWzrB,KAAKuL,SCjGtB,SAASshB,GACPC,EACAC,IAGE9a,GADGA,IACMya,GAAiBC,KAErBG,EAAUC,GAGb,SAAUC,GAASC,GACvB,IAAMC,EAAkB7G,GAAgBX,eAEnCwH,EAAgBzG,wBAA0BwG,EAAME,SAIhDD,EAAgBxG,uBAA0BuG,EAAME,SAIhDnJ,GAAI0B,cAAcT,0BAKnBgI,EAAME,QAAU7F,OAAyBlB,GAAgBoB,UF1EI,IAA1D2C,GE+ELiD,GAAaH,GAIb7C,GAAyB6C,EAAMhF,uBAAuB3tB,KACpD,WAAM,OAAA8yB,GAAaH,IACnB,WAAM,OAAAG,GAAaH,OAKzB,SAASG,GAAaH,GACpB,IAIMC,GRxGCpJ,KQwGDoJ,EAAkB7G,GAAgBX,eAErBiB,gBAChBuG,EAAgBlG,uBAKnB/I,WAAW,WAAM,OAAA4O,GAAQI,EAA0B,IAAE,GAmCvD,SAASN,GACPG,EACAC,GAEA,OAAkD,IAA9CA,GAOEM,EAA6C,CACjDC,IAP+BR,EAOXQ,IACpBC,YAR+BT,EAQHU,YAAc,EAC1CC,mBAAoB,IACpBC,uBAV+BZ,EAUQa,qBACvCC,qBAX+Bd,EAWMe,YACrCC,8BAZ+BhB,EAYeiB,0BAC9CC,8BAb+BlB,EAaemB,2BAE1CC,EAA6B,CACjCC,iBAAkBC,GAhBatB,EAiBd7E,sBAAsBzV,KAEvC6b,uBAAwBhB,GAEnBtsB,KAAKuM,UAAU4gB,IAGxB,SAAwBjB,GACtB,IAAMqB,EAA2B,CAC/B7uB,KAAMwtB,EAAMxtB,KACZ8uB,QAAStB,EAAME,OACfS,qBAAsBX,EAAMY,YAC5BW,YAAavB,EAAMwB,YAGsB,IAAvC32B,OAAOqM,KAAK8oB,EAAMyB,UAAUt1B,SAC9Bk1B,EAAYI,SAAWzB,EAAMyB,UAE/B,IAAMC,EAAmB1B,EAAM2B,gBACc,IAAzC92B,OAAOqM,KAAKwqB,GAAkBv1B,SAChCk1B,EAAYO,kBAAoBF,GAG5BT,EAA2B,CAC/BC,iBAAkBC,GAAmBnB,EAAMhF,sBAAsBzV,KACjEsc,aAAcR,GAEhB,OAAOvtB,KAAKuM,UAAU4gB,GA1Cfa,CAAejC,GAGxB,IACQO,EAyCR,SAASe,GAAmBxG,GAC1B,MAAO,CACLoH,cAAerH,GAASC,GACxBsB,gBR1MKpF,GQ2MLmL,aAAc,CACZ3F,YAAazU,GACbqa,SAAUlL,GAAI0B,cAAczB,SAC5BkL,sBLlLAxO,OADEA,EAAYqD,GAAI0B,cAAc/E,YAChCA,EAAWyO,cACTzO,EAAUyO,cAAcC,WACY,EAEE,EAGH,EK4KrCC,iBAAkBhI,KAClBiI,0BL5JU,WACd,IACMC,EADYxL,GAAI0B,cAAc/E,UAC+B8O,WAGnE,OADED,GAAuBA,EAAoBE,eAE3C,IAAK,UACH,OAAkD,EACpD,IAAK,KACH,OAA6C,EAC/C,IAAK,KACH,OAA6C,EAC/C,IAAK,KACH,OAA6C,EAC/C,QACE,OAAuC,GK6IZC,IAE7BC,0BAA2B,GLxLf,IACRjP,EMrCR,IAEMkP,GAAa,CZEqB,MAEW,OAEL,Qaa9C,IAAAC,IA4CEA,GAAA33B,UAAA43B,MAAA,WACE,GAAc,IAAVr3B,KAAKs3B,MACP,MAAM9c,GAActa,OAAuC,gBAAA,CACzDq3B,UAAWv3B,KAAK+G,OAGpB/G,KAAKw3B,IAAI7L,KAAK3rB,KAAKy3B,gBACnBz3B,KAAKs3B,MAAK,GAOZF,GAAA33B,UAAAi4B,KAAA,WACE,GAAc,IAAV13B,KAAKs3B,MACP,MAAM9c,GAActa,OAAuC,gBAAA,CACzDq3B,UAAWv3B,KAAK+G,OAGpB/G,KAAKs3B,MAAK,EACVt3B,KAAKw3B,IAAI7L,KAAK3rB,KAAK23B,eACnB33B,KAAKw3B,IAAI3L,QACP7rB,KAAK43B,aACL53B,KAAKy3B,eACLz3B,KAAK23B,eAEP33B,KAAK63B,wBACLvD,GAASt0B,OAUXo3B,GAAA33B,UAAAq4B,OAAA,SACEC,EACAC,EACAroB,GAKA,GAAIooB,GAAa,EACf,MAAMvd,GAActa,OAA+C,8BAAA,CACjEq3B,UAAWv3B,KAAK+G,OAGpB,GAAIixB,GAAY,EACd,MAAMxd,GAActa,OAA6C,6BAAA,CAC/Dq3B,UAAWv3B,KAAK+G,OASpB,GALA/G,KAAK+1B,WAAajV,KAAKmX,MAAiB,IAAXD,GAC7Bh4B,KAAKm1B,YAAcrU,KAAKmX,MAAkB,IAAZF,GAC1BpoB,GAAWA,EAAQuoB,aACrBl4B,KAAKi2B,iBAAgB91B,EAAA,GAAQwP,EAAQuoB,aAEnCvoB,GAAWA,EAAQwoB,QACrB,IAAyB,IAA4BxtB,EAAA,EAA5B5B,EAAA3J,OAAOqM,KAAKkE,EAAQwoB,SAApBxtB,EAA4B5B,EAAArI,OAA5BiK,IAA8B,CAAlD,IAAMytB,EAAUrvB,EAAA4B,GACd2oB,MAAMvP,OAAOpU,EAAQwoB,QAAQC,OAChCp4B,KAAKg2B,SAASoC,GAActX,KAAKmX,MAC/BlU,OAAOpU,EAAQwoB,QAAQC,MAK/B9D,GAASt0B,OAUXo3B,GAAA33B,UAAA44B,gBAAA,SAAgBC,EAAiBC,QAAA,IAAAA,IAAAA,EAAgB,QAChB/wB,IAA3BxH,KAAKg2B,SAASsC,GAChBt4B,KAAKw4B,UAAUF,EAASC,GAExBv4B,KAAKw4B,UAAUF,EAASt4B,KAAKg2B,SAASsC,GAAWC,IAUrDnB,GAAA33B,UAAA+4B,UAAA,SAAUF,EAAiBC,GACzB,GDrJ8BxxB,ECqJRuxB,EDrJsBf,ECqJbv3B,KAAK+G,KDpJlB,IAAhBA,EAAKrG,QAboB,IAaJqG,EAAKrG,UAI3B62B,GACCA,EAAUkB,WZhB0B,UYiBR,EAA5BtB,GAAWtU,QAAQ9b,KACpBA,EAAK0xB,WAnBmB,KCmKvB,MAAMje,GAActa,OAA6C,6BAAA,CAC/Dw4B,iBAAkBJ,IDvIpB,IACEK,EAnB0B5xB,EAAcwwB,ECsJ1Cv3B,KAAKg2B,SAASsC,IDpIwBM,ECoIeL,MAAAA,EAAAA,EAAgB,GDnInEI,EAAyB7X,KAAKmX,MAAMW,IACrBA,GACnBzN,GAActiB,KACZ,6DAAAhF,OAA6D80B,EAAc,MAGxEA,IC0IPvB,GAAS33B,UAAAo5B,UAAT,SAAUP,GACR,OAAOt4B,KAAKg2B,SAASsC,IAAY,GAQnClB,GAAA33B,UAAAq5B,aAAA,SAAaC,EAAc13B,GACzB,IPxGuC0F,EAUC1F,EO8FlC23B,IPvGY,KADqBjyB,EOwGQgyB,GPvGxCr4B,QAjDuB,GAiDPqG,EAAKrG,WAGA+tB,GAA4BtZ,KAAK,SAAA8jB,GAC7D,OAAAlyB,EAAK0xB,WAAWQ,QAEiBlyB,EAAKiB,MAAM0mB,KOkGtCwK,EP9FgB,KADkB73B,EO+FSA,GP9FtCX,QAAgBW,EAAMX,QA1DF,IOyJ/B,GAAIs4B,GAAeE,EACjBl5B,KAAKi2B,iBAAiB8C,GAAQ13B,MADhC,CAKA,IAAK23B,EACH,MAAMxe,GAActa,OAAyC,yBAAA,CAC3Di5B,cAAeJ,IAGnB,IAAKG,EACH,MAAM1e,GAActa,OAA0C,0BAAA,CAC5Dk5B,eAAgB/3B,MAStB+1B,GAAY33B,UAAA45B,aAAZ,SAAaN,GACX,OAAO/4B,KAAKi2B,iBAAiB8C,IAG/B3B,GAAe33B,UAAA65B,gBAAf,SAAgBP,QACsBvxB,IAAhCxH,KAAKi2B,iBAAiB8C,WAGnB/4B,KAAKi2B,iBAAiB8C,IAG/B3B,GAAA33B,UAAAy2B,cAAA,WACE,OAAY/1B,EAAA,GAAAH,KAAKi2B,mBAGXmB,GAAY33B,UAAA85B,aAApB,SAAqBxB,GACnB/3B,KAAKm1B,YAAc4C,GAGbX,GAAW33B,UAAA+5B,YAAnB,SAAoBxB,GAClBh4B,KAAK+1B,WAAaiC,GAOZZ,GAAA33B,UAAAo4B,sBAAR,WACE,IAAM4B,EAAqBz5B,KAAKw3B,IAAItL,iBAAiBlsB,KAAK43B,cACpD8B,EAAmBD,GAAsBA,EAAmB,GAC9DC,IACF15B,KAAK+1B,WAAajV,KAAKmX,MAAkC,IAA5ByB,EAAiB1B,UAC9Ch4B,KAAKm1B,YAAcrU,KAAKmX,MACoC,KAAzDyB,EAAiB3B,UAAY/3B,KAAKw3B,IAAIrL,oBAWtCiL,GAAcuC,eAArB,SACEpK,EACAqK,EACAC,EACAC,GAEA,IAIMvF,EAJAwF,EAAQzO,GAAI0B,cAAczB,SAC3BwO,IAGCxF,EAAQ,IAAI6C,GAChB7H,EbrQoC,OasQPwK,GAC7B,GAEIC,EAAelZ,KAAKmX,MAA0C,IAApC3M,GAAI0B,cAAcb,iBAClDoI,EAAMgF,aAAaS,GAGfJ,GAAqBA,EAAkB,KACzCrF,EAAMiF,YAAY1Y,KAAKmX,MAAsC,IAAhC2B,EAAkB,GAAG5B,WAClDzD,EAAMiE,UACJ,iBACA1X,KAAKmX,MAA4C,IAAtC2B,EAAkB,GAAGK,iBAElC1F,EAAMiE,UACJ,2BACA1X,KAAKmX,MAAsD,IAAhD2B,EAAkB,GAAGM,2BAElC3F,EAAMiE,UACJ,eACA1X,KAAKmX,MAA0C,IAApC2B,EAAkB,GAAGO,gBAMhCN,KACIO,EAAaP,EAAata,KAC9B,SAAA8a,GAAe,MAJC,gBAIDA,EAAYtzB,SAEXqzB,EAAWrC,WAC3BxD,EAAMiE,UblS0B,MaoS9B1X,KAAKmX,MAA6B,IAAvBmC,EAAWrC,aAGpBuC,EAAuBT,EAAata,KACxC,SAAA8a,GAAe,MAZY,2BAYZA,EAAYtzB,SAEDuzB,EAAqBvC,WAC/CxD,EAAMiE,UbzSqC,Oa2SzC1X,KAAKmX,MAAuC,IAAjCqC,EAAqBvC,YAIhC+B,GACFvF,EAAMiE,Ub9SgC,OagTpC1X,KAAKmX,MAAwB,IAAlB6B,KAKjBxF,GAASC,KAGJ6C,GAAAmD,sBAAP,SACEhL,EACAzD,GAQAwI,GANc,IAAI8C,GAChB7H,EACAzD,GACA,EACAA,KAILsL,IAnSC,SAAAA,GACW7H,EACAxoB,EACA0tB,EACT+F,QADS,IAAA/F,IAAAA,GAAc,GAFdz0B,KAAqBuvB,sBAArBA,EACAvvB,KAAI+G,KAAJA,EACA/G,KAAMy0B,OAANA,EAtBHz0B,KAAAs3B,MAA6C,EAG7Ct3B,KAAgBi2B,iBAA8B,GACtDj2B,KAAQg2B,SAAsC,GACtCh2B,KAAAw3B,IAAMlM,GAAI0B,cACVhtB,KAAAy6B,SAAW3Z,KAAKmX,MAAsB,IAAhBnX,KAAKyQ,UAmB5BvxB,KAAKy0B,SACRz0B,KAAKy3B,eAAiB,GAAA5zB,ObpDW,sBaoDmB,KAAAA,OAAA7D,KAAKy6B,SAAY,KAAA52B,OAAA7D,KAAK+G,MAC1E/G,KAAK23B,cAAgB,GAAA9zB,ObnDW,qBamDkB,KAAAA,OAAA7D,KAAKy6B,SAAY,KAAA52B,OAAA7D,KAAK+G,MACxE/G,KAAK43B,aACH4C,GACA,GAAG32B,OAAAknB,GAAwB,KAAAlnB,OAAA7D,KAAKy6B,SAAQ,KAAA52B,OAAI7D,KAAK+G,MAE/CyzB,GAGFx6B,KAAK63B,yBChCG,SAAA6C,GACdnL,EACAxH,GAEA,IHgGgC4S,EAS1BC,EAIAC,EAZArG,EGjGAsG,EAAmB/S,EACpB+S,QAAuDtzB,IAAnCszB,EAAiBC,gBAGpC3O,EAAad,GAAI0B,cAAcb,gBAC/BgJ,EAAcrU,KAAKmX,MACqB,KAA3C6C,EAAiB/C,UAAY3L,IAE1BiJ,EAA4ByF,EAAiBC,cAC/Cja,KAAKmX,MAC6D,KAA/D6C,EAAiBC,cAAgBD,EAAiB/C,iBAErDvwB,EACE+tB,EAA4BzU,KAAKmX,MACyB,KAA7D6C,EAAiBE,YAAcF,EAAiB/C,YAI7C4C,EAAiC,CACrCpL,sBAAqBA,EACrBqF,IAHUkG,EAAiB/zB,MAAQ+zB,EAAiB/zB,KAAK2kB,MAAM,KAAK,GAIpEuJ,qBAAsB6F,EAAiBG,aACvC9F,YAAWA,EACXE,0BAAyBA,EACzBE,0BAAyBA,GHwEKoF,EGrEdA,GHsEZnG,EAAkB7G,GAAgBX,eAEnBe,yBAMf6M,EAAoBD,EAAe/F,IAInCiG,EAAiBrG,EAAgBpG,eAAe1C,MAAM,KAAK,GAC3DwP,EAAgB1G,EAAgB3G,uBAAuBnC,MAAM,KAAK,GAEtEkP,IAAsBC,GACtBD,IAAsBM,GAMrB1G,EAAgBvG,gBAChBuG,EAAgBjG,yBAKnBhJ,WAAW,WAAM,OAAA4O,GAAQwG,EAA4C,IAAE,KI3JzE,IAAMQ,GAAmB,IAEnB,SAAUC,GACd7L,GZQOnE,KYAP7F,WAAW,WAAM,OAkBnB,SAAwBgK,GACtB,IAAMiI,EAAMlM,GAAI0B,cACV4M,EAAoBpC,EAAIvL,iBAC5B,cAEI4N,EAAerC,EAAIvL,iBAAiB,SAG1C,CAAA,IAGMoP,EAHF7D,EAAIpK,mBAGFiO,EAAiB9V,WAAW,WAC9B6R,GAAMuC,eACJpK,EACAqK,EACAC,GAEFwB,OAAY7zB,GACX2zB,IACH3D,EAAIpK,kBAAkB,SAACpH,GACjBqV,IACFC,aAAaD,GACbjE,GAAMuC,eACJpK,EACAqK,EACAC,EACA7T,OAKNoR,GAAMuC,eACJpK,EACAqK,EACAC,IApDa0B,CAAehM,IAAwB,GACxDhK,WAAW,WAAM,OAInB,SACEgK,GAIA,IAFA,IAAMiI,EAAMlM,GAAI0B,cACVwO,EAAYhE,EAAIvL,iBAAiB,YAChBthB,EAAA,EAAA8wB,EAAAD,EAAA7wB,EAAS8wB,EAAA/6B,OAATiK,IAAW,CAA7B,IAAMypB,EAAQqH,EAAA9wB,GACjB+vB,GAA0BnL,EAAuB6E,GAEnDoD,EAAI/K,cAAc,WAAY,SAAA1E,GAC5B,OAAA2S,GAA0BnL,EAAuBxH,KAblC2T,CAAqBnM,IAAwB,GAC9DhK,WAAW,WAAM,OAuDnB,SACEgK,GAKA,IAHA,IAAMiI,EAAMlM,GAAI0B,cAEV2O,EAAWnE,EAAIvL,iBAAiB,WAChBthB,EAAA,EAAAixB,EAAAD,EAAAhxB,EAAQixB,EAAAl7B,OAARiK,IAAU,CAA3B,IAAMkhB,EAAO+P,EAAAjxB,GAChB4vB,GAAsBhL,EAAuB1D,GAG/C2L,EAAI/K,cAAc,UAAW,SAAA1E,GAC3B,OAAAwS,GAAsBhL,EAAuBxH,KAlE9B8T,CAAsBtM,IAAwB,IAsEjE,SAASgL,GACPhL,EACA1D,GAEA,IAAMC,EAAcD,EAAQ9kB,KAG1B+kB,EAAY/L,UAAU,EAAGgL,GAAqBrqB,UAC9CqqB,IAIFqM,GAAMmD,sBAAsBhL,EAAuBzD,GC3FrD,IAAAgQ,IAiBEA,GAAKr8B,UAAAs8B,MAAL,SAAMxb,GAAN,IAiCCzZ,EAAA9G,KAhCKA,KAAKg8B,mBAI+Bx0B,KAApC+Y,MAAAA,OAAQ,EAARA,EAAUyN,yBACZhuB,KAAKguB,sBAAwBzN,EAASyN,4BAECxmB,KAArC+Y,MAAAA,OAAQ,EAARA,EAAUwN,0BACZ/tB,KAAK+tB,uBAAyBxN,EAASwN,wBAGrCzC,GAAI0B,cAAcT,wBACpB7iB,IACG9H,KAAK,SAAAq6B,GACAA,INEP3J,KACHC,GAzC+B,MA0C/BD,IAAmB,GMFXZ,GAAyB5qB,GAAMlF,KAC7B,WAAM,OAAAw5B,GAAkBt0B,IACxB,WAAM,OAAAs0B,GAAkBt0B,KAE1BA,EAAKk1B,aAAc,KAGtB3yB,MAAM,SAAA/F,GACL6nB,GAActiB,KAAK,iDAA0CvF,MAGjE6nB,GAActiB,KACZ,uHAMNzJ,OAAAyU,eAAIioB,GAAsBr8B,UAAA,yBAAA,CAG1BoP,IAAA,WACE,OAAO8e,GAAgBX,cAAce,wBAJvC3e,IAAA,SAA2B2E,GACzB4Z,GAAgBX,cAAce,uBAAyBha,mCAMzD3U,OAAAyU,eAAIioB,GAAqBr8B,UAAA,wBAAA,CAGzBoP,IAAA,WACE,OAAO8e,GAAgBX,cAAcgB,uBAJvC5e,IAAA,SAA0B2E,GACxB4Z,GAAgBX,cAAcgB,sBAAwBja,mCAKzD+nB,IA9DC,SACWA,GAAAhiB,EACAyN,GADAvnB,KAAG8Z,IAAHA,EACA9Z,KAAaunB,cAAbA,EAJHvnB,KAAWg8B,aAAY,ECqBjC,IAAMrtB,GAAqB,YAqFzBqL,GACE,IAAIlM,EAAU,cAzB8B,SAC9CiE,EACAhJ,GAAW,IAAAwX,EAAQxX,EAAA4G,QAGbmK,EAAM/H,EAAUK,YAAY,OAAO1C,eACnC6X,EAAgBxV,EACnBK,YAAY,0BACZ1C,eAEH,GAAIoK,EAAI/S,OAAS4H,GACf,MAAM6L,GAActa,OAAM,kBAE5B,GAAsB,oBAAX0H,OACT,MAAM4S,GAActa,OAAM,alB8B5B4qB,GkB5BSljB,OACHs0B,EAAe,IAAIJ,GAAsBhiB,EAAKyN,GAGpD,OAFA2U,EAAaH,MAAMxb,GAEZ2b,GAKsD,WAE7DjgB,GAAgBlV,GAAM6Q,IAEtBqE,GAAgBlV,GAAM6Q,GAAS,QClHjC,IAAAukB,IAQE/8B,OAAAyU,eAAIsoB,GAAsB18B,UAAA,yBAAA,CAA1BoP,IAAA,WACE,OAAO7O,KAAKghB,UAAU+M,wBAGxB3e,IAAA,SAA2B2E,GACzB/T,KAAKghB,UAAU+M,uBAAyBha,mCAG1C3U,OAAAyU,eAAIsoB,GAAqB18B,UAAA,wBAAA,CAAzBoP,IAAA,WACE,OAAO7O,KAAKghB,UAAUgN,uBAGxB5e,IAAA,SAA0B2E,GACxB/T,KAAKghB,UAAUgN,sBAAwBja,mCAGzCooB,GAAK18B,UAAA80B,MAAL,SAAMgD,GACJ,ODqDF3L,ECrDe5rB,KAAKghB,UDwDpB4K,GErFA/gB,EFqFiC+gB,IEnFjB/gB,EAA+BmW,UACrCnW,EAA+BmW,UAEhCnW,EFiFF,IAAIusB,GAAMxL,ECzDc2L,GDoDjB,IACd3L,EElFA/gB,GD+BDsxB,IAxBC,SACSA,GAAAriB,EACEkH,GADFhhB,KAAG8Z,IAAHA,EACE9Z,KAASghB,UAATA,MEJsBob,GAYnC,SAASC,GACPtqB,GAEA,IAAM+H,EAAM/H,EAAUK,YAAY,cAAc1C,eAE1Ckc,EAAc7Z,EAAUK,YAAY,eAAe1C,eAEzD,OAAO,IAAIysB,GAAsBriB,EAAK8R,IAnBLwQ,GAsBTnb,IArBPnQ,SAASmR,kBACxB,IAAInU,EACF,qBACAuuB,GAAkB,WAKtBD,GAAiBngB,+DCfnBgF,GAAShF,qCAA+B"}